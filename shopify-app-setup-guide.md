# Shopify App Setup Guide - Adding New Stores

## Overview

This guide explains how to create a new Shopify app that connects to your existing Fly.io deployment to add another store to the Americans United Inc multistore application. Each store requires its own separate Shopify app for proper isolation and security.

## Prerequisites

Before starting, ensure you have:
- Access to the Shopify Partners Dashboard
- Your Fly.io app URL: `https://your-app-name.fly.dev`
- Admin access to the target Shopify store
- The store's `.myshopify.com` domain name

## Step 1: Create New Shopify App in Partners Dashboard

### 1.1 Access Partners Dashboard
1. Go to [partners.shopify.com](https://partners.shopify.com)
2. Log in with your Shopify Partners account
3. Navigate to **Apps** in the left sidebar
4. Click **Create app**

### 1.2 App Configuration
1. **App name**: `Americans United Inc - [Store Name]`
   - Example: `Americans United Inc - American Trigger Pullers`
2. **App type**: Select **Public app**
3. **App URL**: `https://your-app-name.fly.dev/app`
4. **Allowed redirection URL(s)**:
   ```
   https://your-app-name.fly.dev/auth/callback
   https://your-app-name.fly.dev/auth/shopify/callback
   ```

### 1.3 App Settings Configuration

#### Basic Information
- **App name**: Americans United Inc - [Store Name]
- **App URL**: `https://your-app-name.fly.dev/app`
- **Contact email**: `<EMAIL>`
- **Privacy policy URL**: `https://americansunitedinc.com/privacy`
- **Terms of service URL**: `https://americansunitedinc.com/terms`

#### App setup
- **Embedded app**: ✅ Enabled
- **App bridge version**: 4.0 (latest)

#### URLs
- **App URL**: `https://your-app-name.fly.dev/app`
- **Allowed redirection URLs**:
  ```
  https://your-app-name.fly.dev/auth/callback
  https://your-app-name.fly.dev/auth/shopify/callback
  ```

#### Webhooks
- **Webhook API version**: `2025-04` (latest stable)
- **Webhook endpoint URL**: `https://your-app-name.fly.dev/webhooks/app`

## Step 2: Configure App Permissions (Scopes)

### 2.1 Required Scopes
Navigate to **App setup** → **Configuration** and add these scopes:

#### Read Permissions
- `read_orders` - Access order data for processing
- `read_products` - Access product information
- `read_inventory` - Check inventory levels
- `read_customers` - Access customer data (if needed)
- `read_fulfillments` - Track fulfillment status

#### Write Permissions
- `write_orders` - Update order information (if needed)
- `write_fulfillments` - Create fulfillment records (if needed)

### 2.2 Scope Configuration
```json
{
  "scopes": [
    "read_orders",
    "read_products", 
    "read_inventory",
    "read_customers",
    "read_fulfillments",
    "write_orders",
    "write_fulfillments"
  ]
}
```

## Step 3: Set Up Webhooks

### 3.1 Required Webhooks
In **App setup** → **Webhooks**, configure these webhooks:

#### Mandatory Webhooks
1. **App uninstalled**
   - **Endpoint**: `https://your-app-name.fly.dev/webhooks/app`
   - **API version**: `2025-04`

2. **Customers data request** (GDPR)
   - **Endpoint**: `https://your-app-name.fly.dev/webhooks/app`
   - **API version**: `2025-04`

3. **Customers redact** (GDPR)
   - **Endpoint**: `https://your-app-name.fly.dev/webhooks/app`
   - **API version**: `2025-04`

4. **Shop redact** (GDPR)
   - **Endpoint**: `https://your-app-name.fly.dev/webhooks/app`
   - **API version**: `2025-04`

#### Business Logic Webhooks
5. **Orders created**
   - **Endpoint**: `https://your-app-name.fly.dev/webhooks/app`
   - **API version**: `2025-04`

6. **Orders updated**
   - **Endpoint**: `https://your-app-name.fly.dev/webhooks/app`
   - **API version**: `2025-04`

7. **Orders fulfilled**
   - **Endpoint**: `https://your-app-name.fly.dev/webhooks/app`
   - **API version**: `2025-04`

8. **Orders cancelled**
   - **Endpoint**: `https://your-app-name.fly.dev/webhooks/app`
   - **API version**: `2025-04`

### 3.2 Webhook Configuration Example
```json
{
  "webhook": {
    "topic": "orders/create",
    "address": "https://your-app-name.fly.dev/webhooks/app",
    "format": "json",
    "api_version": "2025-04"
  }
}
```

## Step 4: Configure Fly.io Environment Variables

### 4.1 Get App Credentials
From your Shopify app dashboard, copy:
- **Client ID** (API key)
- **Client secret** (API secret key)

### 4.2 Set Environment Variables
Use the Fly.io CLI to set environment variables for the new store:

```bash
# Replace 'store-domain' with the actual store domain (without .myshopify.com)
# Replace 'your-app-name' with your Fly.io app name

# Store-specific API credentials
flyctl secrets set SHOPIFY_API_KEY_STORE_DOMAIN="your_client_id" -a your-app-name
flyctl secrets set SHOPIFY_API_SECRET_STORE_DOMAIN="your_client_secret" -a your-app-name

# Update supported stores list
flyctl secrets set SUPPORTED_STORES="existing-store1.myshopify.com,existing-store2.myshopify.com,new-store.myshopify.com" -a your-app-name

# Enable multistore mode
flyctl secrets set MULTISTORE_ENABLED="true" -a your-app-name
```

### 4.3 Environment Variable Format
The environment variables follow this pattern:
- Store domain: `american-trigger-pullers.myshopify.com`
- Environment key: `AMERICAN_TRIGGER_PULLERS` (replace dots and dashes with underscores, uppercase)
- API key variable: `SHOPIFY_API_KEY_AMERICAN_TRIGGER_PULLERS`
- API secret variable: `SHOPIFY_API_SECRET_AMERICAN_TRIGGER_PULLERS`

### 4.4 Complete Environment Setup Example
```bash
# For store: american-trigger-pullers.myshopify.com
flyctl secrets set SHOPIFY_API_KEY_AMERICAN_TRIGGER_PULLERS="abc123def456" -a your-app-name
flyctl secrets set SHOPIFY_API_SECRET_AMERICAN_TRIGGER_PULLERS="xyz789uvw012" -a your-app-name

# For store: phaselineco.myshopify.com  
flyctl secrets set SHOPIFY_API_KEY_PHASELINECO="ghi345jkl678" -a your-app-name
flyctl secrets set SHOPIFY_API_SECRET_PHASELINECO="mno901pqr234" -a your-app-name

# Update supported stores
flyctl secrets set SUPPORTED_STORES="american-trigger-pullers.myshopify.com,phaselineco.myshopify.com" -a your-app-name
```

## Step 5: Test App Installation

### 5.1 Generate Installation URL
1. In Partners Dashboard, go to your app
2. Click **Test your app**
3. Select **Test on development store** or get the installation URL
4. Installation URL format: `https://your-store.myshopify.com/admin/oauth/install_custom_app?client_id=YOUR_CLIENT_ID`

### 5.2 Install on Target Store
1. Use the installation URL or install via Partners Dashboard
2. The store admin will see permission request screen
3. Click **Install app** to grant permissions
4. App should redirect to: `https://your-app-name.fly.dev/app`

### 5.3 Verify Installation
After installation, verify:
1. App appears in store's **Apps** section
2. App loads correctly in Shopify admin
3. Navigation menu shows appropriate options (admin vs client)
4. No console errors in browser developer tools

## Step 6: Configure Store-Specific Settings

### 6.1 Admin Store Configuration
If this is an admin store, update the admin shop environment variable:
```bash
flyctl secrets set ADMIN_SHOP="new-admin-store.myshopify.com" -a your-app-name
```

### 6.2 ShipStation Integration (if needed)
If the store needs ShipStation integration:
1. Access the app in the store admin
2. Navigate to **Setup ShipStation Integrations**
3. Map the store to appropriate ShipStation store ID
4. Test webhook delivery

### 6.3 Pricing Configuration
Set up pricing for the new store:
1. Access the app as admin
2. Navigate to pricing management
3. Configure category-specific pricing
4. Test invoice calculations

## Step 7: Monitoring and Troubleshooting

### 7.1 Check App Logs
Monitor app logs for the new store:
```bash
flyctl logs -a your-app-name
```

### 7.2 Common Issues and Solutions

#### Issue: "App not found" error
**Solution**: Verify environment variables are set correctly:
```bash
flyctl secrets list -a your-app-name
```

#### Issue: Authentication failures
**Solution**: Check API credentials and redirection URLs:
1. Verify client ID and secret in Partners Dashboard
2. Ensure redirection URLs match exactly
3. Check that SUPPORTED_STORES includes the new store

#### Issue: Webhooks not working
**Solution**: Verify webhook configuration:
1. Check webhook URLs in Partners Dashboard
2. Verify webhook endpoint is accessible
3. Test webhook delivery manually

### 7.3 Health Check
Verify the app is working:
```bash
curl https://your-app-name.fly.dev/health
curl https://your-app-name.fly.dev/express/health
```

## Step 8: Documentation and Handoff

### 8.1 Document New Store
Update internal documentation with:
- Store domain and admin contact
- App credentials (store securely)
- Special configuration requirements
- ShipStation store mapping (if applicable)

### 8.2 Provide Access Information
Give the store admin:
- App installation confirmation
- Basic usage instructions
- Support contact information
- Link to user documentation

## Security Considerations

### 8.1 Credential Management
- Store API credentials securely in Fly.io secrets
- Never commit credentials to version control
- Use different credentials for each store
- Regularly rotate API credentials

### 8.2 Access Control
- Each store has isolated access to their data
- Admin stores can see aggregated data
- Regular stores only see their own data
- Webhook validation prevents unauthorized access

### 8.3 Monitoring
- Monitor app logs for security issues
- Set up alerts for failed authentication attempts
- Regular security audits of app permissions
- Monitor webhook delivery for anomalies

## Next Steps

After successful setup:
1. **User Training**: Provide training to store administrators
2. **Monitoring Setup**: Configure alerts and monitoring
3. **Backup Verification**: Ensure data backup procedures include new store
4. **Performance Testing**: Test app performance with additional store load
5. **Documentation Updates**: Update operational procedures

## Support and Maintenance

### Regular Maintenance Tasks
- Monitor app performance and logs
- Update Shopify API versions as needed
- Review and update app permissions
- Test webhook delivery periodically
- Update app documentation

### Support Contacts
- **Technical Issues**: <EMAIL>
- **Shopify Partners Support**: partners.shopify.com/support
- **Fly.io Support**: fly.io/docs/about/support

This guide ensures consistent and secure setup of new Shopify apps for additional stores in your multistore application.
