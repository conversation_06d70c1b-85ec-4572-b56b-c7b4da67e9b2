{"name": "americans-united-inc", "private": true, "scripts": {"build": "remix vite:build", "dev": "shopify app dev", "dev-ngrok": "concurrently \"npm run start:express\" \"npm run dev:remix-port3001\"", "dev:remix-port3001": "shopify app dev --tunnel-url=https://satyr-up-vulture.ngrok-free.app:3001", "dev:proxy": "node proxy-server.js", "dev:remix-ngrok": "sleep 3 && PORT=3000 shopify app dev --tunnel-url=https://satyr-up-vulture.ngrok-free.app:3000", "dev-ngrok-old": "shopify app dev --tunnel-url=https://satyr-up-vulture.ngrok-free.app:3000", "config:link": "shopify app config link", "generate": "shopify app generate", "deploy": "shopify app deploy", "config:use": "shopify app config use", "env": "shopify app env", "start": "remix-serve ./build/server/index.js", "start:express": "EXPRESS_PORT=4000 node express-server/server.js", "start:both": "concurrently \"npm run start\" \"npm run start:express\"", "docker-start": "npm run setup && npm run start", "setup": "prisma generate && prisma migrate deploy", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "shopify": "shopify", "prisma": "prisma", "graphql-codegen": "graphql-codegen", "vite": "vite", "test": "jest", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:e2e": "jest --testPathPattern=e2e", "test:performance": "jest --testPathPattern=performance", "test:monitoring": "jest --testPathPattern=monitoring", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "test:express": "jest express-server/", "test:shared": "jest shared/", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "test-orders-dev": "node local-testing-scripts/submit-test-orders.js", "export-data": "node scripts/migration/export-sqlite-data.js", "setup-postgres": "docker-compose up -d && sleep 10 && npm run setup", "import-data": "node scripts/migration/import-data.js", "validate-migration": "node scripts/migration/validate-migration.js", "reset-migration": "node scripts/migration/reset-for-postgresql.js", "simple-reset": "node scripts/migration/simple-reset.js", "deploy:all": "node scripts/deploy-all-apps.js", "deploy:all:parallel": "node scripts/deploy-all-apps.js --parallel", "deploy:stylish-stitches": "shopify app deploy --config=stylish-stitches", "deploy:pld-app": "shopify app deploy --config=pld-app", "deploy:75th-rra": "shopify app deploy --config=75th-rra", "verify:configs": "node scripts/verify-configs.js", "start:production": "NODE_ENV=production remix-serve ./build/server/index.js", "start:express:production": "NODE_ENV=production node express-server/server.js", "start:both:production": "concurrently \"npm run start:production\" \"npm run start:express:production\"", "deploy:fly": "bash infrastructure/scripts/deploy.sh", "deploy:fly:skip-tests": "bash infrastructure/scripts/deploy.sh --skip-tests", "health-check": "bash infrastructure/scripts/health-check.sh"}, "type": "module", "engines": {"node": "^18.20 || ^20.10 || >=21.0.0"}, "dependencies": {"@prisma/client": "^6.2.1", "@remix-run/dev": "^2.16.1", "@remix-run/fs-routes": "^2.16.1", "@remix-run/node": "^2.16.1", "@remix-run/react": "^2.16.1", "@remix-run/serve": "^2.16.1", "@shopify/app-bridge-react": "^4.1.6", "@shopify/polaris": "^12.0.0", "@shopify/shopify-app-express": "^5.0.17", "@shopify/shopify-app-remix": "^3.7.0", "@shopify/shopify-app-session-storage-prisma": "^6.0.6", "@types/pg": "^8.15.2", "axios": "^1.8.4", "compression": "^1.8.0", "cors": "^2.8.5", "csv-parse": "^5.6.0", "csv-writer": "^1.6.0", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-slow-down": "^2.1.0", "helmet": "^8.1.0", "isbot": "^5.1.0", "node-cron": "^3.0.3", "pg": "^8.16.0", "prisma": "^6.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "remix-utils": "^8.7.0", "ts-node": "^10.9.2", "uuid": "^11.1.0", "vite-tsconfig-paths": "^5.0.1", "zod": "^3.25.32", "@remix-run/route-config": "^2.16.1"}, "devDependencies": {"@babel/core": "^7.27.3", "@babel/preset-env": "^7.27.2", "@remix-run/eslint-config": "^2.16.1", "@shopify/api-codegen-preset": "^1.1.1", "@types/eslint": "^9.6.1", "@types/jest": "^29.5.14", "@types/node": "^22.15.24", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "@typescript-eslint/parser": "^8.32.0", "babel-jest": "^30.0.0-beta.3", "concurrently": "^9.1.2", "eslint": "^8.42.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-deprecation": "^3.0.0", "jest": "^29.7.0", "prettier": "^3.2.4", "supertest": "^7.1.1", "typescript": "^5.8.3", "vite": "^6.2.2"}, "workspaces": ["extensions/*"], "trustedDependencies": ["@shopify/plugin-cloudflare"], "resolutions": {"@graphql-tools/url-loader": "8.0.16", "@graphql-codegen/client-preset": "4.7.0", "@graphql-codegen/typescript-operations": "4.5.0"}, "overrides": {"@graphql-tools/url-loader": "8.0.16", "@graphql-codegen/client-preset": "4.7.0", "@graphql-codegen/typescript-operations": "4.5.0"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}