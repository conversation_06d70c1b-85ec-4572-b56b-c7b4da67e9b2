/**
 * Migration script for the VariantFulfillmentService model
 * 
 * This script runs the Prisma migration to create the VariantFulfillmentService table
 * and then schedules the initial cache refresh job.
 */

import { PrismaClient } from '@prisma/client';
import { refreshAllShopsFulfillmentServiceCache } from '../app/jobs/refresh-fulfillment-service-cache';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Starting migration for VariantFulfillmentService model');
    
    // Run the migration
    console.log('Running Prisma migration...');
    await prisma.$executeRaw`PRAGMA foreign_keys=OFF;`;
    
    // Check if the table already exists
    const tableExists = await prisma.$queryRaw`
      SELECT name FROM sqlite_master WHERE type='table' AND name='VariantFulfillmentService';
    `;
    
    if (tableExists.length === 0) {
      // Create the table
      await prisma.$executeRaw`
        CREATE TABLE IF NOT EXISTS "VariantFulfillmentService" (
          "variantId" TEXT NOT NULL PRIMARY KEY,
          "fulfillmentService" TEXT NOT NULL,
          "lastUpdated" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
      `;
      
      // Create the index
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS "VariantFulfillmentService_lastUpdated_idx" ON "VariantFulfillmentService"("lastUpdated");
      `;
      
      console.log('VariantFulfillmentService table created successfully');
    } else {
      console.log('VariantFulfillmentService table already exists');
    }
    
    await prisma.$executeRaw`PRAGMA foreign_keys=ON;`;
    
    // Schedule the initial cache refresh job
    console.log('Scheduling initial cache refresh job...');
    
    // This would typically be done through a job scheduler like node-cron
    // For now, we'll just run it directly
    console.log('Running initial cache refresh job...');
    await refreshAllShopsFulfillmentServiceCache();
    
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => {
    console.log('Migration script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration script failed:', error);
    process.exit(1);
  });
