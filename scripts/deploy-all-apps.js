#!/usr/bin/env node

/**
 * Deploy All Shopify Apps Script
 *
 * This script deploys the Americans United Inc app to all configured stores
 * Works on Windows, macOS, and Linux
 */

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const PROJECT_ROOT = path.dirname(__dirname);
const LOG_DIR = path.join(PROJECT_ROOT, 'logs', 'deployments');
const TIMESTAMP = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);

// Load main environment variables from .env file
dotenv.config({ path: path.join(PROJECT_ROOT, '.env') });

// App configurations
const APPS = {
  'stylish-stitches': {
    config: 'stylish-stitches',
    store: 'stylish-stitches-jordin-kolman.myshopify.com',
    clientId: 'SHOPIFY_API_KEY_STYLISH_STITCHES_JORDIN_KOLMAN',
    apiSecret: 'SHOPIFY_API_SECRET_STYLISH_STITCHES_JORDIN_KOLMAN'
  },
  'pld-app': {
    config: 'pld-app',
    store: 'pld-app-development-store.myshopify.com',
    clientId: 'SHOPIFY_API_KEY_PLD_APP_DEVELOPMENT_STORE',
    apiSecret: 'SHOPIFY_API_SECRET_PLD_APP_DEVELOPMENT_STORE'
  },
  '75th-rra': {
    config: '75th-rra',
    store: '75th-rra-app-development-store.myshopify.com',
    clientId: 'SHOPIFY_API_KEY_75TH_RRA_APP_DEVELOPMENT_STORE',
    apiSecret: 'SHOPIFY_API_SECRET_75TH_RRA_APP_DEVELOPMENT_STORE'
  }
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Utility functions
function colorLog(color, prefix, message) {
  console.log(`${colors[color]}[${prefix}]${colors.reset} ${message}`);
}

function info(message) {
  colorLog('blue', 'INFO', message);
}

function success(message) {
  colorLog('green', 'SUCCESS', message);
}

function warning(message) {
  colorLog('yellow', 'WARNING', message);
}

function error(message) {
  colorLog('red', 'ERROR', message);
}

function printHeader() {
  console.log(`${colors.cyan}================================${colors.reset}`);
  console.log(`${colors.cyan}  Americans United Inc App Deploy${colors.reset}`);
  console.log(`${colors.cyan}================================${colors.reset}`);
  console.log('');
}

function getAppClientId(appConfig) {
  const clientId = process.env[appConfig.clientId];
  if (!clientId) {
    throw new Error(`Environment variable ${appConfig.clientId} not found`);
  }
  return clientId;
}

function getAppSecret(appConfig) {
  const secret = process.env[appConfig.apiSecret];
  if (!secret) {
    throw new Error(`Environment variable ${appConfig.apiSecret} not found`);
  }
  return secret;
}

async function checkPrerequisites() {
  info('Checking prerequisites...');

  try {
    // Check if Shopify CLI is installed
    await runCommand('shopify', ['version'], { stdio: 'pipe' });
  } catch (err) {
    error('Shopify CLI is not installed. Please install it first:');
    console.log('  npm install -g @shopify/cli @shopify/theme');
    process.exit(1);
  }

  // Check if we're in the right directory
  try {
    await fs.access(path.join(PROJECT_ROOT, 'package.json'));
  } catch (err) {
    error('Not in the correct project directory. Please run from the project root.');
    process.exit(1);
  }

  // Check if config files exist and environment variables are set
  for (const [appName, appConfig] of Object.entries(APPS)) {
    const configFile = path.join(PROJECT_ROOT, `shopify.app.${appConfig.config}.toml`);
    try {
      await fs.access(configFile);
    } catch (err) {
      error(`Config file not found: ${configFile}`);
      process.exit(1);
    }

    // Check environment variables
    try {
      getAppClientId(appConfig);
      getAppSecret(appConfig);
      info(`✓ Environment variables verified for ${appName}`);
    } catch (err) {
      error(`Environment variable missing for ${appName}: ${err.message}`);
      process.exit(1);
    }
  }

  // Create log directory
  await fs.mkdir(LOG_DIR, { recursive: true });

  success('Prerequisites check passed');
}

function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      cwd: PROJECT_ROOT,
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', reject);
  });
}

async function deployApp(appName, appConfig) {
  const configName = appConfig.config;
  const logFile = path.join(LOG_DIR, `${appName}_${TIMESTAMP}.log`);

  info(`Deploying ${appName} to ${appConfig.store}...`);
  info(`Config name: ${configName}`);
  info(`Working directory: ${process.cwd()}`);

  try {
    // Verify config file exists in root directory
    const configFile = path.join(PROJECT_ROOT, `shopify.app.${configName}.toml`);
    await fs.access(configFile);
    info(`✓ Config file exists: ${configFile}`);

    // Get the client ID for this app from environment variables
    const clientId = getAppClientId(appConfig);
    info(`Using client ID: ${clientId}`);

    // Create log file stream
    const logStream = await fs.open(logFile, 'w');

    // Set the SHOPIFY_API_KEY environment variable for this deployment
    const deployEnv = {
      ...process.env,
      SHOPIFY_API_KEY: clientId
    };

    // Use config name (Shopify CLI will look for shopify.app.{configName}.toml)
    await runCommand('shopify', ['app', 'deploy', '--config', configName, '--force'], {
      stdio: ['inherit', logStream.fd, logStream.fd],
      cwd: PROJECT_ROOT,
      env: deployEnv
    });

    await logStream.close();
    success(`✓ ${appName} deployed successfully`);
    return { success: true, appName, store: appConfig.store };
  } catch (err) {
    error(`✗ ${appName} deployment failed`);
    error(`Check log file: ${logFile}`);
    return { success: false, appName, store: appConfig.store, error: err.message };
  }
}

async function deployAllSequential() {
  info('Starting sequential deployment...');
  const results = [];

  for (const [appName, appConfig] of Object.entries(APPS)) {
    const result = await deployApp(appName, appConfig);
    results.push(result);
    console.log('');
  }

  return results;
}

async function deployAllParallel() {
  info('Starting parallel deployment...');

  const deploymentPromises = Object.entries(APPS).map(([appName, appConfig]) =>
    deployApp(appName, appConfig)
  );

  const results = await Promise.all(deploymentPromises);
  return results;
}

function printSummary(results) {
  console.log(`${colors.cyan}================================${colors.reset}`);
  console.log(`${colors.cyan}  Deployment Summary${colors.reset}`);
  console.log(`${colors.cyan}================================${colors.reset}`);

  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);

  if (successful.length > 0) {
    success(`Successful deployments (${successful.length}):`);
    successful.forEach(result => {
      console.log(`  ${colors.green}✓${colors.reset} ${result.appName} (${result.store})`);
    });
  }

  if (failed.length > 0) {
    console.log('');
    error(`Failed deployments (${failed.length}):`);
    failed.forEach(result => {
      console.log(`  ${colors.red}✗${colors.reset} ${result.appName} (${result.store})`);
    });
    console.log('');
    error(`Check log files in: ${LOG_DIR}`);
    return false;
  }

  console.log('');
  success('All deployments completed successfully!');
  return true;
}

function showHelp() {
  console.log('Usage: node scripts/deploy-all-apps.js [OPTIONS]');
  console.log('');
  console.log('Deploy Americans United Inc Shopify app to all configured stores');
  console.log('');
  console.log('Options:');
  console.log('  -p, --parallel     Deploy to all stores in parallel (faster)');
  console.log('  -s, --sequential   Deploy to stores one by one (default, safer)');
  console.log('  -h, --help         Show this help message');
  console.log('');
  console.log('Configured stores:');
  Object.entries(APPS).forEach(([, appConfig]) => {
    try {
      const clientId = getAppClientId(appConfig);
      console.log(`  - ${appConfig.store} (config: ${appConfig.config}, client: ${clientId})`);
    } catch (err) {
      console.log(`  - ${appConfig.store} (config: ${appConfig.config}, client: ${err.message})`);
    }
  });
}

async function main() {
  const args = process.argv.slice(2);
  let deploymentMode = 'sequential';

  // Parse command line arguments
  for (const arg of args) {
    switch (arg) {
      case '-p':
      case '--parallel':
        deploymentMode = 'parallel';
        break;
      case '-s':
      case '--sequential':
        deploymentMode = 'sequential';
        break;
      case '-h':
      case '--help':
        showHelp();
        process.exit(0);
        break;
      default:
        error(`Unknown option: ${arg}`);
        showHelp();
        process.exit(1);
    }
  }

  try {
    printHeader();
    await checkPrerequisites();

    info(`Deployment mode: ${deploymentMode}`);
    info(`Timestamp: ${TIMESTAMP}`);
    console.log('');

    let results;
    if (deploymentMode === 'parallel') {
      results = await deployAllParallel();
    } else {
      results = await deployAllSequential();
    }

    const success = printSummary(results);
    process.exit(success ? 0 : 1);

  } catch (err) {
    error(`Deployment script failed: ${err.message}`);
    process.exit(1);
  }
}

// Run the script
main().catch(err => {
  error(`Unexpected error: ${err.message}`);
  process.exit(1);
});
