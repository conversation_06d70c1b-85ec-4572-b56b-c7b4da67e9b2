-- Americans United Inc Database Initialization Script
-- This script sets up the PostgreSQL database for development

-- Create the main development database (already created by POSTGRES_DB env var)
-- CREATE DATABASE americans_united_dev;

-- Create test database for running tests
CREATE DATABASE americans_united_test;

-- Create application user with appropriate permissions
-- (dev_user is already created by POSTGRES_USER env var)

-- Grant all privileges on development database to dev_user
GRANT ALL PRIVILEGES ON DATABASE americans_united_dev TO dev_user;
GRANT ALL PRIVILEGES ON DATABASE americans_united_test TO dev_user;

-- Connect to development database to set up extensions
\c americans_united_dev;

-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Grant schema permissions
GRANT ALL ON SCHEMA public TO dev_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO dev_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO dev_user;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO dev_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO dev_user;

-- Connect to test database to set up extensions
\c americans_united_test;

-- Enable required PostgreSQL extensions for test database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Grant schema permissions for test database
GRANT ALL ON SCHEMA public TO dev_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO dev_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO dev_user;

-- Set default privileges for future objects in test database
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO dev_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO dev_user;

-- Switch back to development database
\c americans_united_dev;

-- Create indexes that will be useful for the application
-- These will be created by Prisma migrations, but we can prepare the database

-- Log successful initialization
DO $$
BEGIN
    RAISE NOTICE 'Americans United Inc database initialization completed successfully';
    RAISE NOTICE 'Development database: americans_united_dev';
    RAISE NOTICE 'Test database: americans_united_test';
    RAISE NOTICE 'Database user: dev_user';
    RAISE NOTICE 'Extensions enabled: uuid-ossp, pg_trgm, btree_gin';
END $$;
