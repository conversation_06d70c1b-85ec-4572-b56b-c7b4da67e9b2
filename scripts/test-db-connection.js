#!/usr/bin/env node

/**
 * Test Database Connection
 * 
 * Simple script to test database connectivity.
 */

import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🔍 Testing database connection...');
console.log('DATABASE_URL from env:', process.env.DATABASE_URL);

try {
  // Dynamic import to avoid module loading issues
  const { PrismaClient } = await import('@prisma/client');
  
  const prisma = new PrismaClient({
    log: ['error'],
  });
  
  console.log('✅ Prisma client created');
  
  // Test connection
  const result = await prisma.$queryRaw`SELECT 1 as test, 'PostgreSQL' as db_type`;
  console.log('✅ Database query successful:', result);
  
  // Test table existence
  const tables = await prisma.$queryRaw`
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    ORDER BY table_name
  `;
  
  console.log('📊 Tables in database:', tables.map(t => t.table_name));
  
  await prisma.$disconnect();
  console.log('✅ Database connection test completed successfully');
  
} catch (error) {
  console.error('❌ Database connection test failed:', error.message);
  process.exit(1);
}
