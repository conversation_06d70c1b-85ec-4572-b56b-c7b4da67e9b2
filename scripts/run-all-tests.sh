#!/bin/bash

# Comprehensive Test Runner for ATP Invoice Preview Application
# This script runs all test suites and generates a comprehensive report

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
TEST_RESULTS_DIR="$PROJECT_ROOT/test-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test configuration
UNIT_TESTS_PATTERN="tests/unit/**/*.test.js"
INTEGRATION_TESTS_PATTERN="tests/integration/**/*.test.js"
E2E_TESTS_PATTERN="tests/e2e/**/*.test.js"
PERFORMANCE_TESTS_PATTERN="tests/performance/**/*.test.js"
MONITORING_TESTS_PATTERN="tests/monitoring/**/*.test.js"

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

# Show usage information
show_usage() {
    cat << EOF
Usage: $0 [options]

Run comprehensive test suite for ATP Invoice Preview Application.

Options:
  --help, -h              Show this help message
  --unit, -u              Run only unit tests
  --integration, -i       Run only integration tests
  --e2e, -e               Run only end-to-end tests
  --performance, -p       Run only performance tests
  --monitoring, -m        Run only monitoring tests
  --coverage, -c          Generate coverage report
  --watch, -w             Run tests in watch mode
  --parallel              Run tests in parallel
  --verbose, -v           Verbose output
  --bail                  Stop on first test failure
  --timeout SECONDS       Set test timeout (default: 30)
  --output-dir DIR        Test results output directory

Examples:
  $0                      # Run all tests
  $0 --unit --coverage    # Run unit tests with coverage
  $0 --e2e --verbose      # Run E2E tests with verbose output
  $0 --parallel --bail    # Run all tests in parallel, stop on failure
EOF
}

# Initialize test environment
init_test_environment() {
    log "Initializing test environment..."
    
    # Create test results directory
    mkdir -p "$TEST_RESULTS_DIR"
    
    # Set test environment variables
    export NODE_ENV=test
    export CI=true
    export FORCE_COLOR=1
    
    # Check if required dependencies are installed
    if ! command -v npm &> /dev/null; then
        error "npm is required but not installed"
        exit 1
    fi
    
    # Install dependencies if needed
    if [ ! -d "$PROJECT_ROOT/node_modules" ]; then
        log "Installing dependencies..."
        cd "$PROJECT_ROOT"
        npm ci
    fi
    
    success "Test environment initialized"
}

# Run specific test suite
run_test_suite() {
    local suite_name="$1"
    local test_pattern="$2"
    local additional_args="${3:-}"
    
    log "Running $suite_name tests..."
    
    local start_time=$(date +%s)
    local output_file="$TEST_RESULTS_DIR/${suite_name}_${TIMESTAMP}.json"
    local log_file="$TEST_RESULTS_DIR/${suite_name}_${TIMESTAMP}.log"
    
    # Build Jest command
    local jest_cmd="npx jest"
    jest_cmd="$jest_cmd --testPathPattern='$test_pattern'"
    jest_cmd="$jest_cmd --json --outputFile='$output_file'"
    jest_cmd="$jest_cmd --verbose"
    
    if [ -n "$additional_args" ]; then
        jest_cmd="$jest_cmd $additional_args"
    fi
    
    # Run tests and capture output
    if eval "$jest_cmd" > "$log_file" 2>&1; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        success "$suite_name tests completed in ${duration}s"
        
        # Parse results
        if [ -f "$output_file" ]; then
            local num_tests=$(jq -r '.numTotalTests // 0' "$output_file")
            local num_passed=$(jq -r '.numPassedTests // 0' "$output_file")
            local num_failed=$(jq -r '.numFailedTests // 0' "$output_file")
            
            info "  Tests: $num_tests, Passed: $num_passed, Failed: $num_failed"
            
            if [ "$num_failed" -gt 0 ]; then
                warning "  Some $suite_name tests failed. Check $log_file for details."
                return 1
            fi
        fi
        
        return 0
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        error "$suite_name tests failed after ${duration}s"
        error "Check $log_file for details"
        return 1
    fi
}

# Generate coverage report
generate_coverage() {
    log "Generating coverage report..."
    
    local coverage_dir="$TEST_RESULTS_DIR/coverage"
    local coverage_cmd="npx jest --coverage --coverageDirectory='$coverage_dir'"
    
    if eval "$coverage_cmd"; then
        success "Coverage report generated in $coverage_dir"
        
        # Display coverage summary
        if [ -f "$coverage_dir/coverage-summary.json" ]; then
            info "Coverage Summary:"
            jq -r '.total | "  Lines: \(.lines.pct)%, Statements: \(.statements.pct)%, Functions: \(.functions.pct)%, Branches: \(.branches.pct)%"' "$coverage_dir/coverage-summary.json"
        fi
    else
        error "Failed to generate coverage report"
        return 1
    fi
}

# Run linting
run_linting() {
    log "Running ESLint..."
    
    local lint_output="$TEST_RESULTS_DIR/lint_${TIMESTAMP}.json"
    
    if npx eslint . --format json --output-file "$lint_output"; then
        success "Linting passed"
        return 0
    else
        warning "Linting issues found. Check $lint_output for details"
        
        # Show summary of issues
        if [ -f "$lint_output" ]; then
            local error_count=$(jq '[.[] | .errorCount] | add // 0' "$lint_output")
            local warning_count=$(jq '[.[] | .warningCount] | add // 0' "$lint_output")
            info "  Errors: $error_count, Warnings: $warning_count"
        fi
        
        return 1
    fi
}

# Run type checking
run_type_checking() {
    log "Running TypeScript type checking..."
    
    if npx tsc --noEmit; then
        success "Type checking passed"
        return 0
    else
        error "Type checking failed"
        return 1
    fi
}

# Generate test report
generate_test_report() {
    log "Generating comprehensive test report..."
    
    local report_file="$TEST_RESULTS_DIR/test_report_${TIMESTAMP}.html"
    local summary_file="$TEST_RESULTS_DIR/test_summary_${TIMESTAMP}.json"
    
    # Collect all test results
    local total_tests=0
    local total_passed=0
    local total_failed=0
    local total_duration=0
    
    # Process each test result file
    for result_file in "$TEST_RESULTS_DIR"/*_"$TIMESTAMP".json; do
        if [ -f "$result_file" ] && [[ "$result_file" != *"summary"* ]]; then
            local tests=$(jq -r '.numTotalTests // 0' "$result_file")
            local passed=$(jq -r '.numPassedTests // 0' "$result_file")
            local failed=$(jq -r '.numFailedTests // 0' "$result_file")
            
            total_tests=$((total_tests + tests))
            total_passed=$((total_passed + passed))
            total_failed=$((total_failed + failed))
        fi
    done
    
    # Create summary JSON
    cat > "$summary_file" << EOF
{
  "timestamp": "$TIMESTAMP",
  "totalTests": $total_tests,
  "totalPassed": $total_passed,
  "totalFailed": $total_failed,
  "successRate": $(echo "scale=2; $total_passed * 100 / $total_tests" | bc -l 2>/dev/null || echo "0"),
  "testSuites": {
    "unit": "$([ -f "$TEST_RESULTS_DIR/unit_${TIMESTAMP}.json" ] && echo "completed" || echo "skipped")",
    "integration": "$([ -f "$TEST_RESULTS_DIR/integration_${TIMESTAMP}.json" ] && echo "completed" || echo "skipped")",
    "e2e": "$([ -f "$TEST_RESULTS_DIR/e2e_${TIMESTAMP}.json" ] && echo "completed" || echo "skipped")",
    "performance": "$([ -f "$TEST_RESULTS_DIR/performance_${TIMESTAMP}.json" ] && echo "completed" || echo "skipped")",
    "monitoring": "$([ -f "$TEST_RESULTS_DIR/monitoring_${TIMESTAMP}.json" ] && echo "completed" || echo "skipped")"
  }
}
EOF
    
    # Generate HTML report
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>ATP Invoice Preview - Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #e8f4fd; padding: 15px; border-radius: 5px; text-align: center; }
        .success { background: #d4edda; }
        .warning { background: #fff3cd; }
        .error { background: #f8d7da; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>ATP Invoice Preview - Test Report</h1>
        <p>Generated: $(date)</p>
        <p>Timestamp: $TIMESTAMP</p>
    </div>
    
    <div class="summary">
        <div class="metric $([ $total_failed -eq 0 ] && echo "success" || echo "error")">
            <h3>Total Tests</h3>
            <p>$total_tests</p>
        </div>
        <div class="metric success">
            <h3>Passed</h3>
            <p>$total_passed</p>
        </div>
        <div class="metric $([ $total_failed -eq 0 ] && echo "success" || echo "error")">
            <h3>Failed</h3>
            <p>$total_failed</p>
        </div>
        <div class="metric">
            <h3>Success Rate</h3>
            <p>$(echo "scale=1; $total_passed * 100 / $total_tests" | bc -l 2>/dev/null || echo "0")%</p>
        </div>
    </div>
    
    <h2>Test Suites</h2>
    <table>
        <tr><th>Suite</th><th>Status</th><th>Tests</th><th>Passed</th><th>Failed</th></tr>
EOF
    
    # Add test suite rows to HTML report
    for suite in unit integration e2e performance monitoring; do
        local result_file="$TEST_RESULTS_DIR/${suite}_${TIMESTAMP}.json"
        if [ -f "$result_file" ]; then
            local tests=$(jq -r '.numTotalTests // 0' "$result_file")
            local passed=$(jq -r '.numPassedTests // 0' "$result_file")
            local failed=$(jq -r '.numFailedTests // 0' "$result_file")
            local status=$([ "$failed" -eq 0 ] && echo "PASS" || echo "FAIL")
            
            echo "        <tr><td>$suite</td><td>$status</td><td>$tests</td><td>$passed</td><td>$failed</td></tr>" >> "$report_file"
        else
            echo "        <tr><td>$suite</td><td>SKIPPED</td><td>-</td><td>-</td><td>-</td></tr>" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF
    </table>
    
    <h2>Files</h2>
    <ul>
        <li><a href="test_summary_${TIMESTAMP}.json">Test Summary (JSON)</a></li>
        <li><a href="coverage/index.html">Coverage Report</a></li>
EOF
    
    # Add links to log files
    for log_file in "$TEST_RESULTS_DIR"/*_"$TIMESTAMP".log; do
        if [ -f "$log_file" ]; then
            local basename=$(basename "$log_file")
            echo "        <li><a href=\"$basename\">$basename</a></li>" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF
    </ul>
</body>
</html>
EOF
    
    success "Test report generated: $report_file"
    info "Test summary: $summary_file"
}

# Main function
main() {
    local run_unit=false
    local run_integration=false
    local run_e2e=false
    local run_performance=false
    local run_monitoring=false
    local run_coverage=false
    local watch_mode=false
    local parallel=false
    local verbose=false
    local bail=false
    local timeout=30
    local output_dir=""
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_usage
                exit 0
                ;;
            --unit|-u)
                run_unit=true
                shift
                ;;
            --integration|-i)
                run_integration=true
                shift
                ;;
            --e2e|-e)
                run_e2e=true
                shift
                ;;
            --performance|-p)
                run_performance=true
                shift
                ;;
            --monitoring|-m)
                run_monitoring=true
                shift
                ;;
            --coverage|-c)
                run_coverage=true
                shift
                ;;
            --watch|-w)
                watch_mode=true
                shift
                ;;
            --parallel)
                parallel=true
                shift
                ;;
            --verbose|-v)
                verbose=true
                shift
                ;;
            --bail)
                bail=true
                shift
                ;;
            --timeout)
                timeout="$2"
                shift 2
                ;;
            --output-dir)
                output_dir="$2"
                shift 2
                ;;
            *)
                error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Set output directory if specified
    if [ -n "$output_dir" ]; then
        TEST_RESULTS_DIR="$output_dir"
    fi
    
    # If no specific test suite is selected, run all
    if [ "$run_unit" = false ] && [ "$run_integration" = false ] && [ "$run_e2e" = false ] && [ "$run_performance" = false ] && [ "$run_monitoring" = false ]; then
        run_unit=true
        run_integration=true
        run_e2e=true
        run_performance=true
        run_monitoring=true
    fi
    
    # Initialize test environment
    init_test_environment
    
    # Change to project root
    cd "$PROJECT_ROOT"
    
    # Build additional Jest arguments
    local jest_args=""
    if [ "$watch_mode" = true ]; then
        jest_args="$jest_args --watch"
    fi
    if [ "$parallel" = true ]; then
        jest_args="$jest_args --maxWorkers=4"
    fi
    if [ "$bail" = true ]; then
        jest_args="$jest_args --bail"
    fi
    if [ "$verbose" = true ]; then
        jest_args="$jest_args --verbose"
    fi
    jest_args="$jest_args --testTimeout=${timeout}000"
    
    # Run linting and type checking first
    local lint_passed=true
    local type_check_passed=true
    
    if ! run_linting; then
        lint_passed=false
    fi
    
    if ! run_type_checking; then
        type_check_passed=false
    fi
    
    # Run test suites
    local test_results=()
    
    if [ "$run_unit" = true ]; then
        if run_test_suite "unit" "$UNIT_TESTS_PATTERN" "$jest_args"; then
            test_results+=("unit:PASS")
        else
            test_results+=("unit:FAIL")
        fi
    fi
    
    if [ "$run_integration" = true ]; then
        if run_test_suite "integration" "$INTEGRATION_TESTS_PATTERN" "$jest_args"; then
            test_results+=("integration:PASS")
        else
            test_results+=("integration:FAIL")
        fi
    fi
    
    if [ "$run_e2e" = true ]; then
        if run_test_suite "e2e" "$E2E_TESTS_PATTERN" "$jest_args"; then
            test_results+=("e2e:PASS")
        else
            test_results+=("e2e:FAIL")
        fi
    fi
    
    if [ "$run_performance" = true ]; then
        if run_test_suite "performance" "$PERFORMANCE_TESTS_PATTERN" "$jest_args"; then
            test_results+=("performance:PASS")
        else
            test_results+=("performance:FAIL")
        fi
    fi
    
    if [ "$run_monitoring" = true ]; then
        if run_test_suite "monitoring" "$MONITORING_TESTS_PATTERN" "$jest_args"; then
            test_results+=("monitoring:PASS")
        else
            test_results+=("monitoring:FAIL")
        fi
    fi
    
    # Generate coverage report if requested
    if [ "$run_coverage" = true ]; then
        generate_coverage
    fi
    
    # Generate comprehensive report
    generate_test_report
    
    # Summary
    log "Test execution completed!"
    info "Results directory: $TEST_RESULTS_DIR"
    
    # Check overall results
    local failed_tests=0
    for result in "${test_results[@]}"; do
        local suite=$(echo "$result" | cut -d: -f1)
        local status=$(echo "$result" | cut -d: -f2)
        
        if [ "$status" = "PASS" ]; then
            success "$suite tests: PASSED"
        else
            error "$suite tests: FAILED"
            failed_tests=$((failed_tests + 1))
        fi
    done
    
    # Final status
    if [ "$failed_tests" -eq 0 ] && [ "$lint_passed" = true ] && [ "$type_check_passed" = true ]; then
        success "All tests and checks passed!"
        exit 0
    else
        error "Some tests or checks failed!"
        exit 1
    fi
}

# Run main function with all arguments
main "$@"
