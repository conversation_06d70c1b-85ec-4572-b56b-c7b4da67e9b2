#!/usr/bin/env node

/**
 * Verify Shopify App Configurations
 *
 * This script verifies that all Shopify app configurations are valid
 * and that the corresponding environment variables are set.
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PROJECT_ROOT = path.dirname(__dirname);
const CONFIGS_DIR = path.join(PROJECT_ROOT, 'shopify-configs');
const ENV_FILE = path.join(PROJECT_ROOT, '.env');

// Expected configurations
const EXPECTED_CONFIGS = {
  'stylish-stitches.shopify.app.toml': {
    clientId: '4f62408a57ff8202871bbff18316a1d6',
    store: 'stylish-stitches-jordin-kolman.myshopify.com',
    envKey: 'SHOPIFY_API_KEY_STYLISH_STITCHES_JORDIN_KOLMAN',
    envSecret: 'SHOPIFY_API_SECRET_STYLISH_STITCHES_JORDIN_KOLMAN'
  },
  'pld-app.shopify.app.toml': {
    clientId: '8c37f2753cfd7a2bafdf5f6bdfd17557',
    store: 'pld-app-development-store.myshopify.com',
    envKey: 'SHOPIFY_API_KEY_PLD_APP_DEVELOPMENT_STORE',
    envSecret: 'SHOPIFY_API_SECRET_PLD_APP_DEVELOPMENT_STORE'
  },
  '75th-rra.shopify.app.toml': {
    clientId: '6a5f4bd11e96c9433fdca65fa1b7a2e2',
    store: '75th-rra-app-development-store.myshopify.com',
    envKey: 'SHOPIFY_API_KEY_75TH_RRA_APP_DEVELOPMENT_STORE',
    envSecret: 'SHOPIFY_API_SECRET_75TH_RRA_APP_DEVELOPMENT_STORE'
  }
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorLog(color, prefix, message) {
  console.log(`${colors[color]}[${prefix}]${colors.reset} ${message}`);
}

function info(message) {
  colorLog('blue', 'INFO', message);
}

function success(message) {
  colorLog('green', 'SUCCESS', message);
}

function warning(message) {
  colorLog('yellow', 'WARNING', message);
}

function error(message) {
  colorLog('red', 'ERROR', message);
}

function printHeader() {
  console.log(`${colors.cyan}================================${colors.reset}`);
  console.log(`${colors.cyan}  Shopify Config Verification${colors.reset}`);
  console.log(`${colors.cyan}================================${colors.reset}`);
  console.log('');
}

async function parseTomlFile(filePath) {
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    const config = {};

    // Simple TOML parser for our specific needs
    const lines = content.split('\n');
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('client_id')) {
        const match = trimmed.match(/client_id\s*=\s*"([^"]+)"/);
        if (match) config.client_id = match[1];
      }
      if (trimmed.startsWith('application_url')) {
        const match = trimmed.match(/application_url\s*=\s*"([^"]+)"/);
        if (match) config.application_url = match[1];
      }
      if (trimmed.startsWith('name')) {
        const match = trimmed.match(/name\s*=\s*"([^"]+)"/);
        if (match) config.name = match[1];
      }
    }

    return config;
  } catch (err) {
    throw new Error(`Failed to parse TOML file: ${err.message}`);
  }
}

async function loadEnvFile() {
  try {
    const content = await fs.readFile(ENV_FILE, 'utf-8');
    const env = {};

    const lines = content.split('\n');
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          env[key.trim()] = valueParts.join('=').trim();
        }
      }
    }

    return env;
  } catch (err) {
    throw new Error(`Failed to load .env file: ${err.message}`);
  }
}

async function verifyConfigFile(configFile, expectedConfig) {
  const configPath = path.join(CONFIGS_DIR, configFile);

  info(`Verifying ${configFile}...`);

  try {
    // Check if file exists
    await fs.access(configPath);

    // Parse the config file
    const config = await parseTomlFile(configPath);

    // Verify client_id
    if (config.client_id !== expectedConfig.clientId) {
      error(`  ✗ Client ID mismatch. Expected: ${expectedConfig.clientId}, Found: ${config.client_id}`);
      return false;
    }

    // Verify required fields exist
    if (!config.application_url) {
      error(`  ✗ Missing application_url`);
      return false;
    }

    if (!config.name) {
      error(`  ✗ Missing name`);
      return false;
    }

    success(`  ✓ Config file is valid`);
    success(`  ✓ Client ID: ${config.client_id}`);
    success(`  ✓ App Name: ${config.name}`);
    success(`  ✓ App URL: ${config.application_url}`);

    return true;

  } catch (err) {
    error(`  ✗ ${err.message}`);
    return false;
  }
}

async function verifyEnvironmentVariables(env) {
  info('Verifying environment variables...');

  let allValid = true;

  for (const [configFile, expectedConfig] of Object.entries(EXPECTED_CONFIGS)) {
    const { envKey, envSecret, clientId, store } = expectedConfig;

    info(`  Checking ${store}...`);

    // Check API key (environment variable should contain the client ID)
    if (env[envKey] === clientId) {
      success(`    ✓ ${envKey} contains correct client ID`);
    } else if (env[envKey]) {
      error(`    ✗ ${envKey} contains incorrect client ID`);
      error(`      Expected: ${clientId}`);
      error(`      Found: ${env[envKey]}`);
      allValid = false;
    } else {
      error(`    ✗ ${envKey} not found in .env`);
      allValid = false;
    }

    // Check API secret
    if (env[envSecret]) {
      success(`    ✓ ${envSecret} is set`);
    } else {
      error(`    ✗ ${envSecret} not found in .env`);
      allValid = false;
    }
  }

  return allValid;
}

async function verifyDeploymentScripts() {
  info('Verifying deployment scripts...');

  const scriptsToCheck = [
    'scripts/deploy-all-apps.js',
    'scripts/deploy-all-apps.sh'
  ];

  let allValid = true;

  for (const script of scriptsToCheck) {
    const scriptPath = path.join(PROJECT_ROOT, script);
    try {
      await fs.access(scriptPath);
      success(`  ✓ ${script} exists`);
    } catch (err) {
      error(`  ✗ ${script} not found`);
      allValid = false;
    }
  }

  return allValid;
}

async function main() {
  try {
    printHeader();

    let allValid = true;

    // Verify config files
    info('Verifying configuration files...');
    for (const [configFile, expectedConfig] of Object.entries(EXPECTED_CONFIGS)) {
      const isValid = await verifyConfigFile(configFile, expectedConfig);
      if (!isValid) allValid = false;
      console.log('');
    }

    // Load and verify environment variables
    try {
      const env = await loadEnvFile();
      const envValid = await verifyEnvironmentVariables(env);
      if (!envValid) allValid = false;
    } catch (err) {
      error(`Environment verification failed: ${err.message}`);
      allValid = false;
    }

    console.log('');

    // Verify deployment scripts
    const scriptsValid = await verifyDeploymentScripts();
    if (!scriptsValid) allValid = false;

    console.log('');

    // Print summary
    if (allValid) {
      success('🎉 All configurations are valid!');
      console.log('');
      info('You can now deploy using:');
      console.log('  npm run deploy:all              # Deploy to all stores');
      console.log('  npm run deploy:all:parallel     # Deploy to all stores in parallel');
      console.log('  npm run deploy:stylish-stitches # Deploy to specific store');
    } else {
      error('❌ Configuration verification failed!');
      console.log('');
      error('Please fix the issues above before deploying.');
    }

    process.exit(allValid ? 0 : 1);

  } catch (err) {
    error(`Verification failed: ${err.message}`);
    process.exit(1);
  }
}

// Run the script
main().catch(err => {
  error(`Unexpected error: ${err.message}`);
  process.exit(1);
});
