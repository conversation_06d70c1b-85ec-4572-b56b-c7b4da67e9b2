#!/usr/bin/env node

/**
 * Debug Database URL
 * 
 * This script helps debug what DATABASE_URL Prisma is actually using.
 */

import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';

// Load environment variables
dotenv.config();

console.log('🔍 Debugging DATABASE_URL...');
console.log('');

// Check environment variables
console.log('📋 Environment Variables:');
console.log(`NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`DATABASE_URL: ${process.env.DATABASE_URL}`);
console.log(`TEST_DATABASE_URL: ${process.env.TEST_DATABASE_URL}`);
console.log('');

// Parse DATABASE_URL
if (process.env.DATABASE_URL) {
  try {
    const url = new URL(process.env.DATABASE_URL);
    console.log('🔗 Parsed DATABASE_URL:');
    console.log(`  Protocol: ${url.protocol}`);
    console.log(`  Username: ${url.username}`);
    console.log(`  Password: ${url.password ? '[HIDDEN]' : 'NOT SET'}`);
    console.log(`  Host: ${url.hostname}`);
    console.log(`  Port: ${url.port}`);
    console.log(`  Database: ${url.pathname.slice(1)}`);
    console.log('');
  } catch (error) {
    console.error('❌ Invalid DATABASE_URL format:', error.message);
  }
} else {
  console.error('❌ DATABASE_URL not found in environment variables');
}

// Test Prisma client creation
console.log('🧪 Testing Prisma Client...');
try {
  const prisma = new PrismaClient({
    log: ['info', 'warn', 'error'],
  });
  
  console.log('✅ Prisma client created successfully');
  
  // Try to connect
  console.log('🔌 Testing database connection...');
  await prisma.$connect();
  console.log('✅ Database connection successful');
  
  // Test a simple query
  console.log('📊 Testing simple query...');
  const result = await prisma.$queryRaw`SELECT 1 as test`;
  console.log('✅ Query successful:', result);
  
  await prisma.$disconnect();
  
} catch (error) {
  console.error('❌ Prisma error:', error.message);
  
  if (error.message.includes('P1000')) {
    console.log('');
    console.log('🔧 Authentication Error Troubleshooting:');
    console.log('1. Check if PostgreSQL is running: docker-compose ps');
    console.log('2. Verify database exists: docker-compose exec postgres psql -U dev_user -l');
    console.log('3. Test connection manually: psql "postgresql://dev_user:dev_password@localhost:5432/americans_united_dev"');
    console.log('4. Check Docker logs: docker-compose logs postgres');
  }
  
  if (error.message.includes('P1001')) {
    console.log('');
    console.log('🔧 Connection Error Troubleshooting:');
    console.log('1. Check if PostgreSQL is running: docker-compose ps');
    console.log('2. Check if port 5432 is accessible: telnet localhost 5432');
    console.log('3. Restart PostgreSQL: docker-compose restart postgres');
  }
}

console.log('');
console.log('🏁 Debug complete');
