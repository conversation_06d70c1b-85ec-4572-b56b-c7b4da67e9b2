/**
 * Setup script for test order reconciliation
 *
 * This script sets up a cron job to run test order reconciliation at regular intervals
 * It can be run manually or as part of the app setup process
 *
 * Usage:
 * node scripts/setup-test-order-reconciliation.js
 */

import axios from 'axios';
import nodeCron from 'node-cron';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';

// Get current file directory (ES modules don't have __dirname)
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables - try both .env files
try {
  // First try the americans-united-inc specific env file
  dotenv.config({ path: join(__dirname, '..', '.env.americans-united-inc') });
  console.log('Loaded environment from .env.americans-united-inc');
} catch (error) {
  console.log('Could not load .env.americans-united-inc, falling back to .env');
  // Fall back to the default .env file
  dotenv.config();
}

/**
 * Read the application URL from the Shopify app TOML file
 * @returns {Promise<string|null>} The application URL or null if not found
 */
async function getAppUrlFromConfig() {
  try {
    // Path to the Shopify app TOML file (relative to this script)
    const tomlPath = join(__dirname, '..', 'shopify.app.americans-united-inc.toml');
    const altTomlPath = join(__dirname, '..', 'shopify.app.toml');

    // Try to read the TOML file
    let tomlContent;
    try {
      tomlContent = await fs.readFile(tomlPath, 'utf8');
      console.log(`Successfully read config from ${tomlPath}`);
    } catch (error) {
      // If the first file doesn't exist, try the alternative
      console.log(`Could not find ${tomlPath}, trying alternative...`);
      try {
        tomlContent = await fs.readFile(altTomlPath, 'utf8');
        console.log(`Successfully read config from ${altTomlPath}`);
      } catch (altError) {
        console.error(`Could not find either TOML file: ${error.message}, ${altError.message}`);
        return null;
      }
    }

    // Extract the application_url using a simple regex
    const match = tomlContent.match(/application_url\s*=\s*"([^"]+)"/);
    if (match && match[1]) {
      console.log(`Found application URL in TOML file: ${match[1]}`);
      return match[1];
    }

    console.log('Could not find application_url in TOML file');
    return null;
  } catch (error) {
    console.error(`Error reading app URL from config: ${error.message}`);
    return null;
  }
}

// Configuration
const API_ENDPOINT = '/api/scheduled-reconciliation';
const API_KEY = process.env.SHOPIFY_API_KEY;
const INTERVAL_MINUTES = 5; // Run every 5 minutes

// Debug: Print the API key (first 4 and last 4 characters for security)
if (API_KEY) {
  const firstChars = API_KEY.substring(0, 4);
  const lastChars = API_KEY.substring(API_KEY.length - 4);
  console.log(`Using API key: ${firstChars}...${lastChars}`);
} else {
  console.error('API key is not set! Make sure SHOPIFY_API_KEY is defined in your environment.');
}

// Fallback URLs in order of preference
const FALLBACK_URLS = [
  process.env.APP_URL,
  'http://localhost:3000',
  'http://localhost:8081'
];

// Initialize API_URL - will be set properly in the init function
let API_URL;

/**
 * Initialize the script by setting up the API URL
 * @returns {Promise<boolean>} True if initialization was successful
 */
async function initialize() {
  // Check for API key
  if (!API_KEY) {
    console.error('Error: SHOPIFY_API_KEY environment variable is not set');
    return false;
  }

  // Try to get the app URL from the config file
  const configUrl = await getAppUrlFromConfig();

  // Use the URL from config or fall back to the fallback URLs
  let baseUrl = configUrl;

  // If we couldn't get the URL from config, try the fallbacks
  if (!baseUrl) {
    console.log('Could not get URL from config file, trying fallbacks...');

    // Find the first non-empty fallback URL
    baseUrl = FALLBACK_URLS.find(url => url);

    if (!baseUrl) {
      console.error('Could not determine app URL. Please set APP_URL environment variable or ensure the app is running with Shopify CLI.');
      return false;
    }
  }

  // Construct the full API URL
  try {
    API_URL = new URL(API_ENDPOINT, baseUrl).toString();
    console.log(`Using API URL: ${API_URL}`);
    return true;
  } catch (error) {
    console.error(`Error constructing API URL: ${error.message}`);
    return false;
  }
}

/**
 * Run test order reconciliation
 */
async function runTestOrderReconciliation() {
  try {
    console.log(`[${new Date().toISOString()}] Running test order reconciliation...`);

    const response = await axios.post(
      API_URL,
      {
        type: 'test-orders',
        hoursToLookBack: 1 // Look back 1 hour for test orders
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': API_KEY
        }
      }
    );

    if (response.data.success) {
      console.log(`[${new Date().toISOString()}] Test order reconciliation completed successfully`);
      console.log(`Processed: ${response.data.results.totalOrdersProcessed}, Skipped: ${response.data.results.totalOrdersSkipped}, Failed: ${response.data.results.totalOrdersFailed}`);
    } else {
      console.error(`[${new Date().toISOString()}] Test order reconciliation failed: ${response.data.error}`);
    }
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error running test order reconciliation:`, error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

/**
 * Set up cron job to run test order reconciliation at regular intervals
 */
function setupCronJob() {
  // Create cron expression for the specified interval
  // Format: minute hour day month day-of-week
  const cronExpression = `*/${INTERVAL_MINUTES} * * * *`; // Run every X minutes

  console.log(`Setting up cron job to run test order reconciliation every ${INTERVAL_MINUTES} minutes`);
  console.log(`Cron expression: ${cronExpression}`);

  // Schedule the cron job
  const job = nodeCron.schedule(cronExpression, runTestOrderReconciliation, {
    scheduled: true,
    timezone: 'UTC' // Use UTC timezone
  });

  console.log('Cron job scheduled successfully');

  // Run immediately on startup
  console.log('Running initial test order reconciliation...');
  runTestOrderReconciliation();

  return job;
}

// Main execution function
async function main() {
  // Initialize the script
  const initialized = await initialize();

  if (!initialized) {
    console.error('Initialization failed. Exiting...');
    process.exit(1);
  }

  // Set up the cron job
  setupCronJob();

  // Keep the process running
  console.log('Process will continue running to maintain the cron job');
  console.log('Press Ctrl+C to exit');
}

// Run the main function
main().catch(error => {
  console.error('Error in main execution:', error);
  process.exit(1);
});

// Export the functions for use in other modules
export {
  runTestOrderReconciliation,
  setupCronJob
};
