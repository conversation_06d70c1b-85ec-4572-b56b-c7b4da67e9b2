#!/bin/bash

# Load test environment variables for Express server
export NODE_ENV=development
export PORT=3000
export EXPRESS_PORT=4000
export ADMIN_SHOP=test-admin.myshopify.com
export SHOPIFY_APP_URL=https://test-app.example.com
export DATABASE_URL=postgresql://test:test@localhost:5432/test
export SHOPIFY_API_VERSION=2025-04
export SCOPES=read_orders,write_orders,read_products,write_products
export SHIPSTATION_V1_KEY=test-v1-key
export SHIPSTATION_V1_SECRET=test-v1-secret
export SHIPSTATION_API_KEY=test-v2-api-key
export SHIPSTATION_WEBHOOK_KEY=test-webhook-key
export SCHEDULED_JOB_TOKEN=test-scheduled-job-token-32-characters-long
export WEBHOOK_VERIFICATION_SECRET=test-webhook-verification-secret-32-chars
export SUPPORTED_STORES=test-store.myshopify.com,another-store.myshopify.com,third-store.myshopify.com
export SHOPIFY_API_KEY_TEST_STORE=test-api-key-1
export SHOPIFY_API_SECRET_TEST_STORE=test-api-secret-1
export SHOPIFY_API_KEY_ANOTHER_STORE=test-api-key-2
export SHOPIFY_API_SECRET_ANOTHER_STORE=test-api-secret-2
export SHOPIFY_API_KEY_THIRD_STORE=test-api-key-3
export SHOPIFY_API_SECRET_THIRD_STORE=test-api-secret-3

# Start the Express server
npm run start:express
