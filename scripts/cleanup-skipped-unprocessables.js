#!/usr/bin/env node

/**
 * Cleanup Script: Remove Incorrect Unprocessable Records
 * 
 * This script removes unprocessable records that were incorrectly created
 * when orders had "All line items skipped for this shop - no processing needed".
 * These are not actual errors but normal business logic cases.
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function cleanupSkippedUnprocessables() {
  try {
    console.log('Starting cleanup of incorrect unprocessable records...');

    // Find all unprocessable records with the "skipped" message
    const skippedRecords = await prisma.unprocessable.findMany({
      where: {
        message: 'All line items skipped for this shop - no processing needed'
      }
    });

    console.log(`Found ${skippedRecords.length} incorrect unprocessable records to remove`);

    if (skippedRecords.length === 0) {
      console.log('No records to clean up. Exiting.');
      return;
    }

    // Show details of what will be removed
    console.log('\nRecords to be removed:');
    skippedRecords.forEach(record => {
      console.log(`- ID: ${record.id}, Shop: ${record.shop}, Date: ${record.date}`);
    });

    // Ask for confirmation (in a real script, you might want to add readline for interactive confirmation)
    console.log('\nRemoving these records...');

    // Delete the records
    const deleteResult = await prisma.unprocessable.deleteMany({
      where: {
        message: 'All line items skipped for this shop - no processing needed'
      }
    });

    console.log(`Successfully removed ${deleteResult.count} incorrect unprocessable records`);

    // Show remaining unprocessable count by shop
    const remainingByShop = await prisma.unprocessable.groupBy({
      by: ['shop'],
      _count: true,
      orderBy: {
        shop: 'asc'
      }
    });

    console.log('\nRemaining unprocessable records by shop:');
    remainingByShop.forEach(group => {
      console.log(`- ${group.shop}: ${group._count} records`);
    });

  } catch (error) {
    console.error('Error during cleanup:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the cleanup
cleanupSkippedUnprocessables()
  .then(() => {
    console.log('\nCleanup completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Cleanup failed:', error);
    process.exit(1);
  });
