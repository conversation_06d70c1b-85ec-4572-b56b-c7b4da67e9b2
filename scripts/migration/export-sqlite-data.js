#!/usr/bin/env node

/**
 * Export SQLite Data Script
 *
 * This script exports all data from the existing SQLite database
 * to a JSON file for migration to PostgreSQL.
 */

import { PrismaClient } from '@prisma/client';
import fs from 'fs/promises';
import path from 'path';

const prisma = new PrismaClient();

async function exportData() {
  try {
    console.log('🔄 Starting SQLite data export...');

    const exportData = {
      metadata: {
        exportDate: new Date().toISOString(),
        source: 'SQLite',
        target: 'PostgreSQL'
      },
      data: {}
    };

    // Export all tables (updated to match current schema)
    console.log('📊 Exporting Session data...');
    exportData.data.sessions = await prisma.session.findMany();

    console.log('⚙️ Exporting SetupFlag data...');
    exportData.data.setupFlags = await prisma.setupFlag.findMany();

    console.log('📊 Exporting BalanceGroup data...');
    exportData.data.balanceGroups = await prisma.balanceGroup.findMany();

    console.log('💰 Exporting InvoiceBalance data...');
    exportData.data.invoiceBalances = await prisma.invoiceBalance.findMany();

    console.log('💳 Exporting InvoiceTransaction data...');
    exportData.data.invoiceTransactions = await prisma.invoiceTransaction.findMany();

    console.log('🚚 Exporting ShippingCost data...');
    exportData.data.shippingCosts = await prisma.shippingCost.findMany();

    console.log('🚛 Exporting ShippingTransaction data...');
    exportData.data.shippingTransactions = await prisma.shippingTransaction.findMany();

    console.log('🗺️ Exporting ShipStationStoreMapping data...');
    exportData.data.shipStationStoreMappings = await prisma.shipStationStoreMapping.findMany();

    console.log('📋 Exporting ShipStationOrderTracking data...');
    exportData.data.shipStationOrderTracking = await prisma.shipStationOrderTracking.findMany();

    console.log('💲 Exporting Price data...');
    exportData.data.prices = await prisma.price.findMany();

    console.log('❌ Exporting Unprocessable data...');
    exportData.data.unprocessables = await prisma.unprocessable.findMany();

    console.log('🎯 Exporting ProcessedWebhook data...');
    exportData.data.processedWebhooks = await prisma.processedWebhook.findMany();

    console.log('🔄 Exporting ReconciliationJob data...');
    exportData.data.reconciliationJobs = await prisma.reconciliationJob.findMany();

    console.log('📦 Exporting VariantFulfillmentService data...');
    exportData.data.variantFulfillmentServices = await prisma.variantFulfillmentService.findMany();

    // Calculate record counts
    const totalRecords = Object.values(exportData.data).reduce((sum, table) => sum + table.length, 0);
    exportData.metadata.totalRecords = totalRecords;
    exportData.metadata.tableCount = Object.keys(exportData.data).length;

    // Write to file
    const exportPath = path.join(process.cwd(), 'data-export.json');
    await fs.writeFile(exportPath, JSON.stringify(exportData, null, 2));

    console.log('✅ Export completed successfully!');
    console.log(`📁 Export file: ${exportPath}`);
    console.log(`📊 Total records exported: ${totalRecords}`);
    console.log(`🗂️ Tables exported: ${exportData.metadata.tableCount}`);

    // Print summary by table
    console.log('\n📋 Export Summary:');
    Object.entries(exportData.data).forEach(([table, records]) => {
      console.log(`  ${table}: ${records.length} records`);
    });

  } catch (error) {
    console.error('❌ Export failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the export
exportData();
