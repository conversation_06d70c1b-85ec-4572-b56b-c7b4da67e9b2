#!/usr/bin/env node

/**
 * Import Data to PostgreSQL Script
 *
 * This script imports data from the exported JSON file
 * into the PostgreSQL database.
 */

import { PrismaClient } from '@prisma/client';
import fs from 'fs/promises';
import path from 'path';

const prisma = new PrismaClient();

async function importData() {
  try {
    console.log('🔄 Starting PostgreSQL data import...');

    // Read export file
    const exportPath = path.join(process.cwd(), 'data-export.json');
    const exportContent = await fs.readFile(exportPath, 'utf-8');
    const exportData = JSON.parse(exportContent);

    console.log(`📁 Reading export file: ${exportPath}`);
    console.log(`📊 Total records to import: ${exportData.metadata.totalRecords}`);
    console.log(`🗂️ Tables to import: ${exportData.metadata.tableCount}`);

    // Import data in dependency order (tables with foreign keys last)

    // 1. Independent tables first
    if (exportData.data.sessions?.length > 0) {
      console.log('📊 Importing Session data...');
      for (const session of exportData.data.sessions) {
        await prisma.session.upsert({
          where: { id: session.id },
          update: session,
          create: session
        });
      }
      console.log(`✅ Imported ${exportData.data.sessions.length} sessions`);
    }

    if (exportData.data.setupFlags?.length > 0) {
      console.log('⚙️ Importing SetupFlag data...');
      for (const setup of exportData.data.setupFlags) {
        await prisma.setupFlag.upsert({
          where: { shop: setup.shop },
          update: setup,
          create: setup
        });
      }
      console.log(`✅ Imported ${exportData.data.setupFlags.length} setup flags`);
    }

    if (exportData.data.balanceGroups?.length > 0) {
      console.log('📊 Importing BalanceGroup data...');
      for (const group of exportData.data.balanceGroups) {
        await prisma.balanceGroup.upsert({
          where: { id: group.id },
          update: group,
          create: group
        });
      }
      console.log(`✅ Imported ${exportData.data.balanceGroups.length} balance groups`);
    }

    // 2. Tables with shop dependencies
    if (exportData.data.invoiceBalances?.length > 0) {
      console.log('💰 Importing InvoiceBalance data...');
      for (const balance of exportData.data.invoiceBalances) {
        await prisma.invoiceBalance.upsert({
          where: { id: balance.id },
          update: balance,
          create: balance
        });
      }
      console.log(`✅ Imported ${exportData.data.invoiceBalances.length} invoice balances`);
    }

    if (exportData.data.invoiceTransactions?.length > 0) {
      console.log('💳 Importing InvoiceTransaction data...');
      for (const transaction of exportData.data.invoiceTransactions) {
        await prisma.invoiceTransaction.upsert({
          where: { id: transaction.id },
          update: transaction,
          create: transaction
        });
      }
      console.log(`✅ Imported ${exportData.data.invoiceTransactions.length} invoice transactions`);
    }

    if (exportData.data.shippingCosts?.length > 0) {
      console.log('🚚 Importing ShippingCost data...');
      for (const cost of exportData.data.shippingCosts) {
        await prisma.shippingCost.upsert({
          where: { id: cost.id },
          update: cost,
          create: cost
        });
      }
      console.log(`✅ Imported ${exportData.data.shippingCosts.length} shipping costs`);
    }

    if (exportData.data.shippingTransactions?.length > 0) {
      console.log('🚛 Importing ShippingTransaction data...');
      for (const transaction of exportData.data.shippingTransactions) {
        await prisma.shippingTransaction.upsert({
          where: { id: transaction.id },
          update: transaction,
          create: transaction
        });
      }
      console.log(`✅ Imported ${exportData.data.shippingTransactions.length} shipping transactions`);
    }

    if (exportData.data.prices?.length > 0) {
      console.log('💲 Importing Price data...');
      for (const price of exportData.data.prices) {
        await prisma.price.upsert({
          where: { id: price.id },
          update: price,
          create: price
        });
      }
      console.log(`✅ Imported ${exportData.data.prices.length} prices`);
    }

    if (exportData.data.reconciliationJobs?.length > 0) {
      console.log('🔄 Importing ReconciliationJob data...');
      for (const job of exportData.data.reconciliationJobs) {
        await prisma.reconciliationJob.upsert({
          where: { id: job.id },
          update: job,
          create: job
        });
      }
      console.log(`✅ Imported ${exportData.data.reconciliationJobs.length} reconciliation jobs`);
    }

    if (exportData.data.processedWebhooks?.length > 0) {
      console.log('🎯 Importing ProcessedWebhook data...');
      for (const webhook of exportData.data.processedWebhooks) {
        await prisma.processedWebhook.upsert({
          where: { id: webhook.id },
          update: webhook,
          create: webhook
        });
      }
      console.log(`✅ Imported ${exportData.data.processedWebhooks.length} processed webhooks`);
    }

    if (exportData.data.unprocessables?.length > 0) {
      console.log('❌ Importing Unprocessable data...');
      for (const unprocessable of exportData.data.unprocessables) {
        await prisma.unprocessable.upsert({
          where: { id: unprocessable.id },
          update: unprocessable,
          create: unprocessable
        });
      }
      console.log(`✅ Imported ${exportData.data.unprocessables.length} unprocessable records`);
    }

    if (exportData.data.variantFulfillmentServices?.length > 0) {
      console.log('📦 Importing VariantFulfillmentService data...');
      for (const service of exportData.data.variantFulfillmentServices) {
        await prisma.variantFulfillmentService.upsert({
          where: { variantId: service.variantId },
          update: service,
          create: service
        });
      }
      console.log(`✅ Imported ${exportData.data.variantFulfillmentServices.length} variant fulfillment services`);
    }

    if (exportData.data.shipStationOrderTracking?.length > 0) {
      console.log('📋 Importing ShipStationOrderTracking data...');
      for (const tracking of exportData.data.shipStationOrderTracking) {
        await prisma.shipStationOrderTracking.upsert({
          where: { storeId: tracking.storeId },
          update: tracking,
          create: tracking
        });
      }
      console.log(`✅ Imported ${exportData.data.shipStationOrderTracking.length} tracking records`);
    }

    if (exportData.data.shipStationStoreMappings?.length > 0) {
      console.log('🗺️ Importing ShipStationStoreMapping data...');
      for (const mapping of exportData.data.shipStationStoreMappings) {
        await prisma.shipStationStoreMapping.upsert({
          where: { storeId: mapping.storeId },
          update: mapping,
          create: mapping
        });
      }
      console.log(`✅ Imported ${exportData.data.shipStationStoreMappings.length} store mappings`);
    }

    console.log('✅ Import completed successfully!');
    console.log('🔍 Run validation script to verify data integrity');

  } catch (error) {
    console.error('❌ Import failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the import
importData();
