#!/usr/bin/env node

/**
 * Validate Migration Script
 *
 * This script validates that the data migration from SQLite to PostgreSQL
 * was successful by comparing record counts and key data integrity.
 */

import { PrismaClient } from '@prisma/client';
import fs from 'fs/promises';
import path from 'path';

const prisma = new PrismaClient();

async function validateMigration() {
  try {
    console.log('🔍 Starting migration validation...');

    // Try to read export file
    const exportPath = path.join(process.cwd(), 'data-export.json');
    let exportData = null;
    let hasExportData = false;

    try {
      const exportContent = await fs.readFile(exportPath, 'utf-8');
      exportData = JSON.parse(exportContent);
      hasExportData = true;

      console.log(`📁 Validating against export file: ${exportPath}`);
      const totalRecords = exportData.metadata?.totalRecords ||
                          Object.values(exportData.data || {}).reduce((sum, arr) => sum + (Array.isArray(arr) ? arr.length : 0), 0);
      console.log(`📊 Expected total records: ${totalRecords}`);
    } catch (error) {
      console.log('📁 No export file found - validating fresh database setup');
      hasExportData = false;
      exportData = { data: {}, metadata: { totalRecords: 0 } };
    }

    let validationErrors = [];
    let totalImported = 0;

    // Validate each table
    const validationResults = {};

    // Sessions
    const sessionCount = await prisma.session.count();
    const expectedSessions = exportData.data.sessions?.length || 0;
    validationResults.sessions = { expected: expectedSessions, actual: sessionCount };
    totalImported += sessionCount;
    if (sessionCount !== expectedSessions) {
      validationErrors.push(`Sessions: expected ${expectedSessions}, got ${sessionCount}`);
    }

    // Setup Flags (replacing shops)
    const setupFlagCount = await prisma.setupFlag.count();
    const expectedSetupFlags = exportData.data.setupFlags?.length || 0;
    validationResults.setupFlags = { expected: expectedSetupFlags, actual: setupFlagCount };
    totalImported += setupFlagCount;
    if (setupFlagCount !== expectedSetupFlags) {
      validationErrors.push(`Setup Flags: expected ${expectedSetupFlags}, got ${setupFlagCount}`);
    }

    // Invoice Balances
    const balanceCount = await prisma.invoiceBalance.count();
    const expectedBalances = exportData.data.invoiceBalances?.length || 0;
    validationResults.invoiceBalances = { expected: expectedBalances, actual: balanceCount };
    totalImported += balanceCount;
    if (balanceCount !== expectedBalances) {
      validationErrors.push(`Invoice Balances: expected ${expectedBalances}, got ${balanceCount}`);
    }

    // Shipping Costs
    const costCount = await prisma.shippingCost.count();
    const expectedCosts = exportData.data.shippingCosts?.length || 0;
    validationResults.shippingCosts = { expected: expectedCosts, actual: costCount };
    totalImported += costCount;
    if (costCount !== expectedCosts) {
      validationErrors.push(`Shipping Costs: expected ${expectedCosts}, got ${costCount}`);
    }

    // Prices
    const priceCount = await prisma.price.count();
    const expectedPrices = exportData.data.prices?.length || 0;
    validationResults.prices = { expected: expectedPrices, actual: priceCount };
    totalImported += priceCount;
    if (priceCount !== expectedPrices) {
      validationErrors.push(`Prices: expected ${expectedPrices}, got ${priceCount}`);
    }

    // Balance Groups
    const balanceGroupCount = await prisma.balanceGroup.count();
    const expectedBalanceGroups = exportData.data.balanceGroups?.length || 0;
    validationResults.balanceGroups = { expected: expectedBalanceGroups, actual: balanceGroupCount };
    totalImported += balanceGroupCount;
    if (balanceGroupCount !== expectedBalanceGroups) {
      validationErrors.push(`Balance Groups: expected ${expectedBalanceGroups}, got ${balanceGroupCount}`);
    }

    // Reconciliation Jobs
    const jobCount = await prisma.reconciliationJob.count();
    const expectedJobs = exportData.data.reconciliationJobs?.length || 0;
    validationResults.reconciliationJobs = { expected: expectedJobs, actual: jobCount };
    totalImported += jobCount;
    if (jobCount !== expectedJobs) {
      validationErrors.push(`Reconciliation Jobs: expected ${expectedJobs}, got ${jobCount}`);
    }

    // Processed Webhooks
    const webhookCount = await prisma.processedWebhook.count();
    const expectedWebhooks = exportData.data.processedWebhooks?.length || 0;
    validationResults.processedWebhooks = { expected: expectedWebhooks, actual: webhookCount };
    totalImported += webhookCount;
    if (webhookCount !== expectedWebhooks) {
      validationErrors.push(`Processed Webhooks: expected ${expectedWebhooks}, got ${webhookCount}`);
    }

    // Unprocessables
    const unprocessableCount = await prisma.unprocessable.count();
    const expectedUnprocessables = exportData.data.unprocessables?.length || 0;
    validationResults.unprocessables = { expected: expectedUnprocessables, actual: unprocessableCount };
    totalImported += unprocessableCount;
    if (unprocessableCount !== expectedUnprocessables) {
      validationErrors.push(`Unprocessables: expected ${expectedUnprocessables}, got ${unprocessableCount}`);
    }

    // Variant Fulfillment Services
    const serviceCount = await prisma.variantFulfillmentService.count();
    const expectedServices = exportData.data.variantFulfillmentServices?.length || 0;
    validationResults.variantFulfillmentServices = { expected: expectedServices, actual: serviceCount };
    totalImported += serviceCount;
    if (serviceCount !== expectedServices) {
      validationErrors.push(`Variant Fulfillment Services: expected ${expectedServices}, got ${serviceCount}`);
    }

    // ShipStation Order Tracking
    const trackingCount = await prisma.shipStationOrderTracking.count();
    const expectedTracking = exportData.data.shipStationOrderTracking?.length || 0;
    validationResults.shipStationOrderTracking = { expected: expectedTracking, actual: trackingCount };
    totalImported += trackingCount;
    if (trackingCount !== expectedTracking) {
      validationErrors.push(`ShipStation Order Tracking: expected ${expectedTracking}, got ${trackingCount}`);
    }

    // ShipStation Store Mappings
    const mappingCount = await prisma.shipStationStoreMapping.count();
    const expectedMappings = exportData.data.shipStationStoreMappings?.length || 0;
    validationResults.shipStationStoreMappings = { expected: expectedMappings, actual: mappingCount };
    totalImported += mappingCount;
    if (mappingCount !== expectedMappings) {
      validationErrors.push(`ShipStation Store Mappings: expected ${expectedMappings}, got ${mappingCount}`);
    }

    // Print validation results
    console.log('\n📋 Validation Results:');
    Object.entries(validationResults).forEach(([table, counts]) => {
      const status = counts.expected === counts.actual ? '✅' : '❌';
      console.log(`  ${status} ${table}: ${counts.actual}/${counts.expected}`);
    });

    const expectedTotal = exportData.metadata?.totalRecords ||
                         Object.values(exportData.data || {}).reduce((sum, arr) => sum + (Array.isArray(arr) ? arr.length : 0), 0);
    console.log(`\n📊 Total Records: ${totalImported}/${expectedTotal}`);

    if (validationErrors.length === 0) {
      if (!hasExportData) {
        console.log('✅ Migration validation passed! Fresh database setup is valid.');
      } else {
        console.log('✅ Migration validation passed! All data migrated successfully.');
      }

      // Additional integrity checks
      console.log('\n🔍 Running additional integrity checks...');

      // Check for any null IDs
      const tablesWithNullIds = [];
      for (const [tableName] of Object.entries(validationResults)) {
        try {
          const nullIdCount = await prisma[tableName].count({
            where: { id: null }
          });
          if (nullIdCount > 0) {
            tablesWithNullIds.push(`${tableName}: ${nullIdCount} null IDs`);
          }
        } catch (error) {
          // Skip if table doesn't have id field or other issues
        }
      }

      if (tablesWithNullIds.length > 0) {
        console.log('⚠️ Found null IDs in:', tablesWithNullIds);
      } else {
        console.log('✅ No null IDs found');
      }

      console.log('✅ Migration validation completed successfully!');

    } else {
      console.log('\n❌ Migration validation failed!');
      console.log('Errors found:');
      validationErrors.forEach(error => console.log(`  - ${error}`));
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ Validation failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the validation
validateMigration();
