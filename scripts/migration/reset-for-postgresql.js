#!/usr/bin/env node

/**
 * Reset Migration History for PostgreSQL
 *
 * This script safely resets the Prisma migration history when switching
 * from SQLite to PostgreSQL, with comprehensive database setup and validation.
 */

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function execCommand(command, options = {}) {
  try {
    const result = execSync(command, {
      stdio: 'pipe',
      encoding: 'utf8',
      ...options
    });
    return { success: true, output: result };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      output: error.stdout || error.stderr || ''
    };
  }
}

async function setupPostgreSQL() {
  console.log('🐘 Setting up PostgreSQL...');

  // Check if Docker is running
  const dockerCheck = execCommand('docker --version');
  if (!dockerCheck.success) {
    throw new Error('Docker is not installed or not running. Please install Docker first.');
  }

  // Start PostgreSQL container
  console.log('🚀 Starting PostgreSQL container...');
  const startResult = execCommand('docker-compose up -d postgres');
  if (!startResult.success) {
    throw new Error(`Failed to start PostgreSQL: ${startResult.error}`);
  }

  // Wait for PostgreSQL to be ready
  console.log('⏳ Waiting for PostgreSQL to be ready...');
  let attempts = 0;
  const maxAttempts = 30;

  while (attempts < maxAttempts) {
    const healthCheck = execCommand('docker-compose exec -T postgres pg_isready -U dev_user -d americans_united_dev');
    if (healthCheck.success) {
      console.log('✅ PostgreSQL is ready');
      break;
    }

    attempts++;
    if (attempts >= maxAttempts) {
      throw new Error('PostgreSQL failed to start within 30 seconds');
    }

    await sleep(1000);
  }

  // Ensure databases exist
  console.log('🗄️  Ensuring databases exist...');

  // Check if databases exist and create them if needed
  const databases = ['americans_united_dev', 'americans_united_test'];

  for (const dbName of databases) {
    // Check if database exists
    const checkDb = execCommand(`docker-compose exec -T postgres psql -U dev_user -d postgres -tAc "SELECT 1 FROM pg_database WHERE datname='${dbName}'"`);

    if (!checkDb.success || !checkDb.output.trim()) {
      // Database doesn't exist, create it
      console.log(`📦 Creating database: ${dbName}`);
      const createResult = execCommand(`docker-compose exec -T postgres psql -U dev_user -d postgres -c "CREATE DATABASE ${dbName};"`);
      if (!createResult.success) {
        console.log(`⚠️  Could not create database ${dbName}: ${createResult.output}`);
      } else {
        console.log(`✅ Created database: ${dbName}`);
      }
    } else {
      console.log(`✅ Database already exists: ${dbName}`);
    }

    // Grant privileges
    const grantResult = execCommand(`docker-compose exec -T postgres psql -U dev_user -d postgres -c "GRANT ALL PRIVILEGES ON DATABASE ${dbName} TO dev_user;"`);
    if (!grantResult.success) {
      console.log(`⚠️  Could not grant privileges on ${dbName}: ${grantResult.output}`);
    }
  }

  // Test connection to main database
  console.log('🔌 Testing database connection...');
  const testConnection = execCommand('docker-compose exec -T postgres psql -U dev_user -d americans_united_dev -c "SELECT 1;"');
  if (!testConnection.success) {
    throw new Error(`Database connection test failed: ${testConnection.error}`);
  }

  console.log('✅ PostgreSQL setup completed');
}

async function resetMigrationHistory() {
  try {
    console.log('🔄 Resetting Prisma migration history for PostgreSQL...');

    const projectRoot = process.cwd();
    const migrationsDir = path.join(projectRoot, 'prisma', 'migrations');
    const lockFile = path.join(migrationsDir, 'migration_lock.toml');

    // Step 1: Setup PostgreSQL
    await setupPostgreSQL();

    // Step 2: Check if migrations directory exists
    try {
      await fs.access(migrationsDir);
      console.log('📁 Found existing migrations directory');

      // Backup existing migrations (optional, for reference)
      const backupDir = path.join(projectRoot, 'prisma', 'migrations-sqlite-backup');
      console.log('💾 Creating backup of SQLite migrations...');

      try {
        await fs.cp(migrationsDir, backupDir, { recursive: true });
        console.log(`✅ SQLite migrations backed up to: ${backupDir}`);
      } catch (backupError) {
        console.log('⚠️  Could not create backup, continuing...');
      }

      // Remove existing migrations directory
      console.log('🗑️  Removing existing migrations directory...');
      await fs.rm(migrationsDir, { recursive: true, force: true });
      console.log('✅ Removed SQLite migration history');

    } catch (error) {
      console.log('📁 No existing migrations directory found');
    }

    // Step 3: Verify PostgreSQL connection and environment
    console.log('🔍 Verifying PostgreSQL connection...');

    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is not set');
    }

    if (!process.env.DATABASE_URL.includes('postgresql://')) {
      throw new Error('DATABASE_URL must be a PostgreSQL connection string');
    }

    console.log('✅ PostgreSQL connection string found');

    // Step 4: Test Prisma connection before migration
    console.log('🧪 Testing Prisma connection...');
    try {
      const testResult = execCommand('npx prisma db pull --force');
      if (!testResult.success) {
        throw new Error(`Prisma connection test failed: ${testResult.error}`);
      }
      console.log('✅ Prisma can connect to PostgreSQL');
    } catch (testError) {
      console.log('⚠️  Prisma connection test had issues, but continuing...');
    }

    // Step 5: Generate new migration for PostgreSQL
    console.log('🏗️  Generating initial PostgreSQL migration...');

    const migrateResult = execCommand('npx prisma migrate dev --name init', { cwd: projectRoot });
    if (!migrateResult.success) {
      console.error('❌ Failed to create initial migration');
      console.error('Error output:', migrateResult.output);

      // Try alternative approach
      console.log('🔄 Trying alternative migration approach...');
      const altResult = execCommand('npx prisma migrate reset --force && npx prisma migrate dev --name init', { cwd: projectRoot });
      if (!altResult.success) {
        throw new Error(`Migration failed: ${altResult.error}\nOutput: ${altResult.output}`);
      }
    }
    console.log('✅ Initial PostgreSQL migration created');

    // Step 6: Verify new migration lock file
    try {
      const newLockContent = await fs.readFile(lockFile, 'utf-8');
      if (newLockContent.includes('provider = "postgresql"')) {
        console.log('✅ Migration lock file updated to PostgreSQL');
      } else {
        console.log('⚠️  Migration lock file may not be updated correctly');
        console.log('Lock file content:', newLockContent);
      }
    } catch (lockError) {
      console.log('⚠️  Could not verify migration lock file');
    }

    // Step 7: Generate Prisma client
    console.log('🔧 Generating Prisma client...');
    const generateResult = execCommand('npx prisma generate', { cwd: projectRoot });
    if (!generateResult.success) {
      console.error('❌ Failed to generate Prisma client');
      throw new Error(`Client generation failed: ${generateResult.error}`);
    }
    console.log('✅ Prisma client generated');

    // Step 8: Final health check
    console.log('🏥 Running final health check...');
    try {
      const healthResult = execCommand('node -e "import(\'./app/utils/database-health.js\').then(({quickHealthCheck}) => quickHealthCheck().then(r => console.log(\'Health:\', r.status)))"');
      if (healthResult.success) {
        console.log('✅ Database health check passed');
      } else {
        console.log('⚠️  Health check had issues but migration completed');
      }
    } catch (healthError) {
      console.log('⚠️  Could not run health check, but migration completed');
    }

    console.log('\n🎉 Migration history reset completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Verify your PostgreSQL database is running');
    console.log('2. Run the data export script if you need to migrate existing data:');
    console.log('   npm run export-data');
    console.log('3. Run the data import script to populate PostgreSQL:');
    console.log('   npm run import-data');
    console.log('4. Validate the migration:');
    console.log('   npm run validate-migration');

  } catch (error) {
    console.error('❌ Migration reset failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Ensure PostgreSQL is running and accessible');
    console.error('2. Verify DATABASE_URL environment variable is set correctly');
    console.error('3. Check that you have write permissions in the prisma directory');
    console.error('4. Make sure no other processes are using the database');
    process.exit(1);
  }
}

// Check if running directly
if (import.meta.url === `file://${process.argv[1]}`) {
  resetMigrationHistory();
}

export { resetMigrationHistory };
