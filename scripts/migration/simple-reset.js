#!/usr/bin/env node

/**
 * Simple Migration Reset
 * 
 * A simplified version that handles the most common migration reset scenarios.
 */

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';

function runCommand(command, description) {
  console.log(`🔧 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    return false;
  }
}

async function simpleReset() {
  try {
    console.log('🚀 Starting simple migration reset...');
    
    const projectRoot = process.cwd();
    const migrationsDir = path.join(projectRoot, 'prisma', 'migrations');
    
    // Step 1: Start PostgreSQL
    console.log('🐘 Starting PostgreSQL...');
    runCommand('docker-compose up -d postgres', 'Starting PostgreSQL container');
    
    // Wait a bit for PostgreSQL to start
    console.log('⏳ Waiting for PostgreSQL to start...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // Step 2: Remove old migrations
    console.log('🗑️  Removing old migration history...');
    try {
      await fs.rm(migrationsDir, { recursive: true, force: true });
      console.log('✅ Old migrations removed');
    } catch (error) {
      console.log('📁 No existing migrations found');
    }
    
    // Step 3: Test database connection
    console.log('🔌 Testing database connection...');
    const connectionTest = runCommand(
      'docker-compose exec -T postgres psql -U dev_user -d americans_united_dev -c "SELECT 1;"',
      'Testing database connection'
    );
    
    if (!connectionTest) {
      console.log('🔧 Creating database...');
      runCommand(
        'docker-compose exec -T postgres psql -U dev_user -d postgres -c "CREATE DATABASE americans_united_dev;"',
        'Creating main database'
      );
      runCommand(
        'docker-compose exec -T postgres psql -U dev_user -d postgres -c "CREATE DATABASE americans_united_test;"',
        'Creating test database'
      );
    }
    
    // Step 4: Create new migration
    console.log('🏗️  Creating new PostgreSQL migration...');
    const migrationSuccess = runCommand(
      'npx prisma migrate dev --name init',
      'Creating initial migration'
    );
    
    if (!migrationSuccess) {
      console.log('🔄 Trying migration reset...');
      runCommand(
        'npx prisma migrate reset --force',
        'Resetting migration state'
      );
      runCommand(
        'npx prisma migrate dev --name init',
        'Creating initial migration (retry)'
      );
    }
    
    // Step 5: Generate Prisma client
    runCommand('npx prisma generate', 'Generating Prisma client');
    
    // Step 6: Verify setup
    console.log('🔍 Verifying setup...');
    try {
      const lockFile = path.join(migrationsDir, 'migration_lock.toml');
      const lockContent = await fs.readFile(lockFile, 'utf-8');
      if (lockContent.includes('provider = "postgresql"')) {
        console.log('✅ Migration lock file correctly set to PostgreSQL');
      } else {
        console.log('⚠️  Migration lock file may have issues');
      }
    } catch (error) {
      console.log('⚠️  Could not verify migration lock file');
    }
    
    console.log('\n🎉 Simple migration reset completed!');
    console.log('\n📋 Next steps:');
    console.log('1. Test your application: npm run dev');
    console.log('2. If you have data to migrate: npm run import-data');
    console.log('3. Run tests to verify everything works');
    
  } catch (error) {
    console.error('❌ Simple reset failed:', error.message);
    console.log('\n🔧 Manual steps to try:');
    console.log('1. docker-compose up -d postgres');
    console.log('2. rm -rf prisma/migrations');
    console.log('3. npx prisma migrate dev --name init');
    console.log('4. npx prisma generate');
    process.exit(1);
  }
}

// Check if running directly
if (import.meta.url === `file://${process.argv[1]}`) {
  simpleReset();
}

export { simpleReset };
