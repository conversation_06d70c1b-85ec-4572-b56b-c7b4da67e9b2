#!/bin/bash

# Deploy All Shopify Apps Script
# This script deploys the Americans United Inc app to all configured stores simultaneously

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIGS_DIR="$PROJECT_ROOT/shopify-configs"
LOG_DIR="$PROJECT_ROOT/logs/deployments"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# App configurations
declare -A APPS=(
    ["stylish-stitches"]="stylish-stitches"
    ["pld-app"]="pld-app"
    ["75th-rra"]="75th-rra"
)

# Store domains for reference
declare -A STORES=(
    ["stylish-stitches"]="stylish-stitches-jordin-kolman.myshopify.com"
    ["pld-app"]="pld-app-development-store.myshopify.com"
    ["75th-rra"]="75th-rra-app-development-store.myshopify.com"
)

# Functions
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Americans United Inc App Deploy${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    print_status "Checking prerequisites..."

    # Check if Shopify CLI is installed
    if ! command -v shopify &> /dev/null; then
        print_error "Shopify CLI is not installed. Please install it first:"
        echo "  npm install -g @shopify/cli @shopify/theme"
        exit 1
    fi

    # Check if we're in the right directory
    if [ ! -f "$PROJECT_ROOT/package.json" ]; then
        print_error "Not in the correct project directory. Please run from the project root."
        exit 1
    fi

    # Check if config files exist
    for app in "${!APPS[@]}"; do
        config_file="$PROJECT_ROOT/shopify.app.${APPS[$app]}.toml"
        if [ ! -f "$config_file" ]; then
            print_error "Config file not found: $config_file"
            exit 1
        fi
    done

    # Create log directory
    mkdir -p "$LOG_DIR"

    print_success "Prerequisites check passed"
}

deploy_app() {
    local app_name="$1"
    local config_name="$2"
    local store_domain="${STORES[$app_name]}"
    local log_file="$LOG_DIR/${app_name}_${TIMESTAMP}.log"

    print_status "Deploying $app_name to $store_domain..."
    print_status "Config name: $config_name"
    print_status "Working directory: $PROJECT_ROOT"

    # Verify config file exists
    local config_file="$PROJECT_ROOT/shopify.app.${config_name}.toml"
    if [ ! -f "$config_file" ]; then
        print_error "Config file not found: $config_file"
        return 1
    fi
    print_status "✓ Config file exists: $config_file"

    # Get the client ID from the config file
    local client_id=$(grep "^client_id" "$config_file" | cut -d'"' -f2)
    print_status "Using client ID: $client_id"

    # Change to project root
    cd "$PROJECT_ROOT"

    # Set SHOPIFY_API_KEY environment variable and deploy
    if SHOPIFY_API_KEY="$client_id" shopify app deploy --config "$config_name" --force > "$log_file" 2>&1; then
        print_success "✓ $app_name deployed successfully"
        return 0
    else
        print_error "✗ $app_name deployment failed"
        print_error "Check log file: $log_file"
        return 1
    fi
}

deploy_all_sequential() {
    print_status "Starting sequential deployment..."
    local failed_deployments=()
    local successful_deployments=()

    for app in "${!APPS[@]}"; do
        config_name="${APPS[$app]}"

        if deploy_app "$app" "$config_name"; then
            successful_deployments+=("$app")
        else
            failed_deployments+=("$app")
        fi

        echo ""
    done

    # Print summary
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Deployment Summary${NC}"
    echo -e "${BLUE}================================${NC}"

    if [ ${#successful_deployments[@]} -gt 0 ]; then
        print_success "Successful deployments (${#successful_deployments[@]}):"
        for app in "${successful_deployments[@]}"; do
            echo -e "  ${GREEN}✓${NC} $app (${STORES[$app]})"
        done
    fi

    if [ ${#failed_deployments[@]} -gt 0 ]; then
        print_error "Failed deployments (${#failed_deployments[@]}):"
        for app in "${failed_deployments[@]}"; do
            echo -e "  ${RED}✗${NC} $app (${STORES[$app]})"
        done
        echo ""
        print_error "Check log files in: $LOG_DIR"
        return 1
    fi

    print_success "All deployments completed successfully!"
    return 0
}

deploy_all_parallel() {
    print_status "Starting parallel deployment..."
    local pids=()
    local apps_array=()

    # Start all deployments in background
    for app in "${!APPS[@]}"; do
        config_name="${APPS[$app]}"
        apps_array+=("$app")

        (deploy_app "$app" "$config_name") &
        pids+=($!)
    done

    # Wait for all deployments to complete
    local failed_deployments=()
    local successful_deployments=()

    for i in "${!pids[@]}"; do
        local pid=${pids[$i]}
        local app=${apps_array[$i]}

        if wait $pid; then
            successful_deployments+=("$app")
        else
            failed_deployments+=("$app")
        fi
    done

    # Print summary (same as sequential)
    echo ""
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Deployment Summary${NC}"
    echo -e "${BLUE}================================${NC}"

    if [ ${#successful_deployments[@]} -gt 0 ]; then
        print_success "Successful deployments (${#successful_deployments[@]}):"
        for app in "${successful_deployments[@]}"; do
            echo -e "  ${GREEN}✓${NC} $app (${STORES[$app]})"
        done
    fi

    if [ ${#failed_deployments[@]} -gt 0 ]; then
        print_error "Failed deployments (${#failed_deployments[@]}):"
        for app in "${failed_deployments[@]}"; do
            echo -e "  ${RED}✗${NC} $app (${STORES[$app]})"
        done
        echo ""
        print_error "Check log files in: $LOG_DIR"
        return 1
    fi

    print_success "All deployments completed successfully!"
    return 0
}

show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Deploy Americans United Inc Shopify app to all configured stores"
    echo ""
    echo "Options:"
    echo "  -p, --parallel     Deploy to all stores in parallel (faster)"
    echo "  -s, --sequential   Deploy to stores one by one (default, safer)"
    echo "  -h, --help         Show this help message"
    echo ""
    echo "Configured stores:"
    for app in "${!STORES[@]}"; do
        echo "  - ${STORES[$app]} (config: ${APPS[$app]})"
    done
}

# Main script
main() {
    local deployment_mode="sequential"

    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -p|--parallel)
                deployment_mode="parallel"
                shift
                ;;
            -s|--sequential)
                deployment_mode="sequential"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done

    print_header
    check_prerequisites

    print_status "Deployment mode: $deployment_mode"
    print_status "Timestamp: $TIMESTAMP"
    echo ""

    if [ "$deployment_mode" = "parallel" ]; then
        deploy_all_parallel
    else
        deploy_all_sequential
    fi
}

# Run the script
main "$@"
