#!/bin/bash

# Database Restore Script for ATP Invoice Preview Application
# This script restores database backups created by the backup script

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
BACKUP_DIR="${BACKUP_DIR:-$PROJECT_ROOT/backups}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Show usage information
show_usage() {
    cat << EOF
Usage: $0 [options] <backup_file>

Restore database from backup file.

Arguments:
  backup_file    Path to the backup file (.sql or .sql.gz)

Options:
  --help, -h           Show this help message
  --list, -l           List available backup files
  --verify, -v         Verify backup file before restore
  --force, -f          Skip confirmation prompts
  --target-db DB       Target database name (default: from DATABASE_URL)
  --dry-run           Show what would be restored without executing

Environment Variables:
  DATABASE_URL         Database connection string (required)
  BACKUP_DIR          Backup directory (default: ./backups)

Examples:
  $0 database_backup_20240101_120000.sql.gz
  $0 --verify --force /path/to/backup.sql
  $0 --list
EOF
}

# Check if required tools are installed
check_dependencies() {
    log "Checking dependencies..."
    
    local missing_deps=()
    
    if ! command -v psql &> /dev/null; then
        missing_deps+=("postgresql-client")
    fi
    
    if ! command -v pg_restore &> /dev/null; then
        missing_deps+=("postgresql-client")
    fi
    
    if ! command -v gzip &> /dev/null; then
        missing_deps+=("gzip")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        error "Missing dependencies: ${missing_deps[*]}"
        error "Please install the missing dependencies and try again."
        exit 1
    fi
    
    success "All dependencies are available"
}

# Load environment variables
load_environment() {
    log "Loading environment variables..."
    
    if [ -f "$PROJECT_ROOT/.env" ]; then
        # Export variables from .env file
        set -a
        source "$PROJECT_ROOT/.env"
        set +a
        success "Environment variables loaded from .env"
    else
        warning ".env file not found, using system environment variables"
    fi
    
    # Validate required environment variables
    if [ -z "${DATABASE_URL:-}" ]; then
        error "DATABASE_URL environment variable is required"
        exit 1
    fi
}

# List available backup files
list_backups() {
    log "Available backup files in $BACKUP_DIR:"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        warning "Backup directory does not exist: $BACKUP_DIR"
        return
    fi
    
    local backup_files=()
    while IFS= read -r -d '' file; do
        backup_files+=("$file")
    done < <(find "$BACKUP_DIR" -name "*backup_*.sql*" -type f -print0 | sort -z)
    
    if [ ${#backup_files[@]} -eq 0 ]; then
        warning "No backup files found in $BACKUP_DIR"
        return
    fi
    
    echo ""
    printf "%-40s %-15s %-20s\n" "Filename" "Size" "Modified"
    printf "%-40s %-15s %-20s\n" "--------" "----" "--------"
    
    for file in "${backup_files[@]}"; do
        local filename=$(basename "$file")
        local size=$(du -h "$file" | cut -f1)
        local modified=$(stat -c "%y" "$file" | cut -d' ' -f1,2 | cut -d'.' -f1)
        printf "%-40s %-15s %-20s\n" "$filename" "$size" "$modified"
    done
    echo ""
}

# Verify backup file integrity
verify_backup() {
    local backup_file="$1"
    
    log "Verifying backup file: $(basename "$backup_file")"
    
    if [ ! -f "$backup_file" ]; then
        error "Backup file does not exist: $backup_file"
        return 1
    fi
    
    if [[ "$backup_file" == *.gz ]]; then
        if gzip -t "$backup_file"; then
            success "Backup file integrity verified"
        else
            error "Backup file is corrupted: $backup_file"
            return 1
        fi
    else
        # For uncompressed SQL files, check if it's readable
        if [ -r "$backup_file" ]; then
            success "Backup file is readable"
        else
            error "Cannot read backup file: $backup_file"
            return 1
        fi
    fi
    
    # Check file size
    local file_size=$(stat -c%s "$backup_file")
    if [ "$file_size" -eq 0 ]; then
        error "Backup file is empty: $backup_file"
        return 1
    fi
    
    log "Backup file size: $(du -h "$backup_file" | cut -f1)"
    return 0
}

# Get database connection info
get_db_info() {
    local db_url="$DATABASE_URL"
    
    # Extract database name from URL
    local db_name=$(echo "$db_url" | sed -n 's|.*://[^/]*/\([^?]*\).*|\1|p')
    
    if [ -z "$db_name" ]; then
        error "Could not extract database name from DATABASE_URL"
        exit 1
    fi
    
    echo "$db_name"
}

# Create database backup before restore
create_pre_restore_backup() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="$BACKUP_DIR/pre_restore_backup_$timestamp.sql.gz"
    
    log "Creating pre-restore backup: $(basename "$backup_file")"
    
    if pg_dump "$DATABASE_URL" | gzip > "$backup_file"; then
        success "Pre-restore backup created: $backup_file"
        echo "$backup_file"
    else
        error "Failed to create pre-restore backup"
        exit 1
    fi
}

# Restore database from backup
restore_database() {
    local backup_file="$1"
    local force="${2:-false}"
    local dry_run="${3:-false}"
    
    log "Preparing to restore database from: $(basename "$backup_file")"
    
    # Verify backup file
    if ! verify_backup "$backup_file"; then
        error "Backup verification failed"
        exit 1
    fi
    
    # Get database info
    local db_name
    db_name=$(get_db_info)
    log "Target database: $db_name"
    
    if [ "$dry_run" = "true" ]; then
        log "DRY RUN: Would restore $backup_file to database $db_name"
        return 0
    fi
    
    # Confirmation prompt
    if [ "$force" != "true" ]; then
        echo ""
        warning "This will REPLACE all data in the database: $db_name"
        warning "Make sure you have a backup of the current database!"
        echo ""
        read -p "Are you sure you want to continue? (yes/no): " -r
        if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
            log "Restore cancelled by user"
            exit 0
        fi
    fi
    
    # Create pre-restore backup
    local pre_restore_backup
    pre_restore_backup=$(create_pre_restore_backup)
    
    # Determine restore method based on file extension
    if [[ "$backup_file" == *.gz ]]; then
        log "Restoring from compressed backup..."
        if gunzip -c "$backup_file" | psql "$DATABASE_URL"; then
            success "Database restored successfully from compressed backup"
        else
            error "Failed to restore from compressed backup"
            warning "Pre-restore backup available at: $pre_restore_backup"
            exit 1
        fi
    else
        log "Restoring from uncompressed backup..."
        if psql "$DATABASE_URL" < "$backup_file"; then
            success "Database restored successfully from backup"
        else
            error "Failed to restore from backup"
            warning "Pre-restore backup available at: $pre_restore_backup"
            exit 1
        fi
    fi
    
    log "Pre-restore backup saved at: $pre_restore_backup"
}

# Validate restored database
validate_restore() {
    log "Validating restored database..."
    
    # Check if we can connect to the database
    if psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
        success "Database connection successful"
    else
        error "Cannot connect to restored database"
        return 1
    fi
    
    # Check for basic tables (adjust based on your schema)
    local tables=("Shop" "InvoiceBalance" "ShippingCost" "ProcessedWebhook")
    local missing_tables=()
    
    for table in "${tables[@]}"; do
        if ! psql "$DATABASE_URL" -c "SELECT 1 FROM \"$table\" LIMIT 1;" > /dev/null 2>&1; then
            missing_tables+=("$table")
        fi
    done
    
    if [ ${#missing_tables[@]} -eq 0 ]; then
        success "All expected tables are present"
    else
        warning "Missing tables: ${missing_tables[*]}"
    fi
    
    # Get record counts
    log "Database statistics after restore:"
    for table in "${tables[@]}"; do
        if psql "$DATABASE_URL" -c "SELECT 1 FROM \"$table\" LIMIT 1;" > /dev/null 2>&1; then
            local count=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM \"$table\";")
            log "  $table: $count records"
        fi
    done
}

# Main function
main() {
    local backup_file=""
    local force=false
    local verify_only=false
    local dry_run=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_usage
                exit 0
                ;;
            --list|-l)
                load_environment
                list_backups
                exit 0
                ;;
            --verify|-v)
                verify_only=true
                shift
                ;;
            --force|-f)
                force=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            --target-db)
                # This would require additional implementation
                warning "Target database option not yet implemented"
                shift 2
                ;;
            -*)
                error "Unknown option: $1"
                show_usage
                exit 1
                ;;
            *)
                if [ -z "$backup_file" ]; then
                    backup_file="$1"
                else
                    error "Multiple backup files specified"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # Check if backup file is specified
    if [ -z "$backup_file" ]; then
        error "No backup file specified"
        show_usage
        exit 1
    fi
    
    # Resolve backup file path
    if [[ "$backup_file" != /* ]]; then
        # Relative path, check in backup directory first
        if [ -f "$BACKUP_DIR/$backup_file" ]; then
            backup_file="$BACKUP_DIR/$backup_file"
        elif [ -f "$backup_file" ]; then
            backup_file="$(realpath "$backup_file")"
        else
            error "Backup file not found: $backup_file"
            exit 1
        fi
    fi
    
    # Check dependencies
    check_dependencies
    
    # Load environment
    load_environment
    
    if [ "$verify_only" = "true" ]; then
        verify_backup "$backup_file"
        exit 0
    fi
    
    # Perform restore
    restore_database "$backup_file" "$force" "$dry_run"
    
    if [ "$dry_run" != "true" ]; then
        # Validate restore
        validate_restore
        
        success "Database restore completed successfully!"
        log "Backup file: $backup_file"
        log "Target database: $(get_db_info)"
    fi
}

# Run main function with all arguments
main "$@"
