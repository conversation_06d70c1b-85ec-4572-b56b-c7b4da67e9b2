#!/bin/bash

# Automated Backup Script for ATP Invoice Preview Application
# This script creates backups of the database and important application data

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
BACKUP_DIR="${BACKUP_DIR:-$PROJECT_ROOT/backups}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RETENTION_DAYS="${RETENTION_DAYS:-30}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log "Checking dependencies..."
    
    local missing_deps=()
    
    if ! command -v pg_dump &> /dev/null; then
        missing_deps+=("postgresql-client")
    fi
    
    if ! command -v gzip &> /dev/null; then
        missing_deps+=("gzip")
    fi
    
    if ! command -v aws &> /dev/null && [ "${BACKUP_TO_S3:-false}" = "true" ]; then
        missing_deps+=("awscli")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        error "Missing dependencies: ${missing_deps[*]}"
        error "Please install the missing dependencies and try again."
        exit 1
    fi
    
    success "All dependencies are available"
}

# Create backup directory
create_backup_dir() {
    log "Creating backup directory: $BACKUP_DIR"
    mkdir -p "$BACKUP_DIR"
    
    if [ ! -w "$BACKUP_DIR" ]; then
        error "Backup directory is not writable: $BACKUP_DIR"
        exit 1
    fi
    
    success "Backup directory ready"
}

# Load environment variables
load_environment() {
    log "Loading environment variables..."
    
    if [ -f "$PROJECT_ROOT/.env" ]; then
        # Export variables from .env file
        set -a
        source "$PROJECT_ROOT/.env"
        set +a
        success "Environment variables loaded from .env"
    else
        warning ".env file not found, using system environment variables"
    fi
    
    # Validate required environment variables
    if [ -z "${DATABASE_URL:-}" ]; then
        error "DATABASE_URL environment variable is required"
        exit 1
    fi
}

# Backup database
backup_database() {
    log "Starting database backup..."
    
    local backup_file="$BACKUP_DIR/database_backup_$TIMESTAMP.sql"
    local compressed_file="$backup_file.gz"
    
    # Extract database connection details from DATABASE_URL
    local db_url="$DATABASE_URL"
    
    log "Creating database dump..."
    if pg_dump "$db_url" > "$backup_file"; then
        success "Database dump created: $backup_file"
        
        # Compress the backup
        log "Compressing database backup..."
        if gzip "$backup_file"; then
            success "Database backup compressed: $compressed_file"
            
            # Get file size
            local file_size=$(du -h "$compressed_file" | cut -f1)
            log "Compressed backup size: $file_size"
            
            echo "$compressed_file" # Return the compressed file path
        else
            error "Failed to compress database backup"
            exit 1
        fi
    else
        error "Failed to create database dump"
        exit 1
    fi
}

# Backup application files
backup_application_files() {
    log "Starting application files backup..."
    
    local backup_file="$BACKUP_DIR/app_files_backup_$TIMESTAMP.tar.gz"
    
    # Files and directories to backup
    local backup_items=(
        ".env.example"
        "package.json"
        "package-lock.json"
        "prisma/schema.prisma"
        "app/pricingData"
        "docs"
        "infrastructure"
        "scripts"
    )
    
    # Create list of existing items
    local existing_items=()
    for item in "${backup_items[@]}"; do
        if [ -e "$PROJECT_ROOT/$item" ]; then
            existing_items+=("$item")
        else
            warning "Backup item not found: $item"
        fi
    done
    
    if [ ${#existing_items[@]} -eq 0 ]; then
        warning "No application files found to backup"
        return
    fi
    
    log "Backing up application files: ${existing_items[*]}"
    
    # Create tar archive
    if (cd "$PROJECT_ROOT" && tar -czf "$backup_file" "${existing_items[@]}"); then
        success "Application files backup created: $backup_file"
        
        # Get file size
        local file_size=$(du -h "$backup_file" | cut -f1)
        log "Application backup size: $file_size"
        
        echo "$backup_file" # Return the backup file path
    else
        error "Failed to create application files backup"
        exit 1
    fi
}

# Upload to S3 (optional)
upload_to_s3() {
    local file_path="$1"
    
    if [ "${BACKUP_TO_S3:-false}" != "true" ]; then
        return
    fi
    
    if [ -z "${S3_BUCKET:-}" ]; then
        warning "S3_BUCKET not set, skipping S3 upload"
        return
    fi
    
    log "Uploading backup to S3: $S3_BUCKET"
    
    local s3_key="backups/$(basename "$file_path")"
    
    if aws s3 cp "$file_path" "s3://$S3_BUCKET/$s3_key"; then
        success "Backup uploaded to S3: s3://$S3_BUCKET/$s3_key"
    else
        error "Failed to upload backup to S3"
        return 1
    fi
}

# Clean old backups
cleanup_old_backups() {
    log "Cleaning up backups older than $RETENTION_DAYS days..."
    
    local deleted_count=0
    
    # Find and delete old backup files
    while IFS= read -r -d '' file; do
        log "Deleting old backup: $(basename "$file")"
        rm "$file"
        ((deleted_count++))
    done < <(find "$BACKUP_DIR" -name "*backup_*.sql.gz" -o -name "*backup_*.tar.gz" -type f -mtime +$RETENTION_DAYS -print0)
    
    if [ $deleted_count -gt 0 ]; then
        success "Deleted $deleted_count old backup files"
    else
        log "No old backup files to delete"
    fi
}

# Verify backup integrity
verify_backup() {
    local backup_file="$1"
    
    log "Verifying backup integrity: $(basename "$backup_file")"
    
    if [[ "$backup_file" == *.gz ]]; then
        if gzip -t "$backup_file"; then
            success "Backup file integrity verified"
        else
            error "Backup file is corrupted: $backup_file"
            return 1
        fi
    elif [[ "$backup_file" == *.tar.gz ]]; then
        if tar -tzf "$backup_file" > /dev/null; then
            success "Backup archive integrity verified"
        else
            error "Backup archive is corrupted: $backup_file"
            return 1
        fi
    fi
}

# Generate backup report
generate_report() {
    local db_backup="$1"
    local app_backup="$2"
    
    local report_file="$BACKUP_DIR/backup_report_$TIMESTAMP.txt"
    
    cat > "$report_file" << EOF
ATP Invoice Preview - Backup Report
Generated: $(date)
Timestamp: $TIMESTAMP

Database Backup:
- File: $(basename "$db_backup")
- Size: $(du -h "$db_backup" | cut -f1)
- Path: $db_backup

Application Files Backup:
- File: $(basename "$app_backup")
- Size: $(du -h "$app_backup" | cut -f1)
- Path: $app_backup

Backup Directory: $BACKUP_DIR
Retention Policy: $RETENTION_DAYS days

Status: SUCCESS
EOF
    
    success "Backup report generated: $report_file"
    
    # Display report
    log "Backup Summary:"
    cat "$report_file"
}

# Main backup function
main() {
    log "Starting ATP Invoice Preview backup process..."
    
    # Check dependencies
    check_dependencies
    
    # Load environment
    load_environment
    
    # Create backup directory
    create_backup_dir
    
    # Perform backups
    local db_backup
    local app_backup
    
    db_backup=$(backup_database)
    app_backup=$(backup_application_files)
    
    # Verify backups
    verify_backup "$db_backup"
    verify_backup "$app_backup"
    
    # Upload to S3 if configured
    upload_to_s3 "$db_backup"
    upload_to_s3 "$app_backup"
    
    # Clean old backups
    cleanup_old_backups
    
    # Generate report
    generate_report "$db_backup" "$app_backup"
    
    success "Backup process completed successfully!"
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --verify FILE  Verify backup file integrity"
        echo ""
        echo "Environment Variables:"
        echo "  DATABASE_URL      Database connection string (required)"
        echo "  BACKUP_DIR        Backup directory (default: ./backups)"
        echo "  RETENTION_DAYS    Days to keep backups (default: 30)"
        echo "  BACKUP_TO_S3      Upload to S3 (default: false)"
        echo "  S3_BUCKET         S3 bucket name (required if BACKUP_TO_S3=true)"
        exit 0
        ;;
    --verify)
        if [ -z "${2:-}" ]; then
            error "Please specify a backup file to verify"
            exit 1
        fi
        verify_backup "$2"
        exit 0
        ;;
    "")
        main
        ;;
    *)
        error "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac
