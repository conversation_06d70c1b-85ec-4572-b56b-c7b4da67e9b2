#!/bin/bash

# Deployment script for Americans United Inc Shopify App
# Handles Fly.io deployment with proper validation and rollback capabilities

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
LOG_DIR="$PROJECT_ROOT/logs/deployments"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$LOG_DIR/deploy_${TIMESTAMP}.log"

# Logging functions
log() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] $1"
    echo -e "${BLUE}${message}${NC}"
    echo "$message" >> "$LOG_FILE"
}

error() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1"
    echo -e "${RED}${message}${NC}" >&2
    echo "$message" >> "$LOG_FILE"
}

success() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS: $1"
    echo -e "${GREEN}${message}${NC}"
    echo "$message" >> "$LOG_FILE"
}

warn() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1"
    echo -e "${YELLOW}${message}${NC}"
    echo "$message" >> "$LOG_FILE"
}

# Function to check prerequisites
check_prerequisites() {
    log "Checking deployment prerequisites..."
    
    # Check if flyctl is installed
    if ! command -v flyctl &> /dev/null; then
        error "flyctl is not installed. Please install it first:"
        error "  curl -L https://fly.io/install.sh | sh"
        return 1
    fi
    
    # Check if we're in the right directory
    if [ ! -f "$PROJECT_ROOT/package.json" ]; then
        error "Not in the correct project directory. Please run from the project root."
        return 1
    fi
    
    # Check if fly.toml exists
    if [ ! -f "$PROJECT_ROOT/fly.toml" ]; then
        error "fly.toml not found. Please create the Fly.io configuration file."
        return 1
    fi
    
    # Check if Dockerfile.multiprocess exists
    if [ ! -f "$PROJECT_ROOT/Dockerfile.multiprocess" ]; then
        error "Dockerfile.multiprocess not found. Please create the multi-process Dockerfile."
        return 1
    fi
    
    # Check if logged into Fly.io
    if ! flyctl auth whoami &> /dev/null; then
        error "Not logged into Fly.io. Please run: flyctl auth login"
        return 1
    fi
    
    # Create log directory
    mkdir -p "$LOG_DIR"
    
    success "Prerequisites check passed"
    return 0
}

# Function to validate environment variables
validate_environment() {
    log "Validating environment variables..."
    
    local required_secrets=(
        "DATABASE_URL"
        "SESSION_SECRET"
        "SHOPIFY_API_KEY_STYLISH_STITCHES_JORDIN_KOLMAN"
        "SHOPIFY_API_SECRET_STYLISH_STITCHES_JORDIN_KOLMAN"
        "SHIPSTATION_API_KEY"
        "SHIPSTATION_API_SECRET"
    )
    
    local missing_secrets=()
    
    for secret in "${required_secrets[@]}"; do
        if ! flyctl secrets list | grep -q "^$secret"; then
            missing_secrets+=("$secret")
        fi
    done
    
    if [ ${#missing_secrets[@]} -gt 0 ]; then
        error "Missing required secrets:"
        for secret in "${missing_secrets[@]}"; do
            error "  - $secret"
        done
        error "Please set missing secrets using: flyctl secrets set SECRET_NAME=value"
        return 1
    fi
    
    success "Environment validation passed"
    return 0
}

# Function to run pre-deployment tests
run_tests() {
    log "Running pre-deployment tests..."
    
    cd "$PROJECT_ROOT"
    
    # Run linting
    if ! npm run lint; then
        error "Linting failed"
        return 1
    fi
    
    # Run unit tests
    if ! npm test; then
        error "Unit tests failed"
        return 1
    fi
    
    # Run build to ensure it works
    if ! npm run build; then
        error "Build failed"
        return 1
    fi
    
    success "Pre-deployment tests passed"
    return 0
}

# Function to deploy to Fly.io
deploy_to_fly() {
    log "Deploying to Fly.io..."
    
    cd "$PROJECT_ROOT"
    
    # Deploy with no-cache to ensure fresh build
    if flyctl deploy --dockerfile Dockerfile.multiprocess --no-cache; then
        success "Deployment to Fly.io completed successfully"
        return 0
    else
        error "Deployment to Fly.io failed"
        return 1
    fi
}

# Function to run post-deployment health checks
run_health_checks() {
    log "Running post-deployment health checks..."
    
    local app_url=$(flyctl info --json | jq -r '.Hostname')
    if [ "$app_url" = "null" ] || [ -z "$app_url" ]; then
        error "Could not determine app URL"
        return 1
    fi
    
    local max_attempts=30
    local attempt=1
    
    # Check Remix health endpoint
    log "Checking Remix server health..."
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "https://$app_url/healthz" > /dev/null; then
            success "Remix server is healthy"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            error "Remix server health check failed after $max_attempts attempts"
            return 1
        fi
        
        warn "Remix health check attempt $attempt/$max_attempts failed, retrying in 10 seconds..."
        sleep 10
        attempt=$((attempt + 1))
    done
    
    # Check Express health endpoint
    log "Checking Express server health..."
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "https://$app_url:4000/express/health" > /dev/null; then
            success "Express server is healthy"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            error "Express server health check failed after $max_attempts attempts"
            return 1
        fi
        
        warn "Express health check attempt $attempt/$max_attempts failed, retrying in 10 seconds..."
        sleep 10
        attempt=$((attempt + 1))
    done
    
    success "All health checks passed"
    return 0
}

# Function to show deployment summary
show_summary() {
    local app_url=$(flyctl info --json | jq -r '.Hostname')
    
    success "Deployment completed successfully!"
    log ""
    log "Application URLs:"
    log "  Remix App: https://$app_url"
    log "  Express API: https://$app_url:4000"
    log ""
    log "Useful commands:"
    log "  View logs: flyctl logs"
    log "  Check status: flyctl status"
    log "  Scale app: flyctl scale count 2"
    log "  SSH into app: flyctl ssh console"
    log ""
    log "Deployment log saved to: $LOG_FILE"
}

# Main deployment function
main() {
    log "Starting deployment of Americans United Inc Shopify App..."
    log "Timestamp: $TIMESTAMP"
    log "Log file: $LOG_FILE"
    
    # Run all deployment steps
    if ! check_prerequisites; then
        error "Prerequisites check failed"
        exit 1
    fi
    
    if ! validate_environment; then
        error "Environment validation failed"
        exit 1
    fi
    
    if ! run_tests; then
        error "Pre-deployment tests failed"
        exit 1
    fi
    
    if ! deploy_to_fly; then
        error "Deployment failed"
        exit 1
    fi
    
    if ! run_health_checks; then
        error "Post-deployment health checks failed"
        warn "Deployment may have succeeded but health checks failed"
        warn "Please check the application manually"
        exit 1
    fi
    
    show_summary
}

# Handle script arguments
case "${1:-}" in
    "--skip-tests")
        log "Skipping pre-deployment tests as requested"
        run_tests() { return 0; }
        shift
        ;;
    "--help"|"-h")
        echo "Usage: $0 [--skip-tests] [--help]"
        echo ""
        echo "Options:"
        echo "  --skip-tests    Skip pre-deployment tests"
        echo "  --help, -h      Show this help message"
        exit 0
        ;;
esac

# Run main function
main "$@"
