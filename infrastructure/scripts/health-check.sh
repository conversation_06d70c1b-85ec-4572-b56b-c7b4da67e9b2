#!/bin/bash

# Health check script for Americans United Inc Shopify App
# Used by Docker healthcheck and deployment validation

set -e

# Configuration
REMIX_PORT=${PORT:-3000}
EXPRESS_PORT=${EXPRESS_PORT:-4000}
TIMEOUT=${HEALTH_CHECK_TIMEOUT:-5}
MAX_RETRIES=${HEALTH_CHECK_RETRIES:-3}

# Colors for output (only if running in terminal)
if [ -t 1 ]; then
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    BLUE='\033[0;34m'
    NC='\033[0m' # No Color
else
    RED=''
    GREEN=''
    YELLOW=''
    BLUE=''
    NC=''
fi

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# Function to check if a service is responding
check_service() {
    local service_name="$1"
    local url="$2"
    local expected_status="${3:-200}"

    log "Checking $service_name at $url..."

    local attempt=1
    while [ $attempt -le $MAX_RETRIES ]; do
        # Use curl with timeout and follow redirects
        local response=$(curl -s -w "%{http_code}" -o /dev/null --max-time $TIMEOUT "$url" 2>/dev/null || echo "000")

        if [ "$response" = "$expected_status" ]; then
            success "$service_name is healthy (HTTP $response)"
            return 0
        fi

        if [ $attempt -lt $MAX_RETRIES ]; then
            warn "$service_name check attempt $attempt/$MAX_RETRIES failed (HTTP $response), retrying..."
            sleep 1
        else
            error "$service_name is unhealthy (HTTP $response) after $MAX_RETRIES attempts"
            return 1
        fi

        attempt=$((attempt + 1))
    done
}

# Function to check database connectivity
check_database() {
    log "Checking database connectivity..."

    # Check if DATABASE_URL is set
    if [ -z "$DATABASE_URL" ]; then
        error "DATABASE_URL environment variable is not set"
        return 1
    fi

    # Try to connect to database using a simple query
    if command -v node >/dev/null 2>&1; then
        # Use Node.js to check database connection
        local db_check_result=$(node -e "
            const { PrismaClient } = require('@prisma/client');
            const prisma = new PrismaClient();
            prisma.\$queryRaw\`SELECT 1\`
                .then(() => { console.log('OK'); process.exit(0); })
                .catch((e) => { console.error('FAIL:', e.message); process.exit(1); })
                .finally(() => prisma.\$disconnect());
        " 2>&1)

        if echo "$db_check_result" | grep -q "OK"; then
            success "Database is accessible"
            return 0
        else
            error "Database connection failed: $db_check_result"
            return 1
        fi
    else
        warn "Node.js not available, skipping database connectivity check"
        return 0
    fi
}

# Function to check system resources
check_system_resources() {
    log "Checking system resources..."

    # Check memory usage
    if command -v free >/dev/null 2>&1; then
        local memory_usage=$(free | awk 'NR==2{printf "%.1f", $3*100/$2}')
        log "Memory usage: ${memory_usage}%"

        # Warn if memory usage is high
        if (( $(echo "$memory_usage > 90" | bc -l) )); then
            warn "High memory usage detected: ${memory_usage}%"
        fi
    fi

    # Check disk usage
    if command -v df >/dev/null 2>&1; then
        local disk_usage=$(df / | awk 'NR==2{print $5}' | sed 's/%//')
        log "Disk usage: ${disk_usage}%"

        # Warn if disk usage is high
        if [ "$disk_usage" -gt 90 ]; then
            warn "High disk usage detected: ${disk_usage}%"
        fi
    fi

    success "System resources check completed"
    return 0
}

# Function to check process-specific health
check_process_health() {
    local process_type="${FLY_PROCESS_GROUP:-both}"

    case "$process_type" in
        "web")
            check_service "Remix Web Server" "http://localhost:$REMIX_PORT/healthz"
            ;;
        "multistore")
            check_service "Express Multistore Server" "http://localhost:$EXPRESS_PORT/express/health"
            ;;
        "remix")
            check_service "Remix Server" "http://localhost:$REMIX_PORT/healthz"
            ;;
        "express")
            check_service "Express Server" "http://localhost:$EXPRESS_PORT/express/health"
            ;;
        "app"|"both"|*)
            local web_healthy=true
            local multistore_healthy=true

            log "Checking both Web and Multistore servers..."

            if ! check_service "Remix Web Server" "http://localhost:$REMIX_PORT/healthz"; then
                web_healthy=false
            fi

            if ! check_service "Express Multistore Server" "http://localhost:$EXPRESS_PORT/express/health"; then
                multistore_healthy=false
            fi

            if [ "$web_healthy" = true ] && [ "$multistore_healthy" = true ]; then
                success "Both multistore servers are healthy"
                return 0
            else
                error "One or more multistore servers are unhealthy (Web: $web_healthy, Multistore: $multistore_healthy)"
                return 1
            fi
            ;;
    esac
}

# Main health check function
main() {
    local exit_code=0

    log "Starting health check for Americans United Inc Shopify App..."
    log "Process type: ${FLY_PROCESS_GROUP:-both}"
    log "Remix port: $REMIX_PORT"
    log "Express port: $EXPRESS_PORT"
    log "Timeout: ${TIMEOUT}s"
    log "Max retries: $MAX_RETRIES"

    # Check system resources (non-critical)
    check_system_resources || true

    # Check database connectivity (critical for app functionality)
    if ! check_database; then
        error "Database health check failed"
        exit_code=1
    fi

    # Check process-specific health endpoints
    if ! check_process_health; then
        error "Process health check failed"
        exit_code=1
    fi

    if [ $exit_code -eq 0 ]; then
        success "All health checks passed"
    else
        error "One or more health checks failed"
    fi

    exit $exit_code
}

# Handle script arguments
case "${1:-}" in
    "--help"|"-h")
        echo "Usage: $0 [--help]"
        echo ""
        echo "Environment variables:"
        echo "  PORT                    Remix server port (default: 3000)"
        echo "  EXPRESS_PORT           Express server port (default: 4000)"
        echo "  HEALTH_CHECK_TIMEOUT   Request timeout in seconds (default: 5)"
        echo "  HEALTH_CHECK_RETRIES   Maximum retry attempts (default: 3)"
        echo "  FLY_PROCESS_GROUP      Process type to check (remix|express|both)"
        echo ""
        echo "Exit codes:"
        echo "  0  All health checks passed"
        echo "  1  One or more health checks failed"
        exit 0
        ;;
esac

# Run main function
main "$@"
