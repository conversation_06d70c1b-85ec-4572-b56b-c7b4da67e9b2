/**
 * Tests for health-check.sh script
 * 
 * These tests validate the health check script functionality
 * by mocking HTTP responses and testing different scenarios.
 */

const { spawn } = require('child_process');
const path = require('path');
const http = require('http');

describe('Health Check Script', () => {
  const scriptPath = path.join(__dirname, 'health-check.sh');
  let mockRemixServer;
  let mockExpressServer;
  
  const REMIX_PORT = 3001;
  const EXPRESS_PORT = 4001;
  
  beforeAll(() => {
    // Set test environment variables
    process.env.PORT = REMIX_PORT.toString();
    process.env.EXPRESS_PORT = EXPRESS_PORT.toString();
    process.env.HEALTH_CHECK_TIMEOUT = '2';
    process.env.HEALTH_CHECK_RETRIES = '2';
  });
  
  afterAll(() => {
    // Clean up environment variables
    delete process.env.PORT;
    delete process.env.EXPRESS_PORT;
    delete process.env.HEALTH_CHECK_TIMEOUT;
    delete process.env.HEALTH_CHECK_RETRIES;
  });
  
  beforeEach(() => {
    // Stop any existing servers
    if (mockRemixServer) {
      mockRemixServer.close();
      mockRemixServer = null;
    }
    if (mockExpressServer) {
      mockExpressServer.close();
      mockExpressServer = null;
    }
  });
  
  afterEach(() => {
    // Clean up servers after each test
    if (mockRemixServer) {
      mockRemixServer.close();
      mockRemixServer = null;
    }
    if (mockExpressServer) {
      mockExpressServer.close();
      mockExpressServer = null;
    }
  });
  
  /**
   * Helper function to create a mock server
   */
  const createMockServer = (port, path, statusCode = 200, response = 'OK') => {
    return new Promise((resolve) => {
      const server = http.createServer((req, res) => {
        if (req.url === path) {
          res.writeHead(statusCode, { 'Content-Type': 'text/plain' });
          res.end(response);
        } else {
          res.writeHead(404);
          res.end('Not Found');
        }
      });
      
      server.listen(port, () => {
        resolve(server);
      });
    });
  };
  
  /**
   * Helper function to run the health check script
   */
  const runHealthCheck = (env = {}) => {
    return new Promise((resolve) => {
      const child = spawn('bash', [scriptPath], {
        env: { ...process.env, ...env },
        stdio: 'pipe'
      });
      
      let stdout = '';
      let stderr = '';
      
      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });
      
      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });
      
      child.on('close', (code) => {
        resolve({
          exitCode: code,
          stdout,
          stderr
        });
      });
    });
  };
  
  describe('Successful Health Checks', () => {
    test('should pass when both servers are healthy', async () => {
      // Start mock servers
      mockRemixServer = await createMockServer(REMIX_PORT, '/healthz');
      mockExpressServer = await createMockServer(EXPRESS_PORT, '/express/health');
      
      const result = await runHealthCheck({
        FLY_PROCESS_GROUP: 'both',
        DATABASE_URL: 'postgresql://test:test@localhost:5432/test'
      });
      
      expect(result.exitCode).toBe(0);
      expect(result.stdout).toContain('Remix Server is healthy');
      expect(result.stdout).toContain('Express Server is healthy');
      expect(result.stdout).toContain('All health checks passed');
    });
    
    test('should pass when only Remix server is checked and healthy', async () => {
      mockRemixServer = await createMockServer(REMIX_PORT, '/healthz');
      
      const result = await runHealthCheck({
        FLY_PROCESS_GROUP: 'remix',
        DATABASE_URL: 'postgresql://test:test@localhost:5432/test'
      });
      
      expect(result.exitCode).toBe(0);
      expect(result.stdout).toContain('Remix Server is healthy');
      expect(result.stdout).not.toContain('Express Server');
    });
    
    test('should pass when only Express server is checked and healthy', async () => {
      mockExpressServer = await createMockServer(EXPRESS_PORT, '/express/health');
      
      const result = await runHealthCheck({
        FLY_PROCESS_GROUP: 'express',
        DATABASE_URL: 'postgresql://test:test@localhost:5432/test'
      });
      
      expect(result.exitCode).toBe(0);
      expect(result.stdout).toContain('Express Server is healthy');
      expect(result.stdout).not.toContain('Remix Server');
    });
  });
  
  describe('Failed Health Checks', () => {
    test('should fail when Remix server is not responding', async () => {
      // Start only Express server
      mockExpressServer = await createMockServer(EXPRESS_PORT, '/express/health');
      
      const result = await runHealthCheck({
        FLY_PROCESS_GROUP: 'both',
        DATABASE_URL: 'postgresql://test:test@localhost:5432/test'
      });
      
      expect(result.exitCode).toBe(1);
      expect(result.stderr).toContain('Remix Server is unhealthy');
    });
    
    test('should fail when Express server is not responding', async () => {
      // Start only Remix server
      mockRemixServer = await createMockServer(REMIX_PORT, '/healthz');
      
      const result = await runHealthCheck({
        FLY_PROCESS_GROUP: 'both',
        DATABASE_URL: 'postgresql://test:test@localhost:5432/test'
      });
      
      expect(result.exitCode).toBe(1);
      expect(result.stderr).toContain('Express Server is unhealthy');
    });
    
    test('should fail when server returns non-200 status', async () => {
      mockRemixServer = await createMockServer(REMIX_PORT, '/healthz', 500, 'Internal Server Error');
      
      const result = await runHealthCheck({
        FLY_PROCESS_GROUP: 'remix',
        DATABASE_URL: 'postgresql://test:test@localhost:5432/test'
      });
      
      expect(result.exitCode).toBe(1);
      expect(result.stderr).toContain('Remix Server is unhealthy (HTTP 500)');
    });
    
    test('should fail when DATABASE_URL is not set', async () => {
      mockRemixServer = await createMockServer(REMIX_PORT, '/healthz');
      
      const result = await runHealthCheck({
        FLY_PROCESS_GROUP: 'remix'
        // DATABASE_URL intentionally not set
      });
      
      expect(result.exitCode).toBe(1);
      expect(result.stderr).toContain('DATABASE_URL environment variable is not set');
    });
  });
  
  describe('Script Arguments', () => {
    test('should show help when --help is passed', async () => {
      const child = spawn('bash', [scriptPath, '--help'], {
        stdio: 'pipe'
      });
      
      let stdout = '';
      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });
      
      const exitCode = await new Promise((resolve) => {
        child.on('close', resolve);
      });
      
      expect(exitCode).toBe(0);
      expect(stdout).toContain('Usage:');
      expect(stdout).toContain('Environment variables:');
      expect(stdout).toContain('Exit codes:');
    });
  });
  
  describe('Retry Logic', () => {
    test('should retry failed requests up to max retries', async () => {
      // Server that fails first request but succeeds on second
      let requestCount = 0;
      const server = http.createServer((req, res) => {
        requestCount++;
        if (req.url === '/healthz') {
          if (requestCount === 1) {
            res.writeHead(500);
            res.end('First request fails');
          } else {
            res.writeHead(200);
            res.end('OK');
          }
        } else {
          res.writeHead(404);
          res.end('Not Found');
        }
      });
      
      mockRemixServer = await new Promise((resolve) => {
        server.listen(REMIX_PORT, () => resolve(server));
      });
      
      const result = await runHealthCheck({
        FLY_PROCESS_GROUP: 'remix',
        DATABASE_URL: 'postgresql://test:test@localhost:5432/test'
      });
      
      expect(result.exitCode).toBe(0);
      expect(result.stdout).toContain('Remix Server is healthy');
      expect(requestCount).toBe(2); // Should have retried once
    });
  });
});
