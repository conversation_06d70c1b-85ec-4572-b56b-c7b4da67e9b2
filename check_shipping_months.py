import csv
from datetime import datetime

# Check what months the shipments should be assigned to
shipment_months = {}

with open('/home/<USER>/workspace/shopify-apps/americans-united-inc/test-data/atp-shipments-5-25.csv', 'r') as file:
    reader = csv.DictReader(file)
    for row in reader:
        # Get the shipped date
        shipped_date_str = row['Date - Shipped Date']
        if shipped_date_str:
            try:
                # Parse the date
                shipped_date = datetime.strptime(shipped_date_str, '%m/%d/%Y %I:%M:%S %p')
                month = shipped_date.month - 1  # JavaScript months are 0-based
                year = shipped_date.year
                
                month_key = f"{year}-{month:02d}"
                if month_key not in shipment_months:
                    shipment_months[month_key] = {
                        'count': 0,
                        'total_carrier_fee': 0,
                        'month_name': shipped_date.strftime('%B %Y')
                    }
                
                shipment_months[month_key]['count'] += 1
                shipment_months[month_key]['total_carrier_fee'] += float(row['Carrier - Fee'])
            except ValueError as e:
                print(f"Error parsing date '{shipped_date_str}': {e}")

print("Shipments by month (JavaScript 0-based months):")
for month_key, data in sorted(shipment_months.items()):
    print(f"Month {month_key} ({data['month_name']}): {data['count']} shipments, ${data['total_carrier_fee']:.2f} total carrier fees")

print(f"\nCurrent month (June 2025) would be month 5")
print(f"May 2025 would be month 4")
print(f"April 2025 would be month 3")
