/*
  Warnings:

  - Added the required column `message` to the `Unprocessable` table without a default value. This is not possible if the table is not empty.

*/
-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Unprocessable" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "shop" TEXT NOT NULL,
    "productType" TEXT,
    "variant" TEXT,
    "sku" TEXT,
    "quantity" INTEGER,
    "orderId" TEXT,
    "date" DATETIME,
    "errorField" TEXT NOT NULL,
    "message" TEXT NOT NULL
);
INSERT INTO "new_Unprocessable" ("date", "errorField", "id", "orderId", "productType", "quantity", "shop", "sku", "variant") SELECT "date", "errorField", "id", "orderId", "productType", "quantity", "shop", "sku", "variant" FROM "Unprocessable";
DROP TABLE "Unprocessable";
ALTER TABLE "new_Unprocessable" RENAME TO "Unprocessable";
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
