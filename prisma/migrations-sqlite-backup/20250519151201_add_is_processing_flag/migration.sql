-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_SetupFlag" (
    "shop" TEXT NOT NULL PRIMARY KEY,
    "isSetup" BOOLEAN NOT NULL,
    "isProcessing" BO<PERSON>EAN NOT NULL DEFAULT false
);
INSERT INTO "new_SetupFlag" ("isSetup", "shop") SELECT "isSetup", "shop" FROM "SetupFlag";
DROP TABLE "SetupFlag";
ALTER TABLE "new_SetupFlag" RENAME TO "SetupFlag";
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
