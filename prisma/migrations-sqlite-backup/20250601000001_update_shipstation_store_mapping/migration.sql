-- Create a new table with the updated schema
CREATE TABLE "ShipStationStoreMapping_new" (
  "storeId" TEXT NOT NULL PRIMARY KEY,
  "storeName" TEXT,
  "shop" TEXT,
  "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" DATETIME NOT NULL
);

-- Copy data from the old table to the new table
INSERT INTO "ShipStationStoreMapping_new" ("storeId", "shop", "createdAt", "updatedAt")
SELECT "storeId", "shop", "createdAt", "updatedAt" FROM "ShipStationStoreMapping";

-- Drop the old table
DROP TABLE "ShipStationStoreMapping";

-- Rename the new table to the original name
ALTER TABLE "ShipStationStoreMapping_new" RENAME TO "ShipStationStoreMapping";

-- Create indexes
CREATE INDEX "ShipStationStoreMapping_shop_idx" ON "ShipStationStoreMapping"("shop");
CREATE INDEX "ShipStationStoreMapping_storeName_idx" ON "ShipStationStoreMapping"("storeName");
