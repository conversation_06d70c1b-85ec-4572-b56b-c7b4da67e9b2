/*
  Warnings:

  - You are about to alter the column `month` on the `InvoiceBalance` table. The data in that column could be lost. The data in that column will be cast from `String` to `Int`.
  - You are about to alter the column `year` on the `InvoiceBalance` table. The data in that column could be lost. The data in that column will be cast from `String` to `Int`.
  - Added the required column `quantity` to the `InvoiceBalance` table without a default value. This is not possible if the table is not empty.

*/
-- CreateTable
CREATE TABLE "Price" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "shop" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "cost" DECIMAL NOT NULL
);

-- CreateTable
CREATE TABLE "Unprocessable" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "shop" TEXT NOT NULL,
    "productType" TEXT,
    "variant" TEXT,
    "sku" TEXT,
    "quantity" INTEGER,
    "orderId" TEXT,
    "date" DATETIME,
    "errorField" TEXT NOT NULL
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_InvoiceBalance" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "shop" TEXT NOT NULL,
    "month" INTEGER NOT NULL,
    "year" INTEGER NOT NULL,
    "category" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "balance" DECIMAL NOT NULL
);
INSERT INTO "new_InvoiceBalance" ("balance", "category", "id", "month", "shop", "year") SELECT "balance", "category", "id", "month", "shop", "year" FROM "InvoiceBalance";
DROP TABLE "InvoiceBalance";
ALTER TABLE "new_InvoiceBalance" RENAME TO "InvoiceBalance";
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
