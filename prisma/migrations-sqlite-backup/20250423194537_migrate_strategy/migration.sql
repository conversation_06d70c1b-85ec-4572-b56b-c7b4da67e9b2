/*
  Warnings:

  - You are about to drop the column `orderId` on the `Unprocessable` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "InvoiceBalance" ADD COLUMN "groupId" INTEGER;

-- CreateTable
CREATE TABLE "BalanceGroup" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "shop" TEXT NOT NULL,
    "name" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "InvoiceTransaction" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "invoiceBalanceId" INTEGER NOT NULL,
    "amount" DECIMAL NOT NULL,
    "description" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "metadata" TEXT,
    CONSTRAINT "InvoiceTransaction_invoiceBalanceId_fkey" FOREIGN KEY ("invoiceBalanceId") REFERENCES "InvoiceBalance" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "ShippingCost" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "shop" TEXT NOT NULL,
    "storeId" TEXT NOT NULL,
    "month" INTEGER NOT NULL,
    "year" INTEGER NOT NULL,
    "quantity" INTEGER NOT NULL,
    "shippingCost" DECIMAL NOT NULL,
    "markupAmount" DECIMAL NOT NULL,
    "totalAmount" DECIMAL NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "ShippingTransaction" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "shippingCostId" INTEGER NOT NULL,
    "amount" DECIMAL NOT NULL,
    "shippingAmount" DECIMAL NOT NULL,
    "markupAmount" DECIMAL NOT NULL,
    "description" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "metadata" TEXT,
    CONSTRAINT "ShippingTransaction_shippingCostId_fkey" FOREIGN KEY ("shippingCostId") REFERENCES "ShippingCost" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "ShipStationStoreMapping" (
    "storeId" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "ProcessedWebhook" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "shop" TEXT NOT NULL,
    "topic" TEXT NOT NULL,
    "webhookId" TEXT NOT NULL,
    "eventId" TEXT,
    "orderId" TEXT,
    "subscriptionId" TEXT,
    "processedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "ReconciliationJob" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "shop" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "startDate" DATETIME NOT NULL,
    "endDate" DATETIME NOT NULL,
    "startedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" DATETIME,
    "ordersProcessed" INTEGER NOT NULL DEFAULT 0,
    "ordersSkipped" INTEGER NOT NULL DEFAULT 0,
    "ordersFailed" INTEGER NOT NULL DEFAULT 0,
    "error" TEXT
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Unprocessable" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "shop" TEXT NOT NULL,
    "productType" TEXT,
    "variant" TEXT,
    "sku" TEXT,
    "quantity" INTEGER,
    "date" DATETIME,
    "errorField" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "variantId" TEXT,
    "productId" TEXT
);
INSERT INTO "new_Unprocessable" ("date", "errorField", "id", "message", "productType", "quantity", "shop", "sku", "variant") SELECT "date", "errorField", "id", "message", "productType", "quantity", "shop", "sku", "variant" FROM "Unprocessable";
DROP TABLE "Unprocessable";
ALTER TABLE "new_Unprocessable" RENAME TO "Unprocessable";
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;

-- CreateIndex
CREATE INDEX "InvoiceTransaction_invoiceBalanceId_idx" ON "InvoiceTransaction"("invoiceBalanceId");

-- CreateIndex
CREATE INDEX "ShippingCost_shop_storeId_idx" ON "ShippingCost"("shop", "storeId");

-- CreateIndex
CREATE INDEX "ShippingCost_shop_month_year_idx" ON "ShippingCost"("shop", "month", "year");

-- CreateIndex
CREATE INDEX "ShippingTransaction_shippingCostId_idx" ON "ShippingTransaction"("shippingCostId");

-- CreateIndex
CREATE INDEX "ShipStationStoreMapping_shop_idx" ON "ShipStationStoreMapping"("shop");

-- CreateIndex
CREATE INDEX "ProcessedWebhook_shop_orderId_idx" ON "ProcessedWebhook"("shop", "orderId");

-- CreateIndex
CREATE UNIQUE INDEX "ProcessedWebhook_shop_webhookId_key" ON "ProcessedWebhook"("shop", "webhookId");

-- CreateIndex
CREATE UNIQUE INDEX "ProcessedWebhook_shop_eventId_key" ON "ProcessedWebhook"("shop", "eventId");

-- CreateIndex
CREATE INDEX "ReconciliationJob_shop_status_idx" ON "ReconciliationJob"("shop", "status");

-- CreateIndex
CREATE INDEX "ReconciliationJob_shop_startedAt_idx" ON "ReconciliationJob"("shop", "startedAt");

-- CreateIndex
CREATE INDEX "InvoiceBalance_shop_category_month_year_idx" ON "InvoiceBalance"("shop", "category", "month", "year");
