-- CreateTable
CREATE TABLE "Session" (
    "id" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "state" TEXT NOT NULL,
    "isOnline" BOOLEAN NOT NULL DEFAULT false,
    "scope" TEXT,
    "expires" TIMESTAMP(3),
    "accessToken" TEXT NOT NULL,
    "userId" BIGINT,
    "firstName" TEXT,
    "lastName" TEXT,
    "email" TEXT,
    "accountOwner" BOOLEAN NOT NULL DEFAULT false,
    "locale" TEXT,
    "collaborator" BOOLEAN DEFAULT false,
    "emailVerified" BOOLEAN DEFAULT false,

    CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SetupFlag" (
    "shop" TEXT NOT NULL,
    "isSetup" BOOLEAN NOT NULL,
    "isProcessing" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "SetupFlag_pkey" PRIMARY KEY ("shop")
);

-- CreateTable
CREATE TABLE "BalanceGroup" (
    "id" SERIAL NOT NULL,
    "shop" TEXT NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "BalanceGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InvoiceBalance" (
    "id" SERIAL NOT NULL,
    "shop" TEXT NOT NULL,
    "month" INTEGER NOT NULL,
    "year" INTEGER NOT NULL,
    "category" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "balance" DECIMAL(65,30) NOT NULL,
    "groupId" INTEGER,

    CONSTRAINT "InvoiceBalance_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InvoiceTransaction" (
    "id" SERIAL NOT NULL,
    "invoiceBalanceId" INTEGER NOT NULL,
    "amount" DECIMAL(65,30) NOT NULL,
    "description" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "metadata" TEXT,

    CONSTRAINT "InvoiceTransaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ShippingCost" (
    "id" SERIAL NOT NULL,
    "shop" TEXT NOT NULL,
    "storeId" TEXT NOT NULL,
    "month" INTEGER NOT NULL,
    "year" INTEGER NOT NULL,
    "quantity" INTEGER NOT NULL,
    "shippingCost" DECIMAL(65,30) NOT NULL,
    "markupAmount" DECIMAL(65,30) NOT NULL,
    "totalAmount" DECIMAL(65,30) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ShippingCost_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ShippingTransaction" (
    "id" SERIAL NOT NULL,
    "shippingCostId" INTEGER NOT NULL,
    "amount" DECIMAL(65,30) NOT NULL,
    "shippingAmount" DECIMAL(65,30) NOT NULL,
    "markupAmount" DECIMAL(65,30) NOT NULL,
    "description" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "metadata" TEXT,

    CONSTRAINT "ShippingTransaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ShipStationStoreMapping" (
    "storeId" TEXT NOT NULL,
    "storeName" TEXT,
    "shop" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ShipStationStoreMapping_pkey" PRIMARY KEY ("storeId")
);

-- CreateTable
CREATE TABLE "ShipStationOrderTracking" (
    "storeId" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "lastOrderNumber" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ShipStationOrderTracking_pkey" PRIMARY KEY ("storeId")
);

-- CreateTable
CREATE TABLE "Price" (
    "id" SERIAL NOT NULL,
    "shop" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "cost" DECIMAL(65,30) NOT NULL,

    CONSTRAINT "Price_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Unprocessable" (
    "id" SERIAL NOT NULL,
    "shop" TEXT NOT NULL,
    "productType" TEXT,
    "variant" TEXT,
    "sku" TEXT,
    "quantity" INTEGER,
    "date" TIMESTAMP(3),
    "errorField" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "variantId" TEXT,
    "productId" TEXT,

    CONSTRAINT "Unprocessable_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProcessedWebhook" (
    "id" SERIAL NOT NULL,
    "shop" TEXT NOT NULL,
    "topic" TEXT NOT NULL,
    "webhookId" TEXT NOT NULL,
    "eventId" TEXT,
    "orderId" TEXT,
    "subscriptionId" TEXT,
    "processedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProcessedWebhook_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ReconciliationJob" (
    "id" SERIAL NOT NULL,
    "shop" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "startedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "ordersProcessed" INTEGER NOT NULL DEFAULT 0,
    "ordersSkipped" INTEGER NOT NULL DEFAULT 0,
    "ordersFailed" INTEGER NOT NULL DEFAULT 0,
    "error" TEXT,

    CONSTRAINT "ReconciliationJob_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VariantFulfillmentService" (
    "variantId" TEXT NOT NULL,
    "fulfillmentService" TEXT NOT NULL,
    "lastUpdated" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "VariantFulfillmentService_pkey" PRIMARY KEY ("variantId")
);

-- CreateIndex
CREATE INDEX "InvoiceBalance_shop_category_month_year_idx" ON "InvoiceBalance"("shop", "category", "month", "year");

-- CreateIndex
CREATE INDEX "InvoiceBalance_shop_month_year_idx" ON "InvoiceBalance"("shop", "month", "year");

-- CreateIndex
CREATE INDEX "InvoiceBalance_shop_category_idx" ON "InvoiceBalance"("shop", "category");

-- CreateIndex
CREATE INDEX "InvoiceBalance_groupId_idx" ON "InvoiceBalance"("groupId");

-- CreateIndex
CREATE INDEX "InvoiceTransaction_invoiceBalanceId_idx" ON "InvoiceTransaction"("invoiceBalanceId");

-- CreateIndex
CREATE INDEX "ShippingCost_shop_storeId_idx" ON "ShippingCost"("shop", "storeId");

-- CreateIndex
CREATE INDEX "ShippingCost_shop_month_year_idx" ON "ShippingCost"("shop", "month", "year");

-- CreateIndex
CREATE INDEX "ShippingCost_shop_storeId_month_year_idx" ON "ShippingCost"("shop", "storeId", "month", "year");

-- CreateIndex
CREATE INDEX "ShippingCost_createdAt_idx" ON "ShippingCost"("createdAt");

-- CreateIndex
CREATE INDEX "ShippingCost_updatedAt_idx" ON "ShippingCost"("updatedAt");

-- CreateIndex
CREATE INDEX "ShippingTransaction_shippingCostId_idx" ON "ShippingTransaction"("shippingCostId");

-- CreateIndex
CREATE INDEX "ShipStationStoreMapping_shop_idx" ON "ShipStationStoreMapping"("shop");

-- CreateIndex
CREATE INDEX "ShipStationStoreMapping_storeName_idx" ON "ShipStationStoreMapping"("storeName");

-- CreateIndex
CREATE INDEX "ShipStationOrderTracking_shop_idx" ON "ShipStationOrderTracking"("shop");

-- CreateIndex
CREATE INDEX "ProcessedWebhook_shop_orderId_idx" ON "ProcessedWebhook"("shop", "orderId");

-- CreateIndex
CREATE UNIQUE INDEX "ProcessedWebhook_shop_webhookId_key" ON "ProcessedWebhook"("shop", "webhookId");

-- CreateIndex
CREATE UNIQUE INDEX "ProcessedWebhook_shop_eventId_key" ON "ProcessedWebhook"("shop", "eventId");

-- CreateIndex
CREATE INDEX "ReconciliationJob_shop_status_idx" ON "ReconciliationJob"("shop", "status");

-- CreateIndex
CREATE INDEX "ReconciliationJob_shop_startedAt_idx" ON "ReconciliationJob"("shop", "startedAt");

-- CreateIndex
CREATE INDEX "ReconciliationJob_shop_type_idx" ON "ReconciliationJob"("shop", "type");

-- CreateIndex
CREATE INDEX "ReconciliationJob_status_idx" ON "ReconciliationJob"("status");

-- CreateIndex
CREATE INDEX "ReconciliationJob_startDate_endDate_idx" ON "ReconciliationJob"("startDate", "endDate");

-- CreateIndex
CREATE INDEX "VariantFulfillmentService_lastUpdated_idx" ON "VariantFulfillmentService"("lastUpdated");

-- AddForeignKey
ALTER TABLE "InvoiceTransaction" ADD CONSTRAINT "InvoiceTransaction_invoiceBalanceId_fkey" FOREIGN KEY ("invoiceBalanceId") REFERENCES "InvoiceBalance"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ShippingTransaction" ADD CONSTRAINT "ShippingTransaction_shippingCostId_fkey" FOREIGN KEY ("shippingCostId") REFERENCES "ShippingCost"("id") ON DELETE CASCADE ON UPDATE CASCADE;
