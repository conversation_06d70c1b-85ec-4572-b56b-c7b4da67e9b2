#!/bin/bash

# Script to fetch a specific shipment from ShipStation API
# Usage: ./fetch-shipment.sh <shipment_id>

# Check if shipment ID is provided
if [ -z "$1" ]; then
  echo "Error: Shipment ID is required"
  echo "Usage: ./fetch-shipment.sh <shipment_id>"
  exit 1
fi

SHIPMENT_ID=$1

# Check if API key is available
if [ -z "$SHIPSTATION_API_KEY" ]; then
  echo "Error: SHIPSTATION_API_KEY environment variable is not set"
  echo "Please set it with: export SHIPSTATION_API_KEY=your_api_key"
  exit 1
fi

echo "Fetching shipment with ID: $SHIPMENT_ID"

# First try to fetch from the direct shipments endpoint
echo "Trying direct shipments endpoint..."
# Save raw response first
curl -s -X GET \
  "https://api.shipstation.com/v2/shipments/$SHIPMENT_ID" \
  -H "API-Key: $SHIPSTATION_API_KEY" \
  -H "Content-Type: application/json" > shipment-result-raw.json

# Display raw response for debugging
echo "Raw response:"
cat shipment-result-raw.json
echo ""

# Try to parse with jq
cat shipment-result-raw.json | jq . > shipment-result.json

# Check if we got a valid result (not an error)
ERROR_MSG=$(jq -r '.message // empty' shipment-result.json)
if [ -z "$ERROR_MSG" ] && [ "$(jq 'has("shipmentId")' shipment-result.json)" = "true" ]; then
  echo "Found shipment with direct endpoint. Results saved to shipment-result.json"
else
  echo "No results from shipments endpoint. Trying labels endpoint..."

  # Try the labels endpoint
  curl -s -X GET \
    "https://api.shipstation.com/v2/labels?shipment_id=$SHIPMENT_ID" \
    -H "API-Key: $SHIPSTATION_API_KEY" \
    -H "Content-Type: application/json" | jq . > label-result.json

  # Check if we got any results
  LABEL_COUNT=$(jq '.labels | length // 0' label-result.json)
  if [ "$LABEL_COUNT" -gt 0 ]; then
    echo "Found label in labels endpoint. Results saved to label-result.json"
  else
    echo "No results from labels endpoint either."

    # Try one more approach - direct label lookup
    echo "Trying direct label lookup..."
    curl -s -X GET \
      "https://api.shipstation.com/v2/labels/$SHIPMENT_ID" \
      -H "API-Key: $SHIPSTATION_API_KEY" \
      -H "Content-Type: application/json" | jq . > direct-label-result.json

    # Check if we got a valid result (not an error)
    ERROR_MSG=$(jq -r '.message // empty' direct-label-result.json)
    if [ -z "$ERROR_MSG" ]; then
      echo "Found label with direct lookup. Results saved to direct-label-result.json"
    else
      echo "No results from direct label lookup either."
      echo "The shipment with ID $SHIPMENT_ID could not be found in the ShipStation API."
    fi
  fi
fi

echo "Done."
