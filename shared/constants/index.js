/**
 * Shared constants for the application
 * Used across both Remix and Express servers
 */

/**
 * HTTP Status Codes
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
};

/**
 * API Response Types
 */
export const RESPONSE_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
};

/**
 * Authentication Types
 */
export const AUTH_TYPES = {
  SHOPIFY_OAUTH: 'shopify-oauth',
  API_KEY: 'api-key',
  WEBHOOK: 'webhook',
  SCHEDULED_JOB: 'scheduled-job',
};

/**
 * Webhook Types
 */
export const WEBHOOK_TYPES = {
  SHOPIFY: 'shopify',
  SHIPSTATION: 'shipstation',
};

/**
 * Order Processing Constants
 */
export const ORDER_PROCESSING = {
  BATCH_SIZE: 50,
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second
  TIMEOUT: 30000, // 30 seconds
  
  FULFILLMENT_SERVICES: {
    MANUAL: 'manual',
    AUTOMATIC: 'automatic',
  },
  
  PRODUCT_TYPES: {
    SHIRT: 'shirt',
    OUTERWEAR: 'outerwear',
    STICKER: 'sticker',
    FLAG: 'flag',
    PATCH: 'patch',
  },
};

/**
 * ShipStation Constants
 */
export const SHIPSTATION = {
  API_VERSION: {
    V1: 'v1',
    V2: 'v2',
  },
  
  ENDPOINTS: {
    STORES: '/stores',
    SHIPMENTS: '/shipments',
    LABELS: '/labels',
    ORDERS: '/orders',
  },
  
  SHIPMENT_STATUS: {
    PENDING: 'pending',
    SHIPPED: 'shipped',
    DELIVERED: 'delivered',
    CANCELLED: 'cancelled',
    VOIDED: 'voided',
  },
  
  RATE_LIMITS: {
    REQUESTS_PER_MINUTE: 40,
    BURST_LIMIT: 10,
  },
};

/**
 * Database Constants
 */
export const DATABASE = {
  CONNECTION_TIMEOUT: 10000, // 10 seconds
  QUERY_TIMEOUT: 30000, // 30 seconds
  MAX_CONNECTIONS: 10,
  
  TRANSACTION_ISOLATION: {
    READ_UNCOMMITTED: 'READ UNCOMMITTED',
    READ_COMMITTED: 'READ COMMITTED',
    REPEATABLE_READ: 'REPEATABLE READ',
    SERIALIZABLE: 'SERIALIZABLE',
  },
};

/**
 * Cache Constants
 */
export const CACHE = {
  TTL: {
    SHORT: 5 * 60, // 5 minutes
    MEDIUM: 30 * 60, // 30 minutes
    LONG: 2 * 60 * 60, // 2 hours
    VERY_LONG: 24 * 60 * 60, // 24 hours
  },
  
  KEYS: {
    STORE_CONFIG: 'store_config',
    FULFILLMENT_SERVICE: 'fulfillment_service',
    PRICING_DATA: 'pricing_data',
    SHIPSTATION_STORES: 'shipstation_stores',
  },
};

/**
 * Reconciliation Constants
 */
export const RECONCILIATION = {
  TYPES: {
    SCHEDULED: 'scheduled',
    MISSING_ORDER: 'missing_order',
    SHIPSTATION: 'shipstation',
    MANUAL: 'manual',
  },
  
  STATUS: {
    PENDING: 'pending',
    RUNNING: 'running',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled',
  },
  
  BATCH_SIZE: 100,
  MAX_CONCURRENT_JOBS: 3,
};

/**
 * Error Codes
 */
export const ERROR_CODES = {
  // Authentication Errors
  AUTH_MISSING_TOKEN: 'AUTH_MISSING_TOKEN',
  AUTH_INVALID_TOKEN: 'AUTH_INVALID_TOKEN',
  AUTH_EXPIRED_TOKEN: 'AUTH_EXPIRED_TOKEN',
  AUTH_INSUFFICIENT_PERMISSIONS: 'AUTH_INSUFFICIENT_PERMISSIONS',
  
  // Validation Errors
  VALIDATION_REQUIRED_FIELD: 'VALIDATION_REQUIRED_FIELD',
  VALIDATION_INVALID_FORMAT: 'VALIDATION_INVALID_FORMAT',
  VALIDATION_OUT_OF_RANGE: 'VALIDATION_OUT_OF_RANGE',
  
  // Business Logic Errors
  STORE_NOT_SUPPORTED: 'STORE_NOT_SUPPORTED',
  STORE_CONFIG_NOT_FOUND: 'STORE_CONFIG_NOT_FOUND',
  ORDER_PROCESSING_FAILED: 'ORDER_PROCESSING_FAILED',
  RECONCILIATION_FAILED: 'RECONCILIATION_FAILED',
  
  // External Service Errors
  SHOPIFY_API_ERROR: 'SHOPIFY_API_ERROR',
  SHIPSTATION_API_ERROR: 'SHIPSTATION_API_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  
  // System Errors
  MEMORY_LIMIT_EXCEEDED: 'MEMORY_LIMIT_EXCEEDED',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
};

/**
 * Logging Constants
 */
export const LOGGING = {
  LEVELS: {
    ERROR: 'error',
    WARN: 'warn',
    INFO: 'info',
    DEBUG: 'debug',
    TRACE: 'trace',
  },
  
  CATEGORIES: {
    AUTH: 'auth',
    API: 'api',
    DATABASE: 'database',
    WEBHOOK: 'webhook',
    ORDER_PROCESSING: 'order_processing',
    RECONCILIATION: 'reconciliation',
    SYSTEM: 'system',
  },
};

/**
 * Memory Management Constants
 */
export const MEMORY = {
  HEAP_WARNING_THRESHOLD: 0.8, // 80% of max heap
  CLEANUP_INTERVAL: 60000, // 1 minute
  LARGE_DATASET_THRESHOLD: 10000, // 10k records
  BATCH_PROCESSING_SIZE: 1000,
};

/**
 * Request Timeout Constants
 */
export const TIMEOUTS = {
  API_REQUEST: 30000, // 30 seconds
  DATABASE_QUERY: 30000, // 30 seconds
  WEBHOOK_PROCESSING: 25000, // 25 seconds
  FILE_UPLOAD: 60000, // 1 minute
  HEALTH_CHECK: 5000, // 5 seconds
};

/**
 * Default Values
 */
export const DEFAULTS = {
  PAGE_SIZE: 50,
  MAX_PAGE_SIZE: 1000,
  CURRENCY: 'USD',
  TIMEZONE: 'America/New_York',
  DATE_FORMAT: 'YYYY-MM-DD',
  DATETIME_FORMAT: 'YYYY-MM-DD HH:mm:ss',
};

export default {
  HTTP_STATUS,
  RESPONSE_TYPES,
  AUTH_TYPES,
  WEBHOOK_TYPES,
  ORDER_PROCESSING,
  SHIPSTATION,
  DATABASE,
  CACHE,
  RECONCILIATION,
  ERROR_CODES,
  LOGGING,
  MEMORY,
  TIMEOUTS,
  DEFAULTS,
};
