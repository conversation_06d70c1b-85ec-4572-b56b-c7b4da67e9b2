/**
 * Shared configuration module for both Remix and Express servers
 * Provides centralized configuration management with validation
 */

import { config as appConfig } from '../../app/lib/config/index.js';

/**
 * Shared configuration object
 * Re-exports the main app configuration for use across servers
 */
export const config = appConfig;

/**
 * Express-specific configuration
 */
export const expressConfig = {
  port: process.env.EXPRESS_PORT || 4000,
  cors: {
    origin: config.shopify.appUrl,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Shopify-Shop-Domain', 'X-API-Key'],
  },
  security: {
    helmet: {
      contentSecurityPolicy: {
        directives: {
          frameAncestors: ["'self'", "https://*.shopify.com"],
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'", "https://*.shopify.com"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://*.shopify.com"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "https://*.shopify.com"],
        },
      },
      crossOriginEmbedderPolicy: false,
    },
  },
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
  },
  bodyParser: {
    limit: '10mb',
    extended: true,
  },
  timeout: {
    server: 30000, // 30 seconds
    request: 25000, // 25 seconds
  },
};

/**
 * Multistore configuration helpers
 */
export const multistoreHelpers = {
  /**
   * Get store configuration by domain
   * @param {string} storeDomain - The store domain
   * @returns {object|null} Store configuration or null if not found
   */
  getStoreConfig(storeDomain) {
    return config.multistore.storeConfigs[storeDomain] || null;
  },

  /**
   * Check if a store is supported
   * @param {string} storeDomain - The store domain
   * @returns {boolean} True if store is supported
   */
  isStoreSupported(storeDomain) {
    return config.multistore.supportedStores.includes(storeDomain);
  },

  /**
   * Get all supported stores
   * @returns {string[]} Array of supported store domains
   */
  getSupportedStores() {
    return config.multistore.supportedStores;
  },

  /**
   * Extract shop domain from various request sources
   * @param {object} req - Express request object
   * @returns {string|null} Shop domain or null if not found
   */
  extractShopFromRequest(req) {
    return (
      req.query.shop ||
      req.body?.shop ||
      req.headers['x-shopify-shop-domain'] ||
      req.headers['x-shop-domain'] ||
      null
    );
  },
};

/**
 * Environment-specific configuration
 */
export const environmentConfig = {
  isDevelopment: config.app.environment === 'development',
  isProduction: config.app.environment === 'production',
  
  logging: {
    level: config.app.environment === 'development' ? 'debug' : 'info',
    format: config.app.environment === 'development' ? 'dev' : 'combined',
  },
  
  monitoring: {
    enabled: config.app.environment === 'production',
    healthCheck: {
      interval: 30000, // 30 seconds
      timeout: 5000, // 5 seconds
    },
  },
};

/**
 * Validate configuration on module load
 */
function validateConfiguration() {
  const requiredEnvVars = [
    'SHOPIFY_APP_URL',
    'DATABASE_URL',
    'SUPPORTED_STORES',
  ];

  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  // Validate multistore configuration
  if (!config.multistore.supportedStores.length) {
    throw new Error('At least one supported store must be configured');
  }

  // Validate that all supported stores have configurations
  const missingConfigs = config.multistore.supportedStores.filter(
    store => !config.multistore.storeConfigs[store]
  );

  if (missingConfigs.length > 0) {
    throw new Error(`Missing store configurations for: ${missingConfigs.join(', ')}`);
  }
}

// Validate configuration on import
validateConfiguration();

export default config;
