/**
 * TypeScript-style type definitions for multistore functionality
 * These are JSDoc type definitions for better IDE support and documentation
 */

/**
 * @typedef {Object} StoreConfig
 * @property {string} apiKey - Shopify API key for the store
 * @property {string} apiSecret - Shopify API secret for the store
 * @property {string[]} [scopes] - OAuth scopes for the store
 * @property {string} [webhookSecret] - Webhook verification secret
 */

/**
 * @typedef {Object} MultistoreConfig
 * @property {boolean} enabled - Whether multistore is enabled
 * @property {string[]} supportedStores - Array of supported store domains
 * @property {Record<string, StoreConfig>} storeConfigs - Store-specific configurations
 */

/**
 * @typedef {Object} ShopifyAppInstance
 * @property {string} apiKey - API key for this instance
 * @property {string} apiSecret - API secret for this instance
 * @property {string[]} scopes - OAuth scopes
 * @property {Function} authenticate - Authentication function
 * @property {Function} webhook - Webhook handler
 * @property {Object} api - API client instance
 */

/**
 * @typedef {Object} ExpressRequest
 * @property {Object} query - Query parameters
 * @property {Object} body - Request body
 * @property {Object} headers - Request headers
 * @property {string} [shop] - Shop domain (added by middleware)
 * @property {ShopifyAppInstance} [shopifyApp] - Shopify app instance (added by middleware)
 * @property {string} [requestId] - Unique request ID (added by middleware)
 */

/**
 * @typedef {Object} ExpressResponse
 * @property {Function} json - Send JSON response
 * @property {Function} status - Set status code
 * @property {Function} send - Send response
 * @property {Function} redirect - Redirect response
 */

/**
 * @typedef {Function} ExpressNext
 * @param {Error} [error] - Optional error to pass to error handler
 */

/**
 * @typedef {Function} ExpressMiddleware
 * @param {ExpressRequest} req - Express request object
 * @param {ExpressResponse} res - Express response object
 * @param {ExpressNext} next - Next middleware function
 */

/**
 * @typedef {Object} AuthenticationResult
 * @property {boolean} success - Whether authentication was successful
 * @property {string} [shop] - Authenticated shop domain
 * @property {Object} [session] - Session data
 * @property {string} [error] - Error message if authentication failed
 */

/**
 * @typedef {Object} WebhookPayload
 * @property {string} shop - Shop domain
 * @property {string} topic - Webhook topic
 * @property {Object} data - Webhook data
 * @property {string} timestamp - Webhook timestamp
 * @property {string} [signature] - Webhook signature for verification
 */

/**
 * @typedef {Object} ApiResponse
 * @property {boolean} success - Whether the request was successful
 * @property {*} [data] - Response data
 * @property {string} [message] - Response message
 * @property {Object} [error] - Error details
 * @property {Object} [meta] - Additional metadata
 */

/**
 * @typedef {Object} ProcessingOptions
 * @property {number} [batchSize] - Size of processing batches
 * @property {number} [maxConcurrency] - Maximum concurrent operations
 * @property {number} [timeout] - Operation timeout in milliseconds
 * @property {Function} [onProgress] - Progress callback function
 * @property {Function} [onError] - Error callback function
 */

/**
 * @typedef {Object} MemoryStats
 * @property {string} rss - Resident Set Size
 * @property {string} heapTotal - Total heap size
 * @property {string} heapUsed - Used heap size
 * @property {string} external - External memory usage
 * @property {string} arrayBuffers - Array buffer usage
 */

/**
 * @typedef {Object} ProcessingResult
 * @property {boolean} success - Whether processing was successful
 * @property {*} [result] - Processing result data
 * @property {number} processedItems - Number of items processed
 * @property {number} [processingTime] - Time taken to process
 * @property {string} [error] - Error message if processing failed
 */

/**
 * @typedef {Object} RateLimitConfig
 * @property {number} windowMs - Time window in milliseconds
 * @property {number} max - Maximum requests per window
 * @property {string} [message] - Rate limit exceeded message
 * @property {boolean} [standardHeaders] - Include standard rate limit headers
 * @property {boolean} [legacyHeaders] - Include legacy rate limit headers
 */

/**
 * @typedef {Object} SecurityConfig
 * @property {Object} helmet - Helmet security configuration
 * @property {Object} cors - CORS configuration
 * @property {RateLimitConfig} rateLimit - Rate limiting configuration
 */

/**
 * @typedef {Object} ServerConfig
 * @property {number} port - Server port
 * @property {SecurityConfig} security - Security configuration
 * @property {Object} bodyParser - Body parser configuration
 * @property {Object} timeout - Timeout configuration
 */

/**
 * @typedef {Object} HealthCheckResult
 * @property {boolean} healthy - Whether the service is healthy
 * @property {string} status - Health status string
 * @property {Object} checks - Individual health check results
 * @property {string} timestamp - Health check timestamp
 * @property {number} uptime - Service uptime in seconds
 */

/**
 * @typedef {Object} LogEntry
 * @property {string} level - Log level (error, warn, info, debug)
 * @property {string} message - Log message
 * @property {string} category - Log category
 * @property {string} timestamp - Log timestamp
 * @property {Object} [meta] - Additional metadata
 * @property {string} [requestId] - Associated request ID
 */

/**
 * @typedef {Object} ValidationError
 * @property {string} field - Field name that failed validation
 * @property {string} message - Validation error message
 * @property {*} value - Value that failed validation
 * @property {string} [rule] - Validation rule that failed
 */

/**
 * @typedef {Object} ErrorResponse
 * @property {boolean} success - Always false for errors
 * @property {Object} error - Error details
 * @property {string} error.message - Error message
 * @property {string} error.code - Error code
 * @property {string} error.timestamp - Error timestamp
 * @property {ValidationError[]} [error.validationErrors] - Validation errors
 * @property {number} statusCode - HTTP status code
 */

/**
 * @typedef {Object} PaginationOptions
 * @property {number} [page] - Page number (1-based)
 * @property {number} [limit] - Items per page
 * @property {string} [sortBy] - Field to sort by
 * @property {string} [sortOrder] - Sort order (asc/desc)
 */

/**
 * @typedef {Object} PaginatedResponse
 * @property {boolean} success - Whether the request was successful
 * @property {Array} data - Array of items
 * @property {Object} pagination - Pagination metadata
 * @property {number} pagination.page - Current page
 * @property {number} pagination.limit - Items per page
 * @property {number} pagination.total - Total number of items
 * @property {number} pagination.pages - Total number of pages
 * @property {boolean} pagination.hasNext - Whether there's a next page
 * @property {boolean} pagination.hasPrev - Whether there's a previous page
 */

/**
 * @typedef {Object} CacheOptions
 * @property {number} [ttl] - Time to live in seconds
 * @property {string} [key] - Cache key
 * @property {boolean} [refresh] - Whether to refresh the cache
 */

/**
 * @typedef {Object} RetryOptions
 * @property {number} [maxRetries] - Maximum number of retries
 * @property {number} [baseDelay] - Base delay between retries in milliseconds
 * @property {number} [maxDelay] - Maximum delay between retries
 * @property {Function} [shouldRetry] - Function to determine if retry should happen
 */

// Export types for use in other modules
export const Types = {
  StoreConfig: 'StoreConfig',
  MultistoreConfig: 'MultistoreConfig',
  ShopifyAppInstance: 'ShopifyAppInstance',
  ExpressRequest: 'ExpressRequest',
  ExpressResponse: 'ExpressResponse',
  ExpressNext: 'ExpressNext',
  ExpressMiddleware: 'ExpressMiddleware',
  AuthenticationResult: 'AuthenticationResult',
  WebhookPayload: 'WebhookPayload',
  ApiResponse: 'ApiResponse',
  ProcessingOptions: 'ProcessingOptions',
  MemoryStats: 'MemoryStats',
  ProcessingResult: 'ProcessingResult',
  RateLimitConfig: 'RateLimitConfig',
  SecurityConfig: 'SecurityConfig',
  ServerConfig: 'ServerConfig',
  HealthCheckResult: 'HealthCheckResult',
  LogEntry: 'LogEntry',
  ValidationError: 'ValidationError',
  ErrorResponse: 'ErrorResponse',
  PaginationOptions: 'PaginationOptions',
  PaginatedResponse: 'PaginatedResponse',
  CacheOptions: 'CacheOptions',
  RetryOptions: 'RetryOptions',
};

export default Types;
