/**
 * Memory-efficient processing utilities for large datasets
 * Prevents memory leaks and handles large data processing safely
 */

import { MEMORY, TIMEOUTS } from '../constants/index.js';
import { chunkArray, getMemoryUsage, isMemoryUsageHigh, formatBytes } from './index.js';

/**
 * Process large datasets in batches with memory management
 * @param {Array} dataset - Large dataset to process
 * @param {Function} processor - Function to process each batch
 * @param {object} options - Processing options
 * @returns {Promise<Array>} Array of processing results
 */
export async function processLargeDataset(dataset, processor, options = {}) {
  const {
    batchSize = MEMORY.BATCH_PROCESSING_SIZE,
    maxConcurrency = 3,
    memoryThreshold = MEMORY.HEAP_WARNING_THRESHOLD,
    onProgress = null,
    onMemoryWarning = null,
    timeout = TIMEOUTS.API_REQUEST,
  } = options;

  if (!Array.isArray(dataset)) {
    throw new Error('Dataset must be an array');
  }

  if (typeof processor !== 'function') {
    throw new Error('Processor must be a function');
  }

  const chunks = chunkArray(dataset, batchSize);
  const results = [];
  let processedCount = 0;

  console.log(`Processing ${dataset.length} items in ${chunks.length} batches of ${batchSize}`);

  // Process chunks with concurrency control
  for (let i = 0; i < chunks.length; i += maxConcurrency) {
    const batchPromises = [];
    
    // Create batch of concurrent processing promises
    for (let j = 0; j < maxConcurrency && (i + j) < chunks.length; j++) {
      const chunkIndex = i + j;
      const chunk = chunks[chunkIndex];
      
      const batchPromise = processBatchWithTimeout(
        chunk,
        processor,
        timeout,
        chunkIndex
      );
      
      batchPromises.push(batchPromise);
    }

    // Wait for current batch to complete
    const batchResults = await Promise.allSettled(batchPromises);
    
    // Process results and handle errors
    for (const result of batchResults) {
      if (result.status === 'fulfilled') {
        results.push(result.value);
        processedCount += result.value.processedItems || 0;
      } else {
        console.error('Batch processing failed:', result.reason);
        results.push({
          success: false,
          error: result.reason.message,
          processedItems: 0,
        });
      }
    }

    // Memory management
    if (isMemoryUsageHigh(memoryThreshold)) {
      const memoryUsage = getMemoryUsage();
      console.warn('High memory usage detected:', memoryUsage);
      
      if (onMemoryWarning) {
        onMemoryWarning(memoryUsage);
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      // Add a small delay to allow memory cleanup
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Progress reporting
    if (onProgress) {
      onProgress({
        processed: processedCount,
        total: dataset.length,
        percentage: Math.round((processedCount / dataset.length) * 100),
        batchesCompleted: i + batchPromises.length,
        totalBatches: chunks.length,
      });
    }
  }

  return results;
}

/**
 * Process a single batch with timeout protection
 * @param {Array} chunk - Data chunk to process
 * @param {Function} processor - Processing function
 * @param {number} timeout - Timeout in milliseconds
 * @param {number} chunkIndex - Index of the chunk for logging
 * @returns {Promise<object>} Processing result
 */
async function processBatchWithTimeout(chunk, processor, timeout, chunkIndex) {
  return new Promise(async (resolve, reject) => {
    const timeoutId = setTimeout(() => {
      reject(new Error(`Batch ${chunkIndex} processing timed out after ${timeout}ms`));
    }, timeout);

    try {
      const startTime = Date.now();
      const result = await processor(chunk, chunkIndex);
      const endTime = Date.now();
      
      clearTimeout(timeoutId);
      
      resolve({
        success: true,
        result,
        processedItems: chunk.length,
        processingTime: endTime - startTime,
        chunkIndex,
      });
    } catch (error) {
      clearTimeout(timeoutId);
      reject(error);
    }
  });
}

/**
 * Stream processor for very large datasets that don't fit in memory
 * @param {AsyncIterable} dataStream - Async iterable data stream
 * @param {Function} processor - Function to process each item
 * @param {object} options - Processing options
 * @returns {AsyncGenerator} Async generator yielding results
 */
export async function* processDataStream(dataStream, processor, options = {}) {
  const {
    batchSize = MEMORY.BATCH_PROCESSING_SIZE,
    memoryThreshold = MEMORY.HEAP_WARNING_THRESHOLD,
    onMemoryWarning = null,
  } = options;

  let batch = [];
  let processedCount = 0;

  for await (const item of dataStream) {
    batch.push(item);

    if (batch.length >= batchSize) {
      const results = await processBatch(batch, processor);
      processedCount += batch.length;
      
      yield {
        results,
        processedCount,
        batchSize: batch.length,
      };

      // Clear batch and check memory
      batch = [];
      
      if (isMemoryUsageHigh(memoryThreshold)) {
        const memoryUsage = getMemoryUsage();
        console.warn('High memory usage in stream processing:', memoryUsage);
        
        if (onMemoryWarning) {
          onMemoryWarning(memoryUsage);
        }
        
        if (global.gc) {
          global.gc();
        }
      }
    }
  }

  // Process remaining items
  if (batch.length > 0) {
    const results = await processBatch(batch, processor);
    processedCount += batch.length;
    
    yield {
      results,
      processedCount,
      batchSize: batch.length,
    };
  }
}

/**
 * Process a batch of items
 * @param {Array} batch - Batch of items to process
 * @param {Function} processor - Processing function
 * @returns {Promise<Array>} Array of results
 */
async function processBatch(batch, processor) {
  const results = [];
  
  for (const item of batch) {
    try {
      const result = await processor(item);
      results.push({ success: true, result });
    } catch (error) {
      results.push({ success: false, error: error.message });
    }
  }
  
  return results;
}

/**
 * Memory-safe array transformation
 * @param {Array} array - Array to transform
 * @param {Function} transformer - Transformation function
 * @param {object} options - Processing options
 * @returns {Promise<Array>} Transformed array
 */
export async function transformArraySafely(array, transformer, options = {}) {
  const {
    batchSize = MEMORY.BATCH_PROCESSING_SIZE,
    memoryThreshold = MEMORY.HEAP_WARNING_THRESHOLD,
  } = options;

  const result = [];
  const chunks = chunkArray(array, batchSize);

  for (const chunk of chunks) {
    const transformedChunk = await Promise.all(
      chunk.map(async (item, index) => {
        try {
          return await transformer(item, index);
        } catch (error) {
          console.error('Transformation error:', error);
          return null;
        }
      })
    );

    result.push(...transformedChunk.filter(item => item !== null));

    // Memory check
    if (isMemoryUsageHigh(memoryThreshold)) {
      if (global.gc) {
        global.gc();
      }
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  return result;
}

/**
 * Monitor memory usage during processing
 * @param {Function} operation - Operation to monitor
 * @param {object} options - Monitoring options
 * @returns {Promise<object>} Operation result with memory stats
 */
export async function monitorMemoryUsage(operation, options = {}) {
  const {
    logInterval = 5000, // Log every 5 seconds
    memoryThreshold = MEMORY.HEAP_WARNING_THRESHOLD,
  } = options;

  const startMemory = getMemoryUsage();
  const startTime = Date.now();
  
  let intervalId;
  const memorySnapshots = [];

  // Start memory monitoring
  if (logInterval > 0) {
    intervalId = setInterval(() => {
      const currentMemory = getMemoryUsage();
      memorySnapshots.push({
        timestamp: Date.now(),
        memory: currentMemory,
      });
      
      if (isMemoryUsageHigh(memoryThreshold)) {
        console.warn('High memory usage detected during operation:', currentMemory);
      }
    }, logInterval);
  }

  try {
    const result = await operation();
    const endTime = Date.now();
    const endMemory = getMemoryUsage();

    if (intervalId) {
      clearInterval(intervalId);
    }

    return {
      result,
      memoryStats: {
        start: startMemory,
        end: endMemory,
        snapshots: memorySnapshots,
        duration: endTime - startTime,
      },
    };
  } catch (error) {
    if (intervalId) {
      clearInterval(intervalId);
    }
    throw error;
  }
}

export default {
  processLargeDataset,
  processDataStream,
  transformArraySafely,
  monitorMemoryUsage,
};
