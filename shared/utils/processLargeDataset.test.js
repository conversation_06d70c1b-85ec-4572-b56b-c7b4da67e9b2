/**
 * Tests for processLargeDataset utility
 */

import { jest } from '@jest/globals';
import {
  processLargeDataset,
  processDataStream,
  transformArraySafely,
  monitorMemoryUsage
} from './processLargeDataset.js';

// Mock dependencies
jest.mock('../constants/index.js', () => ({
  MEMORY: {
    BATCH_PROCESSING_SIZE: 10,
    HEAP_WARNING_THRESHOLD: 0.8,
  },
  TIMEOUTS: {
    API_REQUEST: 30000,
  },
}));

jest.mock('./index.js', () => ({
  chunkArray: jest.fn((array, size) => {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }),
  getMemoryUsage: jest.fn(() => ({
    rss: '100 MB',
    heapTotal: '50 MB',
    heapUsed: '30 MB',
    external: '5 MB',
    arrayBuffers: '1 MB',
  })),
  isMemoryUsageHigh: jest.fn(() => false),
  formatBytes: jest.fn((bytes) => `${bytes} bytes`),
}));

describe('processLargeDataset', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should process dataset in batches', async () => {
    const dataset = Array.from({ length: 25 }, (_, i) => i);
    const processor = jest.fn().mockResolvedValue({ processed: true });

    const results = await processLargeDataset(dataset, processor, {
      batchSize: 10,
      maxConcurrency: 2,
    });

    expect(processor).toHaveBeenCalledTimes(3); // 25 items / 10 batch size = 3 batches
    expect(results).toHaveLength(3);
    expect(results.every(r => r.success)).toBe(true);
  });

  it('should handle processor errors gracefully', async () => {
    // Mock console.error to prevent test output pollution
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

    const dataset = [1, 2, 3];
    const processor = jest.fn()
      .mockResolvedValueOnce({ processed: true })
      .mockRejectedValueOnce(new Error('Processing failed'))
      .mockResolvedValueOnce({ processed: true });

    const results = await processLargeDataset(dataset, processor, {
      batchSize: 1,
      maxConcurrency: 1,
    });

    expect(results).toHaveLength(3);
    expect(results[0].success).toBe(true);
    expect(results[1].success).toBe(false);
    expect(results[1].error).toBe('Processing failed');
    expect(results[2].success).toBe(true);

    // Verify console.error was called
    expect(consoleErrorSpy).toHaveBeenCalled();

    // Restore console.error
    consoleErrorSpy.mockRestore();
  });

  it('should call progress callback', async () => {
    const dataset = Array.from({ length: 20 }, (_, i) => i);
    const processor = jest.fn().mockResolvedValue({ processed: true });
    const onProgress = jest.fn();

    await processLargeDataset(dataset, processor, {
      batchSize: 5,
      onProgress,
    });

    expect(onProgress).toHaveBeenCalled();
    expect(onProgress).toHaveBeenCalledWith(
      expect.objectContaining({
        processed: expect.any(Number),
        total: 20,
        percentage: expect.any(Number),
      })
    );
  });

  it('should call memory warning callback when memory is high', async () => {
    // Mock console.warn to prevent test output pollution
    const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();

    const { isMemoryUsageHigh, getMemoryUsage } = require('./index.js');

    isMemoryUsageHigh.mockReturnValue(true);

    const dataset = [1, 2, 3];
    const processor = jest.fn().mockResolvedValue({ processed: true });
    const onMemoryWarning = jest.fn();

    await processLargeDataset(dataset, processor, {
      batchSize: 1,
      onMemoryWarning,
    });

    expect(onMemoryWarning).toHaveBeenCalled();
    expect(getMemoryUsage).toHaveBeenCalled();
    expect(consoleWarnSpy).toHaveBeenCalled();

    // Restore console.warn
    consoleWarnSpy.mockRestore();
  });

  it('should handle timeout errors', async () => {
    const dataset = [1];
    const processor = jest.fn().mockImplementation(() =>
      new Promise(resolve => setTimeout(resolve, 100))
    );

    const results = await processLargeDataset(dataset, processor, {
      batchSize: 1,
      timeout: 50, // Shorter than processor delay
    });

    expect(results).toHaveLength(1);
    expect(results[0].success).toBe(false);
    expect(results[0].error).toContain('timed out');
  });

  it('should validate input parameters', async () => {
    await expect(processLargeDataset('not-an-array', jest.fn())).rejects.toThrow('Dataset must be an array');
    await expect(processLargeDataset([], 'not-a-function')).rejects.toThrow('Processor must be a function');
  });
});

describe('processDataStream', () => {
  it('should process async iterable data stream', async () => {
    async function* mockDataStream() {
      for (let i = 0; i < 15; i++) {
        yield i;
      }
    }

    const processor = jest.fn().mockResolvedValue({ processed: true });
    const results = [];

    for await (const result of processDataStream(mockDataStream(), processor, {
      batchSize: 5,
    })) {
      results.push(result);
    }

    expect(results).toHaveLength(3); // 15 items / 5 batch size = 3 batches
    expect(processor).toHaveBeenCalledTimes(15); // Called once per item
  });

  it('should handle memory warnings in stream processing', async () => {
    const { isMemoryUsageHigh, getMemoryUsage } = require('./index.js');

    isMemoryUsageHigh.mockReturnValue(true);

    async function* mockDataStream() {
      for (let i = 0; i < 5; i++) {
        yield i;
      }
    }

    const processor = jest.fn().mockResolvedValue({ processed: true });
    const onMemoryWarning = jest.fn();
    const results = [];

    for await (const result of processDataStream(mockDataStream(), processor, {
      batchSize: 2,
      onMemoryWarning,
    })) {
      results.push(result);
    }

    expect(onMemoryWarning).toHaveBeenCalled();
  });
});

describe('transformArraySafely', () => {
  it('should transform array in batches', async () => {
    const array = [1, 2, 3, 4, 5];
    const transformer = jest.fn().mockImplementation(x => x * 2);

    const result = await transformArraySafely(array, transformer, {
      batchSize: 2,
    });

    expect(result).toEqual([2, 4, 6, 8, 10]);
    expect(transformer).toHaveBeenCalledTimes(5);
  });

  it('should filter out null results from failed transformations', async () => {
    const array = [1, 2, 3];
    const transformer = jest.fn()
      .mockResolvedValueOnce(2)
      .mockRejectedValueOnce(new Error('Transform failed'))
      .mockResolvedValueOnce(6);

    const result = await transformArraySafely(array, transformer);

    expect(result).toEqual([2, 6]); // Failed transformation filtered out
  });

  it('should trigger garbage collection when memory usage is high', async () => {
    const { isMemoryUsageHigh } = require('./index.js');

    isMemoryUsageHigh.mockReturnValue(true);
    global.gc = jest.fn();

    const array = [1, 2, 3];
    const transformer = jest.fn().mockImplementation(x => x * 2);

    await transformArraySafely(array, transformer, {
      batchSize: 1,
    });

    expect(global.gc).toHaveBeenCalled();
  });
});

describe('monitorMemoryUsage', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should monitor memory usage during operation', async () => {
    const { getMemoryUsage } = require('./index.js');

    const operation = jest.fn().mockImplementation(() =>
      new Promise(resolve => {
        setTimeout(() => resolve('operation result'), 1000);
      })
    );

    const monitorPromise = monitorMemoryUsage(operation, {
      logInterval: 500,
    });

    // Advance timers to trigger memory monitoring
    jest.advanceTimersByTime(500);
    jest.advanceTimersByTime(500);

    const result = await monitorPromise;

    expect(result.result).toBe('operation result');
    expect(result.memoryStats).toBeDefined();
    expect(result.memoryStats.start).toBeDefined();
    expect(result.memoryStats.end).toBeDefined();
    expect(result.memoryStats.duration).toBeGreaterThan(0);
    expect(getMemoryUsage).toHaveBeenCalled();
  });

  it('should handle operation errors and clean up monitoring', async () => {
    const operation = jest.fn().mockRejectedValue(new Error('Operation failed'));

    await expect(monitorMemoryUsage(operation, {
      logInterval: 100,
    })).rejects.toThrow('Operation failed');
  });

  it('should not start monitoring if logInterval is 0', async () => {
    const { getMemoryUsage } = require('./index.js');

    const operation = jest.fn().mockResolvedValue('result');

    const result = await monitorMemoryUsage(operation, {
      logInterval: 0,
    });

    expect(result.result).toBe('result');
    expect(result.memoryStats.snapshots).toEqual([]);
  });

  it('should warn about high memory usage during monitoring', async () => {
    const { isMemoryUsageHigh } = require('./index.js');
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

    isMemoryUsageHigh.mockReturnValue(true);

    const operation = jest.fn().mockImplementation(() =>
      new Promise(resolve => {
        setTimeout(() => resolve('result'), 600);
      })
    );

    const monitorPromise = monitorMemoryUsage(operation, {
      logInterval: 200,
      memoryThreshold: 0.7,
    });

    // Advance timers to trigger memory monitoring
    jest.advanceTimersByTime(200);
    jest.advanceTimersByTime(400);

    await monitorPromise;

    expect(consoleSpy).toHaveBeenCalledWith(
      'High memory usage detected during operation:',
      expect.any(Object)
    );

    consoleSpy.mockRestore();
  });
});
