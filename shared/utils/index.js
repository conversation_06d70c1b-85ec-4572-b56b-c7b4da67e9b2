/**
 * Shared utility functions for both Remix and Express servers
 */

import { ERROR_CODES, HTTP_STATUS } from '../constants/index.js';

/**
 * Format error response consistently across servers
 * @param {Error|string} error - Error object or message
 * @param {string} code - Error code from ERROR_CODES
 * @param {number} statusCode - HTTP status code
 * @returns {object} Formatted error response
 */
export function formatErrorResponse(error, code = ERROR_CODES.INTERNAL_SERVER_ERROR, statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR) {
  const message = error instanceof Error ? error.message : error;
  
  return {
    success: false,
    error: {
      message,
      code,
      timestamp: new Date().toISOString(),
    },
    statusCode,
  };
}

/**
 * Format success response consistently across servers
 * @param {any} data - Response data
 * @param {string} message - Success message
 * @param {object} meta - Additional metadata
 * @returns {object} Formatted success response
 */
export function formatSuccessResponse(data, message = 'Success', meta = {}) {
  return {
    success: true,
    data,
    message,
    meta: {
      timestamp: new Date().toISOString(),
      ...meta,
    },
  };
}

/**
 * Normalize shop domain to consistent format
 * @param {string} shop - Shop domain (with or without .myshopify.com)
 * @returns {string} Normalized shop domain
 */
export function normalizeShopDomain(shop) {
  if (!shop) return null;
  
  // Remove protocol if present
  let normalized = shop.replace(/^https?:\/\//, '');
  
  // Add .myshopify.com if not present
  if (!normalized.includes('.myshopify.com')) {
    normalized = `${normalized}.myshopify.com`;
  }
  
  return normalized.toLowerCase();
}

/**
 * Extract shop name from domain for display purposes
 * @param {string} shopDomain - Full shop domain
 * @returns {string} Formatted shop name
 */
export function formatShopName(shopDomain) {
  if (!shopDomain) return '';
  
  // Remove .myshopify.com
  let name = shopDomain.replace('.myshopify.com', '');
  
  // Replace dashes with spaces and capitalize each word
  return name
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Validate required fields in an object
 * @param {object} obj - Object to validate
 * @param {string[]} requiredFields - Array of required field names
 * @throws {Error} If any required field is missing
 */
export function validateRequiredFields(obj, requiredFields) {
  const missing = requiredFields.filter(field => {
    const value = obj[field];
    return value === undefined || value === null || value === '';
  });
  
  if (missing.length > 0) {
    throw new Error(`Missing required fields: ${missing.join(', ')}`);
  }
}

/**
 * Safely parse JSON with error handling
 * @param {string} jsonString - JSON string to parse
 * @param {any} defaultValue - Default value if parsing fails
 * @returns {any} Parsed JSON or default value
 */
export function safeJsonParse(jsonString, defaultValue = null) {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    return defaultValue;
  }
}

/**
 * Create a delay promise for rate limiting or retries
 * @param {number} ms - Milliseconds to delay
 * @returns {Promise} Promise that resolves after delay
 */
export function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry a function with exponential backoff
 * @param {Function} fn - Function to retry
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} baseDelay - Base delay in milliseconds
 * @returns {Promise} Promise that resolves with function result
 */
export async function retryWithBackoff(fn, maxRetries = 3, baseDelay = 1000) {
  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        break;
      }
      
      const delayMs = baseDelay * Math.pow(2, attempt);
      await delay(delayMs);
    }
  }
  
  throw lastError;
}

/**
 * Chunk an array into smaller arrays
 * @param {Array} array - Array to chunk
 * @param {number} size - Size of each chunk
 * @returns {Array[]} Array of chunks
 */
export function chunkArray(array, size) {
  const chunks = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

/**
 * Generate a unique request ID for tracking
 * @returns {string} Unique request ID
 */
export function generateRequestId() {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Check if a value is a valid URL
 * @param {string} url - URL to validate
 * @returns {boolean} True if valid URL
 */
export function isValidUrl(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Sanitize string for logging (remove sensitive data)
 * @param {string} str - String to sanitize
 * @returns {string} Sanitized string
 */
export function sanitizeForLogging(str) {
  if (!str) return str;
  
  // Replace common sensitive patterns
  return str
    .replace(/api[_-]?key[s]?["\s]*[:=]["\s]*[a-zA-Z0-9]+/gi, 'api_key="***"')
    .replace(/secret[s]?["\s]*[:=]["\s]*[a-zA-Z0-9]+/gi, 'secret="***"')
    .replace(/token[s]?["\s]*[:=]["\s]*[a-zA-Z0-9]+/gi, 'token="***"')
    .replace(/password[s]?["\s]*[:=]["\s]*[a-zA-Z0-9]+/gi, 'password="***"');
}

/**
 * Convert bytes to human readable format
 * @param {number} bytes - Number of bytes
 * @returns {string} Human readable size
 */
export function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Get memory usage information
 * @returns {object} Memory usage stats
 */
export function getMemoryUsage() {
  const usage = process.memoryUsage();
  
  return {
    rss: formatBytes(usage.rss),
    heapTotal: formatBytes(usage.heapTotal),
    heapUsed: formatBytes(usage.heapUsed),
    external: formatBytes(usage.external),
    arrayBuffers: formatBytes(usage.arrayBuffers),
  };
}

/**
 * Check if memory usage is approaching limits
 * @param {number} threshold - Warning threshold (0-1)
 * @returns {boolean} True if memory usage is high
 */
export function isMemoryUsageHigh(threshold = 0.8) {
  const usage = process.memoryUsage();
  return (usage.heapUsed / usage.heapTotal) > threshold;
}

export default {
  formatErrorResponse,
  formatSuccessResponse,
  normalizeShopDomain,
  formatShopName,
  validateRequiredFields,
  safeJsonParse,
  delay,
  retryWithBackoff,
  chunkArray,
  generateRequestId,
  isValidUrl,
  sanitizeForLogging,
  formatBytes,
  getMemoryUsage,
  isMemoryUsageHigh,
};
