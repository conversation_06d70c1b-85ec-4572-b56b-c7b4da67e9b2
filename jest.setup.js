/**
 * Jest setup file
 * Runs before each test file
 */

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
};

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.SHOPIFY_APP_URL = 'https://test-app.example.com';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test';
process.env.SUPPORTED_STORES = 'test-store.myshopify.com,another-store.myshopify.com';
process.env.EXPRESS_PORT = '4000';

// Shopify API configuration
process.env.SHOPIFY_API_KEY = 'test-api-key';
process.env.SHOPIFY_API_SECRET = 'test-api-secret';
process.env.SHOPIFY_SCOPES = 'read_orders,read_products';

// Store-specific configuration
process.env.TEST_STORE_SHOPIFY_API_KEY = 'test-api-key';
process.env.TEST_STORE_SHOPIFY_API_SECRET = 'test-api-secret';
process.env.TEST_STORE_SHOPIFY_SCOPES = 'read_orders,read_products';
process.env.TEST_STORE_SHOPIFY_APP_URL = 'https://test-app.example.com';

process.env.ANOTHER_STORE_SHOPIFY_API_KEY = 'test-api-key-2';
process.env.ANOTHER_STORE_SHOPIFY_API_SECRET = 'test-api-secret-2';
process.env.ANOTHER_STORE_SHOPIFY_SCOPES = 'read_orders,read_products';
process.env.ANOTHER_STORE_SHOPIFY_APP_URL = 'https://test-app.example.com';

// ShipStation configuration
process.env.SHIPSTATION_API_KEY = 'test-shipstation-key';
process.env.SHIPSTATION_API_SECRET = 'test-shipstation-secret';
process.env.SHIPSTATION_API_URL = 'https://ssapi.shipstation.com';

// API keys for testing
process.env.RECONCILIATION_API_KEY = 'test-reconciliation-key';
process.env.SHIPSTATION_WEBHOOK_KEY = 'test-webhook-key';

// Mock global.gc if not available
if (!global.gc) {
  global.gc = jest.fn();
}

// Set up global test utilities
global.testUtils = {
  createMockRequest: (overrides = {}) => ({
    query: {},
    body: {},
    headers: {},
    params: {},
    method: 'GET',
    path: '/test',
    ...overrides,
  }),

  createMockResponse: (overrides = {}) => ({
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    redirect: jest.fn().mockReturnThis(),
    end: jest.fn(),
    headersSent: false,
    statusCode: 200,
    ...overrides,
  }),

  createMockNext: () => jest.fn(),
};

// Increase timeout for async operations
jest.setTimeout(30000);
