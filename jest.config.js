/**
 * Jest configuration for the project
 * Supports ES modules and different test environments
 */

export default {
  // Transform configuration
  transform: {
    '^.+\\.js$': ['babel-jest', {
      presets: [
        ['@babel/preset-env', {
          targets: { node: 'current' },
          modules: 'commonjs' // Use CommonJS for Jest
        }]
      ]
    }]
  },

  // Module name mapping
  moduleNameMapper: {
    '^(\\.{1,2}/.*)\\.js$': '$1',
  },

  // Test environment
  testEnvironment: 'node',

  // Test file patterns
  testMatch: [
    '**/__tests__/**/*.js',
    '**/?(*.)+(spec|test).js'
  ],

  // Coverage configuration
  collectCoverageFrom: [
    'app/**/*.js',
    'express-server/**/*.js',
    'shared/**/*.js',
    '!**/*.test.js',
    '!**/*.spec.js',
    '!**/node_modules/**',
    '!**/coverage/**',
    '!**/build/**',
    '!**/dist/**'
  ],

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],

  // Module directories
  moduleDirectories: ['node_modules', '<rootDir>'],

  // Clear mocks between tests
  clearMocks: true,

  // Restore mocks after each test
  restoreMocks: true,

  // Verbose output
  verbose: true,

  // Test timeout
  testTimeout: 30000,

  // Global setup and teardown
  globalSetup: '<rootDir>/jest.global-setup.js',
  globalTeardown: '<rootDir>/jest.global-teardown.js',

  // Projects configuration for different test types
  projects: [
    {
      displayName: 'express-server',
      testMatch: ['<rootDir>/express-server/**/*.test.js'],
      testEnvironment: 'node',
    },
    {
      displayName: 'shared',
      testMatch: ['<rootDir>/shared/**/*.test.js'],
      testEnvironment: 'node',
    },
    {
      displayName: 'app',
      testMatch: ['<rootDir>/app/**/*.test.js'],
      testEnvironment: 'node',
    }
  ]
};
