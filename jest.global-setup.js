/**
 * Jest global setup
 * Runs once before all tests
 */

export default async function globalSetup() {
  console.log('🧪 Setting up Jest test environment...');
  
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.SHOPIFY_APP_URL = 'https://test-app.example.com';
  process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test';
  process.env.SUPPORTED_STORES = 'test-store.myshopify.com,another-store.myshopify.com';
  process.env.EXPRESS_PORT = '4000';
  
  // Mock any global dependencies that need to be available
  global.testStartTime = Date.now();
  
  console.log('✅ Jest test environment setup complete');
}
