/**
 * Babel configuration for Jest and ES modules
 */

export default {
  presets: [
    [
      '@babel/preset-env',
      {
        targets: {
          node: 'current',
        },
        modules: 'auto', // Let Babel decide based on environment
      },
    ],
  ],
  env: {
    test: {
      presets: [
        [
          '@babel/preset-env',
          {
            targets: {
              node: 'current',
            },
            modules: 'commonjs', // Use CommonJS for Jest
          },
        ],
      ],
    },
  },
};
