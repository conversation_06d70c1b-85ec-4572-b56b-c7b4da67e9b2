import { PrismaClient } from "@prisma/client";
import shopify from "../app/shopify.server";
import { randomUUID } from "crypto";

// Initialize Prisma client
const db = new PrismaClient();

// Configuration
const INTERVAL_MS = 30000; // 30 seconds between order cycles
const MAX_ITEMS_PER_ORDER = 20; // Maximum number of items per order
const MAX_PRODUCTS_TO_FETCH = 50; // Number of random products to cache for each shop
const FETCH_LIMIT = 250; // Maximum number of products to fetch at once
const CACHE_REFRESH_INTERVAL = 60 * 60 * 1000; // 1 hour - how often to refresh the product cache

// Rate limiting configuration
const DELAY_BETWEEN_SHOPS_MS = 5000; // 5 seconds between processing each shop
const MAX_RETRIES = 3; // Maximum number of retries for a failed API call
const INITIAL_RETRY_DELAY_MS = 1000; // Start with a 1 second delay for retries
const MAX_CALLS_PER_MINUTE = 40; // Shopify's standard rate limit is ~40 calls per minute
const RATE_LIMIT_WINDOW_MS = 60 * 1000; // 1 minute window for rate limiting

// Track API calls to stay under rate limits
const apiCallTracker = {
  calls: [],

  // Add a timestamp for a new API call
  addCall() {
    const now = Date.now();
    this.calls.push(now);
    // Clean up old calls outside the rate limit window
    this.calls = this.calls.filter(time => now - time < RATE_LIMIT_WINDOW_MS);
  },

  // Check if we're approaching the rate limit
  isApproachingLimit() {
    return this.calls.length >= MAX_CALLS_PER_MINUTE * 0.8; // 80% of limit
  },

  // Get recommended delay based on current call volume
  getRecommendedDelay() {
    if (this.calls.length === 0) return 0;

    const callsInWindow = this.calls.length;
    const usagePercentage = callsInWindow / MAX_CALLS_PER_MINUTE;

    if (usagePercentage < 0.5) return 0; // Under 50% usage, no delay
    if (usagePercentage < 0.7) return 500; // 50-70% usage, small delay
    if (usagePercentage < 0.9) return 1000; // 70-90% usage, medium delay
    return 2000; // Over 90% usage, larger delay
  },

  // Reset the call tracker
  reset() {
    this.calls = [];
  }
};

// Cache to store products for each shop
const shopProductsCache = new Map();

/**
 * Get all shops from the database
 * @returns {Promise<Array>} - Array of shops
 */
async function getShops() {
  try {
    // Get all unique shops from the Session table
    const shops = await db.session.findMany({
      select: {
        shop: true,
        accessToken: true
      },
      distinct: ['shop']
    });

    return shops;
  } catch (error) {
    console.error(`Error getting shops: ${error.message}`);
    return [];
  }
}

/**
 * Sleep for a specified number of milliseconds
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise<void>}
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Add jitter to a delay value to prevent synchronized retries
 * @param {number} delay - Base delay in milliseconds
 * @param {number} jitterFactor - Factor to determine jitter amount (0-1)
 * @returns {number} - Delay with jitter added
 */
function addJitter(delay, jitterFactor = 0.3) {
  const jitterAmount = delay * jitterFactor;
  return delay + (Math.random() * jitterAmount * 2) - jitterAmount;
}

/**
 * Make a rate-limited GraphQL call with retries and backoff
 * @param {object} shopAdmin - The admin API client
 * @param {string} query - GraphQL query
 * @param {object} variables - Query variables
 * @returns {Promise<object>} - Query response
 */
async function rateLimitedGraphQL(shopAdmin, query, variables = {}) {
  let retries = 0;
  let delay = INITIAL_RETRY_DELAY_MS;

  while (true) {
    try {
      // Check if we're approaching rate limits and add delay if needed
      const recommendedDelay = apiCallTracker.getRecommendedDelay();
      if (recommendedDelay > 0) {
        const delayWithJitter = addJitter(recommendedDelay);
        console.log(`Rate limit approaching, delaying for ${delayWithJitter}ms before API call`);
        await sleep(delayWithJitter);
      }

      // Track this API call
      apiCallTracker.addCall();

      // Make the API call
      const response = await shopAdmin.graphql(query, { variables });
      return response;
    } catch (error) {
      // Check if this is a rate limit error
      const isRateLimitError =
        error.message?.includes('Throttled') ||
        error.message?.includes('rate limit') ||
        error.message?.includes('Too many requests');

      // If we've hit the max retries or it's not a rate limit error, throw
      if (retries >= MAX_RETRIES || !isRateLimitError) {
        throw error;
      }

      // Exponential backoff with jitter
      retries++;
      const backoffDelay = addJitter(delay * Math.pow(2, retries - 1));
      console.log(`Rate limit hit, backing off for ${backoffDelay}ms (retry ${retries}/${MAX_RETRIES})`);
      await sleep(backoffDelay);

      // Reset the API call tracker after a backoff
      apiCallTracker.reset();
    }
  }
}

/**
 * Fetches products from a shop and randomly selects a subset
 * @param {string} shop - The shop domain
 * @param {object} shopAdmin - The admin API client for the shop
 * @returns {Promise<Array>} - Array of random products
 */
async function fetchShopProducts(shop, shopAdmin) {
  try {
    // Check if we already have products cached for this shop
    if (shopProductsCache.has(shop)) {
      return shopProductsCache.get(shop);
    }

    console.log(`Fetching products for shop: ${shop}`);

    // First, get the total count of products
    const countQuery = "query GetProductCount { products { pageInfo { totalCount } } }";
    const countResponse = await rateLimitedGraphQL(shopAdmin, countQuery);

    // The response is already parsed by the Shopify client
    if (countResponse.errors) {
      console.error(`Error fetching product count for ${shop}:`, countResponse.errors);
      return [];
    }

    const totalProducts = countResponse.body.data.products.pageInfo.totalCount;

    if (totalProducts === 0) {
      console.log(`Shop ${shop} has no products`);
      return [];
    }

    console.log(`Shop ${shop} has ${totalProducts} total products`);

    // Fetch more products than we need
    const fetchLimit = Math.min(FETCH_LIMIT, MAX_PRODUCTS_TO_FETCH * 5);

    // If there are more products than our fetch limit, choose a random starting point
    let randomOffset = 0;
    if (totalProducts > fetchLimit) {
      // Make sure we don't go past the end of the products
      randomOffset = Math.floor(Math.random() * (totalProducts - fetchLimit));
      console.log(`Using random offset ${randomOffset} for shop ${shop}`);
    }

    // Query products from Shopify using GraphQL with the random offset
    const productsQuery = "query GetProducts($fetchLimit: Int!, $randomOffset: Int!) { products(first: $fetchLimit, offset: $randomOffset) { edges { node { id title productType variants(first: 10) { edges { node { id title sku price inventoryQuantity availableForSale } } } } } } }";
    const response = await rateLimitedGraphQL(shopAdmin, productsQuery, {
      fetchLimit,
      randomOffset
    });

    // The response is already parsed by the Shopify client
    if (response.errors) {
      console.error(`Error fetching products for ${shop}:`, response.errors);
      return [];
    }

    // Process and format the products
    const allProducts = response.body.data.products.edges.map(edge => {
      const product = edge.node;

      // Only include variants that have SKUs and are available for sale
      const variants = product.variants.edges
        .map(variantEdge => variantEdge.node)
        .filter(variant => variant.sku && variant.availableForSale);

      if (variants.length === 0) {
        return null; // Skip products with no valid variants
      }

      return {
        id: product.id,
        title: product.title,
        productType: product.productType,
        variants
      };
    }).filter(product => product !== null);

    // If we have fewer products than requested, use all of them
    if (allProducts.length <= MAX_PRODUCTS_TO_FETCH) {
      shopProductsCache.set(shop, allProducts);
      console.log(`Cached ${allProducts.length} products for shop: ${shop}`);
      return allProducts;
    }

    // Randomly select MAX_PRODUCTS_TO_FETCH products
    const randomProducts = [];
    const indices = new Set();

    while (indices.size < MAX_PRODUCTS_TO_FETCH && indices.size < allProducts.length) {
      const randomIndex = Math.floor(Math.random() * allProducts.length);
      if (!indices.has(randomIndex)) {
        indices.add(randomIndex);
        randomProducts.push(allProducts[randomIndex]);
      }
    }

    // Cache the random products for future use
    shopProductsCache.set(shop, randomProducts);

    console.log(`Cached ${randomProducts.length} random products (from ${allProducts.length} total) for shop: ${shop}`);
    return randomProducts;
  } catch (error) {
    console.error(`Error fetching products for ${shop}:`, error.message);
    return [];
  }
}

/**
 * Creates a test order with random products for a shop
 * @param {string} shop - The shop domain
 * @param {object} shopAdmin - The admin API client for the shop
 * @returns {Promise<object|null>} - The created order or null if failed
 */
async function createTestOrder(shop, shopAdmin) {
  try {
    // Fetch products for this shop
    const products = await fetchShopProducts(shop, shopAdmin);

    if (products.length === 0) {
      console.error(`No products available for shop: ${shop}`);
      return null;
    }

    // Determine how many different products to include (1 to 5)
    const numProductTypes = Math.min(Math.floor(Math.random() * 5) + 1, products.length);

    // Randomly select products
    const selectedProducts = [];
    const usedProductIndices = new Set();

    let totalItems = 0;

    // Select random products until we have enough or reach the maximum
    while (selectedProducts.length < numProductTypes && totalItems < MAX_ITEMS_PER_ORDER) {
      // Pick a random product that we haven't used yet
      let productIndex;
      do {
        productIndex = Math.floor(Math.random() * products.length);
      } while (usedProductIndices.has(productIndex) && usedProductIndices.size < products.length);

      // If we've used all products, break
      if (usedProductIndices.size >= products.length) {
        break;
      }

      usedProductIndices.add(productIndex);
      const product = products[productIndex];

      // Pick a random variant
      const variant = product.variants[Math.floor(Math.random() * product.variants.length)];

      // Determine quantity (1 to 5)
      const quantity = Math.floor(Math.random() * 5) + 1;

      // Make sure we don't exceed the maximum items
      const adjustedQuantity = Math.min(quantity, MAX_ITEMS_PER_ORDER - totalItems);
      if (adjustedQuantity <= 0) {
        break;
      }

      totalItems += adjustedQuantity;

      selectedProducts.push({
        variantId: variant.id,
        title: product.title,
        variantTitle: variant.title,
        sku: variant.sku,
        price: variant.price,
        quantity: adjustedQuantity
      });
    }

    if (selectedProducts.length === 0) {
      console.error(`Could not select any valid products for shop: ${shop}`);
      return null;
    }

    // Create a unique order ID for tracking
    const testOrderId = `test-${randomUUID()}`;

    // Prepare line items for the order
    const lineItems = selectedProducts.map(product => ({
      variantId: product.variantId,
      quantity: product.quantity
    }));

    // Create the order using GraphQL mutation
    const orderCreateMutation = "mutation orderCreate($input: OrderInput!) { orderCreate(input: $input, test: true) { order { id legacyResourceId name test totalPriceSet { shopMoney { amount currencyCode } } } userErrors { field message } } }";

    const orderVariables = {
      input: {
        note: "Test order created by automated script - " + testOrderId,
        email: "<EMAIL>",
        phone: "+15551234567",
        shippingAddress: {
          firstName: "Test",
          lastName: "Customer",
          address1: "123 Test St",
          city: "Test City",
          province: "AL",
          zip: "12345",
          country: "US"
        },
        lineItems,
        customAttributes: [
          {
            key: "test_order",
            value: "true"
          }
        ],
        tags: ["test-order", "automated"]
      }
    };

    const response = await rateLimitedGraphQL(shopAdmin, orderCreateMutation, {
      input: orderVariables.input
    });

    // The response is already parsed by the Shopify client
    const data = response;

    if (data.errors) {
      console.error(`Error creating test order for ${shop}:`, data.errors);
      return null;
    }

    if (data.data.orderCreate.userErrors && data.data.orderCreate.userErrors.length > 0) {
      console.error(`Error creating test order for ${shop}:`, data.data.orderCreate.userErrors);
      return null;
    }

    const order = data.data.orderCreate.order;
    console.log("Created " + (order.test ? 'TEST ' : '') + "order #" + order.name + " for " + shop + " with " + totalItems + " items, total: " + order.totalPriceSet.shopMoney.amount + " " + order.totalPriceSet.shopMoney.currencyCode);
    return order;
  } catch (error) {
    console.error(`Error creating test order for ${shop}:`, error.message);
    return null;
  }
}

/**
 * Main function to run the script
 */
async function main() {
  try {
    // Get all shops
    const shops = await getShops();
    console.log(`Found ${shops.length} shops to process`);

    if (shops.length === 0) {
      console.error("No shops found. Exiting.");
      process.exit(1);
    }

    // Initialize admin API clients for each shop
    const shopAdmins = new Map();

    for (const shopData of shops) {
      try {
        const shopAdmin = shopify.admin.graphql.session.fromShop(shopData.shop);
        shopAdmins.set(shopData.shop, shopAdmin);
      } catch (error) {
        console.error(`Error initializing admin API for shop ${shopData.shop}:`, error.message);
      }
    }

    // Pre-fetch products for all shops sequentially to avoid rate limits
    console.log("Pre-fetching products for all shops...");

    for (const [shop, admin] of shopAdmins.entries()) {
      console.log(`Pre-fetching products for shop: ${shop}`);
      try {
        await fetchShopProducts(shop, admin);
        // Add a small delay between shops
        await sleep(addJitter(DELAY_BETWEEN_SHOPS_MS));
      } catch (error) {
        console.error(`Error pre-fetching products for shop ${shop}:`, error.message);
      }
    }

    console.log("Finished pre-fetching products");

    // Set up interval to create orders
    console.log(`Starting to create test orders every ${INTERVAL_MS / 1000} seconds`);

    // Function to process all shops sequentially
    async function processAllShops() {
      console.log(`Starting order cycle at ${new Date().toISOString()}`);

      for (const [shop, admin] of shopAdmins.entries()) {
        try {
          console.log(`Processing shop: ${shop}`);
          await createTestOrder(shop, admin);

          // Add a delay between shops to avoid rate limits
          if (shopAdmins.size > 1) {
            const delayMs = addJitter(DELAY_BETWEEN_SHOPS_MS);
            console.log(`Waiting ${delayMs}ms before processing next shop`);
            await sleep(delayMs);
          }
        } catch (error) {
          console.error(`Error creating test order for shop ${shop}:`, error.message);
        }
      }

      console.log(`Completed order cycle at ${new Date().toISOString()}`);
    }

    // Create initial orders immediately
    await processAllShops();

    // Set up interval for subsequent order cycles
    setInterval(processAllShops, INTERVAL_MS);

    // Function to refresh product cache for all shops
    async function refreshProductCache() {
      console.log(`Starting product cache refresh at ${new Date().toISOString()}`);

      // Clear the cache
      shopProductsCache.clear();

      // Fetch new random products for all shops sequentially
      for (const [shop, admin] of shopAdmins.entries()) {
        try {
          console.log(`Refreshing product cache for shop: ${shop}`);
          await fetchShopProducts(shop, admin);

          // Add a delay between shops
          if (shopAdmins.size > 1) {
            await sleep(addJitter(DELAY_BETWEEN_SHOPS_MS));
          }
        } catch (error) {
          console.error(`Error refreshing products for shop ${shop}:`, error.message);
        }
      }

      console.log(`Completed product cache refresh at ${new Date().toISOString()}`);
    }

    // Set up interval to refresh product cache periodically
    setInterval(refreshProductCache, CACHE_REFRESH_INTERVAL);

    console.log(`Script running. Creating test orders every ${INTERVAL_MS / 1000} seconds.`);

    // Keep the script running
    process.stdin.resume();
  } catch (error) {
    console.error("Error running script:", error.message);
    process.exit(1);
  }
}

// Run the script
main().catch(error => {
  console.error("Fatal error:", error);
  process.exit(1);
});
