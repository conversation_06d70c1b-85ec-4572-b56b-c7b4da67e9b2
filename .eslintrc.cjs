/** @type {import('@types/eslint').Linter.BaseConfig} */
module.exports = {
  root: true,
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 2024,
    "sourceType": "module",
    "project": "./tsconfig.json", // <-- Point to your project's "tsconfig.json" or create a new one.
    "tsconfigRootDir": __dirname,
  },
  plugins: ["deprecation"],
  extends: [
    "plugin:deprecation/recommended",
    "@remix-run/eslint-config",
    "@remix-run/eslint-config/node",
    "@remix-run/eslint-config/jest-testing-library",
    "prettier",
  ],
  globals: {
    shopify: "readonly"
  },
  "rules": {
      "no-unused-vars": "warn",
      "deprecation/deprecation": "warn",
    }
};
