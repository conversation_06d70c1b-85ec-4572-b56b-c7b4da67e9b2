# fly.toml app configuration file generated for americans-united-inc on 2025-05-29T13:27:38-05:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'americans-united-inc'
primary_region = 'ord'

[build]
  dockerfile = 'Dockerfile.multiprocess'

[deploy]
  release_command = 'npm run setup'

[env]
  EXPRESS_PORT = '4000'
  NODE_ENV = 'production'
  PORT = '3000'

[processes]
  multistore = 'npm run start:express:production'
  web = 'npm run start:production'

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 1
  processes = ['web']

  [http_service.concurrency]
    type = 'connections'
    hard_limit = 50
    soft_limit = 40

  [[http_service.checks]]
    interval = '30s'
    timeout = '10s'
    grace_period = '10s'
    method = 'GET'
    path = '/healthz'
    protocol = 'http'
    tls_skip_verify = false

[[services]]
  protocol = 'tcp'
  internal_port = 4000
  processes = ['multistore']

  [[services.ports]]
    port = 4000
    handlers = ['http']

  [services.concurrency]
    type = 'connections'
    hard_limit = 25
    soft_limit = 20

  [[services.tcp_checks]]
    interval = '15s'
    timeout = '5s'
    grace_period = '5s'

  [[services.http_checks]]
    interval = '30s'
    timeout = '10s'
    grace_period = '10s'
    method = 'GET'
    path = '/express/health'
    protocol = 'http'

# Removed global checks - each service has its own health checks configured
# The web process has health checks via http_service.checks
# The multistore process has health checks via services.http_checks

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1

[[statics]]
  guest_path = '/app/public'
  url_prefix = '/public/'
