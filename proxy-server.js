import express from 'express';
import { createProxyMiddleware } from 'http-proxy-middleware';
import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';

const app = express();
const PROXY_PORT = 3000;
let remixPort = null;
let remixProcess = null;

// Function to extract port from Remix server output
function extractPortFromOutput(data) {
  const output = data.toString();

  // Skip GraphiQL server port (we want the main Remix server)
  if (output.includes('GraphiQL server')) {
    return null;
  }

  // Look for the main Remix server port patterns
  const patterns = [
    /➜\s+Local:\s+http:\/\/localhost:(\d+)\//,
    /Local:\s+http:\/\/localhost:(\d+)\//,
    /Remix.*port\s+(\d+)/i,
    /Vite.*port\s+(\d+)/i
  ];

  for (const pattern of patterns) {
    const match = output.match(pattern);
    if (match) {
      const port = parseInt(match[1]);
      // Skip common non-Remix ports
      if (port === 3457 || port === 4000) {
        continue;
      }
      console.log(`🔍 Found Remix server port ${port} using pattern: ${pattern}`);
      return port;
    }
  }
  return null;
}

// Function to start the Remix server and get its port
function startRemixServer() {
  return new Promise((resolve, reject) => {
    console.log('🚀 Starting Remix server...');

    // Start the Shopify CLI dev server without tunnel URL to avoid port conflicts
    remixProcess = spawn('shopify', ['app', 'dev'], {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: { ...process.env }
    });

    let outputBuffer = '';

    remixProcess.stdout.on('data', (data) => {
      const output = data.toString();
      outputBuffer += output;

      // Forward output to console
      process.stdout.write(`[remix] ${output}`);

      // Check if we found the port
      const port = extractPortFromOutput(output);
      if (port && !remixPort) {
        remixPort = port;
        console.log(`✅ Remix server detected on port ${remixPort}`);
        resolve(port);
      }
    });

    remixProcess.stderr.on('data', (data) => {
      const output = data.toString();
      process.stderr.write(`[remix] ${output}`);

      // Also check stderr for port info
      const port = extractPortFromOutput(output);
      if (port && !remixPort) {
        remixPort = port;
        console.log(`✅ Remix server detected on port ${remixPort}`);
        resolve(port);
      }
    });

    remixProcess.on('error', (error) => {
      console.error('❌ Failed to start Remix server:', error);
      reject(error);
    });

    remixProcess.on('exit', (code) => {
      console.log(`🛑 Remix server exited with code ${code}`);
      remixPort = null;
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      if (!remixPort) {
        reject(new Error('Timeout waiting for Remix server to start'));
      }
    }, 30000);
  });
}

// Setup proxy middleware
function setupProxy() {
  if (!remixPort) {
    console.error('❌ No Remix port available for proxy');
    return;
  }

  const proxyOptions = {
    target: `http://localhost:${remixPort}`,
    changeOrigin: true,
    ws: true, // Enable WebSocket proxying
    onError: (err, req, res) => {
      console.error('❌ Proxy error:', err.message);
      if (!res.headersSent) {
        res.status(500).json({ error: 'Proxy error', message: err.message });
      }
    },
    onProxyReq: (proxyReq, req, res) => {
      console.log(`🔄 Proxying ${req.method} ${req.url} to port ${remixPort}`);
    }
  };

  // Create proxy middleware
  const proxy = createProxyMiddleware(proxyOptions);

  // Clear existing middleware and add proxy
  app._router = null;
  app.use('/', proxy);

  console.log(`✅ Proxy configured: localhost:${PROXY_PORT} -> localhost:${remixPort}`);
}

// Health check endpoint (before proxy setup)
app.get('/proxy-health', (req, res) => {
  res.json({
    status: 'ok',
    proxyPort: PROXY_PORT,
    remixPort: remixPort,
    timestamp: new Date().toISOString()
  });
});

// Start the proxy server
async function startProxy() {
  try {
    console.log('🔧 Starting proxy server setup...');

    // Start Remix server and wait for port
    await startRemixServer();

    // Setup proxy
    setupProxy();

    // Start proxy server
    const server = app.listen(PROXY_PORT, () => {
      console.log(`🎯 Proxy server running on http://localhost:${PROXY_PORT}`);
      console.log(`📡 Forwarding to Remix server on http://localhost:${remixPort}`);
      console.log(`🏥 Health check: http://localhost:${PROXY_PORT}/proxy-health`);
    });

    // Handle WebSocket upgrades
    server.on('upgrade', (request, socket, head) => {
      if (remixPort) {
        const proxy = createProxyMiddleware({
          target: `http://localhost:${remixPort}`,
          ws: true,
          changeOrigin: true
        });
        proxy.upgrade(request, socket, head);
      }
    });

    // Graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down proxy server...');
      if (remixProcess) {
        remixProcess.kill('SIGTERM');
      }
      server.close(() => {
        console.log('✅ Proxy server shut down');
        process.exit(0);
      });
    });

  } catch (error) {
    console.error('❌ Failed to start proxy:', error);
    process.exit(1);
  }
}

// Start the proxy
startProxy();
