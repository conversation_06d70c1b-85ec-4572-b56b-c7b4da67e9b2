/**
 * Tests for Express multistore server
 */

import { jest } from '@jest/globals';
import request from 'supertest';

// Mock dependencies before importing the server
jest.mock('../shared/config/index.js', () => ({
  config: {
    app: {
      environment: 'test',
    },
    multistore: {
      supportedStores: ['test-store.myshopify.com'],
      storeConfigs: {
        'test-store.myshopify.com': {
          apiKey: 'test-api-key',
          apiSecret: 'test-api-secret',
        },
      },
    },
  },
  expressConfig: {
    port: 4000,
    cors: {
      origin: 'http://localhost:3000',
      credentials: true,
    },
    security: {
      helmet: {
        contentSecurityPolicy: {
          directives: {
            frameAncestors: ["'self'", "https://*.shopify.com"],
          },
        },
      },
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000,
      max: 100,
    },
    bodyParser: {
      limit: '10mb',
      extended: true,
    },
    timeout: {
      server: 30000,
      request: 25000,
    },
  },
  environmentConfig: {
    monitoring: {
      enabled: false,
    },
  },
}));

jest.mock('../shared/utils/index.js', () => ({
  formatSuccessResponse: jest.fn((data, message) => ({
    success: true,
    data,
    message,
    meta: {
      timestamp: new Date().toISOString(),
    },
  })),
  getMemoryUsage: jest.fn(() => ({
    rss: '100 MB',
    heapTotal: '50 MB',
    heapUsed: '30 MB',
    external: '5 MB',
    arrayBuffers: '1 MB',
  })),
}));

jest.mock('./middleware/multistore.js', () => ({
  multistoreMiddleware: jest.fn((req, res, next) => {
    req.shop = 'test-store.myshopify.com';
    req.requestId = 'test-request-id';
    next();
  }),
  requestLoggingMiddleware: jest.fn((req, res, next) => next()),
  multistoreErrorHandler: jest.fn((error, req, res, next) => {
    res.status(500).json({
      success: false,
      error: {
        message: error.message,
        code: 'INTERNAL_SERVER_ERROR',
        timestamp: new Date().toISOString(),
      },
    });
  }),
}));

jest.mock('./routes/auth.js', () => {
  const express = require('express');
  const router = express.Router();
  
  router.get('/health', (req, res) => {
    res.json({
      success: true,
      data: {
        service: 'authentication',
        status: 'healthy',
      },
    });
  });
  
  return router;
});

jest.mock('./routes/webhooks.js', () => {
  const express = require('express');
  const router = express.Router();
  
  router.get('/health', (req, res) => {
    res.json({
      success: true,
      data: {
        service: 'webhooks',
        status: 'healthy',
      },
    });
  });
  
  return router;
});

jest.mock('./routes/api.js', () => {
  const express = require('express');
  const router = express.Router();
  
  router.get('/health', (req, res) => {
    res.json({
      success: true,
      data: {
        service: 'api',
        status: 'healthy',
      },
    });
  });
  
  return router;
});

// Mock process.uptime
jest.spyOn(process, 'uptime').mockReturnValue(3600); // 1 hour

describe('Express Multistore Server', () => {
  let app;

  beforeAll(async () => {
    // Import the server after mocks are set up
    const serverModule = await import('./server.js');
    app = serverModule.default;
  });

  afterAll(() => {
    // Clean up any resources if needed
  });

  describe('Health Check Endpoints', () => {
    it('should return health status for root health check', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          service: 'express-multistore-server',
          status: 'healthy',
          environment: 'test',
          uptime: '60m 0s',
          memory: {
            rss: '100 MB',
            heapTotal: '50 MB',
            heapUsed: '30 MB',
            external: '5 MB',
            arrayBuffers: '1 MB',
          },
        },
        message: 'Express multistore server is healthy',
      });
    });

    it('should return server information', async () => {
      const response = await request(app)
        .get('/info')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          name: 'Express Multistore Server',
          environment: 'test',
          supportedStores: ['test-store.myshopify.com'],
          features: [
            'multistore-authentication',
            'webhook-processing',
            'api-endpoints',
            'rate-limiting',
            'memory-management',
          ],
          endpoints: {
            auth: '/express/auth',
            webhooks: '/express/webhooks',
            api: '/express/api',
            health: '/health',
          },
        },
        message: 'Server information',
      });
    });
  });

  describe('Route Health Checks', () => {
    it('should return auth service health', async () => {
      const response = await request(app)
        .get('/express/auth/health')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          service: 'authentication',
          status: 'healthy',
        },
      });
    });

    it('should return webhooks service health', async () => {
      const response = await request(app)
        .get('/express/webhooks/health')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          service: 'webhooks',
          status: 'healthy',
        },
      });
    });

    it('should return API service health', async () => {
      const response = await request(app)
        .get('/express/api/health')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          service: 'api',
          status: 'healthy',
        },
      });
    });
  });

  describe('Error Handling', () => {
    it('should return 404 for undefined routes', async () => {
      const response = await request(app)
        .get('/nonexistent-route')
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        error: {
          message: 'Route not found: GET /nonexistent-route',
          code: 'ROUTE_NOT_FOUND',
        },
      });
    });

    it('should handle POST requests to undefined routes', async () => {
      const response = await request(app)
        .post('/nonexistent-route')
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        error: {
          message: 'Route not found: POST /nonexistent-route',
          code: 'ROUTE_NOT_FOUND',
        },
      });
    });
  });

  describe('Middleware Integration', () => {
    it('should apply multistore middleware to /express routes', async () => {
      const { multistoreMiddleware } = require('./middleware/multistore.js');
      
      await request(app)
        .get('/express/auth/health')
        .expect(200);

      expect(multistoreMiddleware).toHaveBeenCalled();
    });

    it('should apply request logging middleware', async () => {
      const { requestLoggingMiddleware } = require('./middleware/multistore.js');
      
      await request(app)
        .get('/health')
        .expect(200);

      expect(requestLoggingMiddleware).toHaveBeenCalled();
    });
  });

  describe('Security Headers', () => {
    it('should include security headers in responses', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      // Check for common security headers that helmet adds
      expect(response.headers).toHaveProperty('x-content-type-options');
      expect(response.headers).toHaveProperty('x-frame-options');
    });
  });

  describe('CORS Configuration', () => {
    it('should handle CORS preflight requests', async () => {
      const response = await request(app)
        .options('/health')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'GET')
        .expect(204);

      expect(response.headers).toHaveProperty('access-control-allow-origin');
    });
  });

  describe('Rate Limiting', () => {
    it('should apply rate limiting to requests', async () => {
      // Make multiple requests to test rate limiting
      const requests = Array(5).fill().map(() => 
        request(app).get('/health')
      );

      const responses = await Promise.all(requests);
      
      // All requests should succeed (under the limit)
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });

      // Check for rate limit headers
      const lastResponse = responses[responses.length - 1];
      expect(lastResponse.headers).toHaveProperty('x-ratelimit-limit');
      expect(lastResponse.headers).toHaveProperty('x-ratelimit-remaining');
    });
  });
});
