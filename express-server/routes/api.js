/**
 * API routes for Express multistore server
 * Handles API endpoints that require multistore functionality
 */

import express from 'express';
import { shopifyAuthMiddleware } from '../middleware/multistore.js';
import { formatSuccessResponse, formatErrorResponse, validateRequiredFields } from '../../shared/utils/index.js';
import { HTTP_STATUS, ERROR_CODES } from '../../shared/constants/index.js';
import { processLargeDataset } from '../../shared/utils/processLargeDataset.js';

const router = express.Router();

/**
 * Get store information
 * Route: GET /express/api/:store/info
 */
router.get('/:store/info', shopifyAuthMiddleware, async (req, res) => {
  try {
    const { store } = req.params;

    // Use the authenticated session to get store info
    const shopInfo = await req.shopifyApp.api.rest.Shop.all({
      session: req.session,
    });

    const response = formatSuccessResponse({
      store: req.shop,
      info: shopInfo.data[0],
    }, 'Store information retrieved');

    res.json(response);
  } catch (error) {
    console.error(`[${req.requestId}] Store info error:`, error);

    const errorResponse = formatErrorResponse(
      'Failed to retrieve store information',
      ERROR_CODES.SHOPIFY_API_ERROR,
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );

    res.status(errorResponse.statusCode).json(errorResponse);
  }
});

/**
 * Get orders for a specific store
 * Route: GET /express/api/:store/orders
 */
router.get('/:store/orders', shopifyAuthMiddleware, async (req, res) => {
  try {
    const { store } = req.params;
    const {
      limit = 50,
      since_id,
      created_at_min,
      created_at_max,
      status = 'any',
      financial_status,
      fulfillment_status
    } = req.query;

    // Build query parameters
    const queryParams = {
      session: req.session,
      limit: Math.min(parseInt(limit), 250), // Shopify max is 250
      status,
    };

    if (since_id) queryParams.since_id = since_id;
    if (created_at_min) queryParams.created_at_min = created_at_min;
    if (created_at_max) queryParams.created_at_max = created_at_max;
    if (financial_status) queryParams.financial_status = financial_status;
    if (fulfillment_status) queryParams.fulfillment_status = fulfillment_status;

    // Fetch orders from Shopify
    const orders = await req.shopifyApp.api.rest.Order.all(queryParams);

    const response = formatSuccessResponse({
      store: req.shop,
      orders: orders.data,
      count: orders.data.length,
      pagination: {
        limit: queryParams.limit,
        hasMore: orders.data.length === queryParams.limit,
      },
    }, 'Orders retrieved successfully');

    res.json(response);
  } catch (error) {
    console.error(`[${req.requestId}] Orders fetch error:`, error);

    const errorResponse = formatErrorResponse(
      'Failed to retrieve orders',
      ERROR_CODES.SHOPIFY_API_ERROR,
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );

    res.status(errorResponse.statusCode).json(errorResponse);
  }
});

/**
 * Process orders for a specific store
 * Route: POST /express/api/:store/orders/process
 */
router.post('/:store/orders/process', shopifyAuthMiddleware, async (req, res) => {
  try {
    const { store } = req.params;
    const { order_ids, date_range, batch_size = 50 } = req.body;

    // Validate input
    if (!order_ids && !date_range) {
      const error = formatErrorResponse(
        'Either order_ids or date_range must be provided',
        ERROR_CODES.VALIDATION_REQUIRED_FIELD,
        HTTP_STATUS.BAD_REQUEST
      );
      return res.status(error.statusCode).json(error);
    }

    let orders = [];

    // Fetch orders based on input
    if (order_ids) {
      // Fetch specific orders
      for (const orderId of order_ids) {
        try {
          const order = await req.shopifyApp.api.rest.Order.find({
            session: req.session,
            id: orderId,
          });
          orders.push(order);
        } catch (orderError) {
          console.warn(`[${req.requestId}] Failed to fetch order ${orderId}:`, orderError);
        }
      }
    } else if (date_range) {
      // Fetch orders by date range
      const ordersResponse = await req.shopifyApp.api.rest.Order.all({
        session: req.session,
        created_at_min: date_range.start,
        created_at_max: date_range.end,
        limit: 250,
        status: 'any',
      });
      orders = ordersResponse.data;
    }

    // Process orders using the large dataset processor
    const results = await processLargeDataset(
      orders,
      async (orderBatch) => {
        // TODO: Implement actual order processing logic
        // This would use the order processing service
        return {
          processed: orderBatch.length,
          success: true,
        };
      },
      {
        batchSize: parseInt(batch_size),
        onProgress: (progress) => {
          console.log(`[${req.requestId}] Order processing progress:`, progress);
        },
      }
    );

    const response = formatSuccessResponse({
      store: req.shop,
      totalOrders: orders.length,
      results,
      summary: {
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
      },
    }, 'Orders processed successfully');

    res.json(response);
  } catch (error) {
    console.error(`[${req.requestId}] Order processing error:`, error);

    const errorResponse = formatErrorResponse(
      'Failed to process orders',
      ERROR_CODES.ORDER_PROCESSING_FAILED,
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );

    res.status(errorResponse.statusCode).json(errorResponse);
  }
});

/**
 * Get products for a specific store
 * Route: GET /express/api/:store/products
 */
router.get('/:store/products', shopifyAuthMiddleware, async (req, res) => {
  try {
    const { store } = req.params;
    const { limit = 50, since_id, vendor, product_type } = req.query;

    const queryParams = {
      session: req.session,
      limit: Math.min(parseInt(limit), 250),
    };

    if (since_id) queryParams.since_id = since_id;
    if (vendor) queryParams.vendor = vendor;
    if (product_type) queryParams.product_type = product_type;

    const products = await req.shopifyApp.api.rest.Product.all(queryParams);

    const response = formatSuccessResponse({
      store: req.shop,
      products: products.data,
      count: products.data.length,
    }, 'Products retrieved successfully');

    res.json(response);
  } catch (error) {
    console.error(`[${req.requestId}] Products fetch error:`, error);

    const errorResponse = formatErrorResponse(
      'Failed to retrieve products',
      ERROR_CODES.SHOPIFY_API_ERROR,
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );

    res.status(errorResponse.statusCode).json(errorResponse);
  }
});

/**
 * Trigger reconciliation for a specific store
 * Route: POST /express/api/:store/reconciliation
 */
router.post('/:store/reconciliation', shopifyAuthMiddleware, async (req, res) => {
  try {
    const { store } = req.params;
    const { type = 'scheduled', date_range, options = {} } = req.body;

    // Validate reconciliation type
    const validTypes = ['scheduled', 'missing_order', 'shipstation', 'manual'];
    if (!validTypes.includes(type)) {
      const error = formatErrorResponse(
        `Invalid reconciliation type: ${type}`,
        ERROR_CODES.VALIDATION_INVALID_FORMAT,
        HTTP_STATUS.BAD_REQUEST
      );
      return res.status(error.statusCode).json(error);
    }

    // Import and use the reconciliation orchestrator service
    const { ReconciliationOrchestrator } = await import('../../app/services/reconciliation/ReconciliationOrchestrator.js');

    const reconciliationOrchestrator = new ReconciliationOrchestrator();

    // Map type to orchestrator type
    const typeMapping = {
      'scheduled': 'scheduled',
      'missing_order': 'missing_orders',
      'shipstation': 'shipstation_sync',
      'manual': 'scheduled'
    };

    const orchestratorType = typeMapping[type] || 'scheduled';

    // Prepare reconciliation options
    const reconciliationOptions = {
      shopDomain: req.shop,
      ...options,
    };

    if (date_range) {
      reconciliationOptions.startDate = new Date(date_range.start);
      reconciliationOptions.endDate = new Date(date_range.end);
    }

    // Execute reconciliation
    const result = await reconciliationOrchestrator.executeReconciliation(
      orchestratorType,
      reconciliationOptions
    );

    const response = formatSuccessResponse({
      store: req.shop,
      reconciliation: {
        type,
        status: result.success ? 'completed' : 'failed',
        jobId: result.jobId,
        result: result.result,
        date_range,
        options,
      },
    }, 'Reconciliation completed successfully');

    res.json(response);
  } catch (error) {
    console.error(`[${req.requestId}] Reconciliation error:`, error);

    const errorResponse = formatErrorResponse(
      'Failed to initiate reconciliation',
      ERROR_CODES.RECONCILIATION_FAILED,
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );

    res.status(errorResponse.statusCode).json(errorResponse);
  }
});

/**
 * Get multistore statistics
 * Route: GET /express/api/stats
 */
router.get('/stats', async (req, res) => {
  try {
    // TODO: Implement multistore statistics
    // This would aggregate data across all stores

    const response = formatSuccessResponse({
      totalStores: 0, // Placeholder
      activeStores: 0, // Placeholder
      totalOrders: 0, // Placeholder
      totalRevenue: 0, // Placeholder
    }, 'Multistore statistics retrieved');

    res.json(response);
  } catch (error) {
    console.error(`[${req.requestId}] Stats error:`, error);

    const errorResponse = formatErrorResponse(
      'Failed to retrieve statistics',
      ERROR_CODES.INTERNAL_SERVER_ERROR,
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );

    res.status(errorResponse.statusCode).json(errorResponse);
  }
});

/**
 * API health check
 * Route: GET /express/api/health
 */
router.get('/health', (req, res) => {
  const response = formatSuccessResponse({
    service: 'api',
    status: 'healthy',
    timestamp: new Date().toISOString(),
  }, 'API service is healthy');

  res.json(response);
});

export default router;
