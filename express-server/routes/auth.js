/**
 * Authentication routes for Express multistore server
 * Handles OAuth flows for multiple Shopify stores
 */

import express from 'express';
import { shopifyAuthMiddleware } from '../middleware/multistore.js';
import { formatSuccessResponse, formatErrorResponse } from '../../shared/utils/index.js';
import { HTTP_STATUS, ERROR_CODES } from '../../shared/constants/index.js';

const router = express.Router();

/**
 * OAuth callback route for store-specific authentication
 * Route: GET /express/auth/:store/callback
 */
router.get('/:store/callback', async (req, res) => {
  try {
    const { store } = req.params;
    
    // The shop should already be set by multistore middleware
    if (req.shop !== `${store}.myshopify.com`) {
      const error = formatErrorResponse(
        'Store mismatch in authentication',
        ERROR_CODES.AUTH_INVALID_TOKEN,
        HTTP_STATUS.BAD_REQUEST
      );
      return res.status(error.statusCode).json(error);
    }

    // Use store-specific Shopify app instance for OAuth callback
    return req.shopifyApp.authenticate.admin(req, res);
  } catch (error) {
    console.error(`[${req.requestId}] OAuth callback error:`, error);
    
    const errorResponse = formatErrorResponse(
      'Authentication failed',
      ERROR_CODES.AUTH_INVALID_TOKEN,
      HTTP_STATUS.UNAUTHORIZED
    );
    
    res.status(errorResponse.statusCode).json(errorResponse);
  }
});

/**
 * Initiate OAuth flow for a specific store
 * Route: GET /express/auth/:store/login
 */
router.get('/:store/login', async (req, res) => {
  try {
    const { store } = req.params;
    
    // Validate store parameter
    if (!store) {
      const error = formatErrorResponse(
        'Store parameter required',
        ERROR_CODES.VALIDATION_REQUIRED_FIELD,
        HTTP_STATUS.BAD_REQUEST
      );
      return res.status(error.statusCode).json(error);
    }

    // Set shop in query for multistore middleware
    req.query.shop = `${store}.myshopify.com`;

    // Use store-specific Shopify app instance to initiate OAuth
    return req.shopifyApp.authenticate.admin(req, res);
  } catch (error) {
    console.error(`[${req.requestId}] OAuth initiation error:`, error);
    
    const errorResponse = formatErrorResponse(
      'Failed to initiate authentication',
      ERROR_CODES.AUTH_INVALID_TOKEN,
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );
    
    res.status(errorResponse.statusCode).json(errorResponse);
  }
});

/**
 * Check authentication status for a store
 * Route: GET /express/auth/:store/status
 */
router.get('/:store/status', shopifyAuthMiddleware, async (req, res) => {
  try {
    const { store } = req.params;
    
    // If we reach here, authentication was successful
    const response = formatSuccessResponse({
      authenticated: true,
      store: req.shop,
      session: {
        shop: req.session?.shop,
        accessToken: req.session?.accessToken ? '***' : null, // Don't expose actual token
        scope: req.session?.scope,
      },
    }, 'Authentication status retrieved');

    res.json(response);
  } catch (error) {
    console.error(`[${req.requestId}] Auth status check error:`, error);
    
    const errorResponse = formatErrorResponse(
      'Failed to check authentication status',
      ERROR_CODES.AUTH_INVALID_TOKEN,
      HTTP_STATUS.UNAUTHORIZED
    );
    
    res.status(errorResponse.statusCode).json(errorResponse);
  }
});

/**
 * Logout/revoke authentication for a store
 * Route: POST /express/auth/:store/logout
 */
router.post('/:store/logout', async (req, res) => {
  try {
    const { store } = req.params;
    
    // Clear session for this store
    if (req.session) {
      req.session.destroy((err) => {
        if (err) {
          console.error(`[${req.requestId}] Session destruction error:`, err);
        }
      });
    }

    const response = formatSuccessResponse({
      loggedOut: true,
      store: `${store}.myshopify.com`,
    }, 'Successfully logged out');

    res.json(response);
  } catch (error) {
    console.error(`[${req.requestId}] Logout error:`, error);
    
    const errorResponse = formatErrorResponse(
      'Failed to logout',
      ERROR_CODES.INTERNAL_SERVER_ERROR,
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );
    
    res.status(errorResponse.statusCode).json(errorResponse);
  }
});

/**
 * Get OAuth URL for a specific store (for frontend redirects)
 * Route: GET /express/auth/:store/url
 */
router.get('/:store/url', async (req, res) => {
  try {
    const { store } = req.params;
    const { redirect_uri } = req.query;
    
    if (!store) {
      const error = formatErrorResponse(
        'Store parameter required',
        ERROR_CODES.VALIDATION_REQUIRED_FIELD,
        HTTP_STATUS.BAD_REQUEST
      );
      return res.status(error.statusCode).json(error);
    }

    // Construct OAuth URL
    const shopDomain = `${store}.myshopify.com`;
    const baseUrl = req.shopifyApp?.config?.appUrl || process.env.SHOPIFY_APP_URL;
    const authUrl = `${baseUrl}/express/auth/${store}/login`;
    
    // Add redirect_uri if provided
    const finalUrl = redirect_uri 
      ? `${authUrl}?redirect_uri=${encodeURIComponent(redirect_uri)}`
      : authUrl;

    const response = formatSuccessResponse({
      authUrl: finalUrl,
      store: shopDomain,
    }, 'OAuth URL generated');

    res.json(response);
  } catch (error) {
    console.error(`[${req.requestId}] OAuth URL generation error:`, error);
    
    const errorResponse = formatErrorResponse(
      'Failed to generate OAuth URL',
      ERROR_CODES.INTERNAL_SERVER_ERROR,
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );
    
    res.status(errorResponse.statusCode).json(errorResponse);
  }
});

/**
 * Validate session for a specific store
 * Route: POST /express/auth/:store/validate
 */
router.post('/:store/validate', async (req, res) => {
  try {
    const { store } = req.params;
    const { session_token } = req.body;
    
    if (!session_token) {
      const error = formatErrorResponse(
        'Session token required',
        ERROR_CODES.VALIDATION_REQUIRED_FIELD,
        HTTP_STATUS.BAD_REQUEST
      );
      return res.status(error.statusCode).json(error);
    }

    // Use Shopify app instance to validate session
    const session = await req.shopifyApp.sessionStorage.loadSession(session_token);
    
    if (!session || !session.isActive()) {
      const error = formatErrorResponse(
        'Invalid or expired session',
        ERROR_CODES.AUTH_EXPIRED_TOKEN,
        HTTP_STATUS.UNAUTHORIZED
      );
      return res.status(error.statusCode).json(error);
    }

    const response = formatSuccessResponse({
      valid: true,
      store: session.shop,
      expires: session.expires,
      scope: session.scope,
    }, 'Session validated');

    res.json(response);
  } catch (error) {
    console.error(`[${req.requestId}] Session validation error:`, error);
    
    const errorResponse = formatErrorResponse(
      'Session validation failed',
      ERROR_CODES.AUTH_INVALID_TOKEN,
      HTTP_STATUS.UNAUTHORIZED
    );
    
    res.status(errorResponse.statusCode).json(errorResponse);
  }
});

/**
 * Health check for authentication service
 * Route: GET /express/auth/health
 */
router.get('/health', (req, res) => {
  const response = formatSuccessResponse({
    service: 'authentication',
    status: 'healthy',
    timestamp: new Date().toISOString(),
  }, 'Authentication service is healthy');

  res.json(response);
});

export default router;
