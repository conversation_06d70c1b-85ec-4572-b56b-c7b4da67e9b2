/**
 * Webhook routes for Express multistore server
 * Handles webhooks from multiple Shopify stores and other services
 */

import express from 'express';
import { webhookVerificationMiddleware } from '../middleware/multistore.js';
import { formatSuccessResponse, formatErrorResponse } from '../../shared/utils/index.js';
import { HTTP_STATUS, ERROR_CODES, WEBHOOK_TYPES } from '../../shared/constants/index.js';

const router = express.Router();

// Middleware to parse raw body for webhook verification
router.use(express.raw({ type: 'application/json' }));

/**
 * Shopify webhook handler for store-specific webhooks
 * Route: POST /express/webhooks/shopify/:store/:topic
 */
router.post('/shopify/:store/:topic', webhookVerificationMiddleware, async (req, res) => {
  try {
    const { store, topic } = req.params;
    const payload = req.body;

    console.log(`[${req.requestId}] Received Shopify webhook - Store: ${store}, Topic: ${topic}`);

    // Validate webhook topic
    const validTopics = [
      'orders/create',
      'orders/updated',
      'orders/paid',
      'orders/cancelled',
      'orders/fulfilled',
      'app/uninstalled',
      'customers/data_request',
      'customers/redact',
      'shop/redact',
    ];

    if (!validTopics.includes(topic)) {
      const error = formatErrorResponse(
        `Unsupported webhook topic: ${topic}`,
        ERROR_CODES.VALIDATION_INVALID_FORMAT,
        HTTP_STATUS.BAD_REQUEST
      );
      return res.status(error.statusCode).json(error);
    }

    // Process webhook based on topic
    await processShopifyWebhook(store, topic, payload, req);

    const response = formatSuccessResponse({
      processed: true,
      store,
      topic,
      timestamp: new Date().toISOString(),
    }, 'Webhook processed successfully');

    res.json(response);
  } catch (error) {
    console.error(`[${req.requestId}] Shopify webhook processing error:`, error);

    const errorResponse = formatErrorResponse(
      'Webhook processing failed',
      ERROR_CODES.WEBHOOK_PROCESSING_FAILED,
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );

    res.status(errorResponse.statusCode).json(errorResponse);
  }
});

/**
 * ShipStation webhook handler
 * Route: POST /express/webhooks/shipstation
 */
router.post('/shipstation', async (req, res) => {
  try {
    const payload = req.body;

    console.log(`[${req.requestId}] Received ShipStation webhook`);

    // Verify ShipStation webhook (if verification is configured)
    if (process.env.SHIPSTATION_WEBHOOK_SECRET) {
      const signature = req.headers['x-shipstation-signature'];
      if (!verifyShipStationWebhook(payload, signature)) {
        const error = formatErrorResponse(
          'Invalid webhook signature',
          ERROR_CODES.AUTH_INVALID_TOKEN,
          HTTP_STATUS.UNAUTHORIZED
        );
        return res.status(error.statusCode).json(error);
      }
    }

    // Process ShipStation webhook
    await processShipStationWebhook(payload, req);

    const response = formatSuccessResponse({
      processed: true,
      type: 'shipstation',
      timestamp: new Date().toISOString(),
    }, 'ShipStation webhook processed successfully');

    res.json(response);
  } catch (error) {
    console.error(`[${req.requestId}] ShipStation webhook processing error:`, error);

    const errorResponse = formatErrorResponse(
      'ShipStation webhook processing failed',
      ERROR_CODES.WEBHOOK_PROCESSING_FAILED,
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );

    res.status(errorResponse.statusCode).json(errorResponse);
  }
});

/**
 * Generic webhook endpoint for testing
 * Route: POST /express/webhooks/test
 */
router.post('/test', async (req, res) => {
  try {
    const payload = req.body;

    console.log(`[${req.requestId}] Received test webhook:`, payload);

    const response = formatSuccessResponse({
      received: true,
      payload,
      headers: req.headers,
      timestamp: new Date().toISOString(),
    }, 'Test webhook received');

    res.json(response);
  } catch (error) {
    console.error(`[${req.requestId}] Test webhook error:`, error);

    const errorResponse = formatErrorResponse(
      'Test webhook failed',
      ERROR_CODES.INTERNAL_SERVER_ERROR,
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );

    res.status(errorResponse.statusCode).json(errorResponse);
  }
});

/**
 * Webhook health check
 * Route: GET /express/webhooks/health
 */
router.get('/health', (req, res) => {
  const response = formatSuccessResponse({
    service: 'webhooks',
    status: 'healthy',
    supportedTypes: [WEBHOOK_TYPES.SHOPIFY, WEBHOOK_TYPES.SHIPSTATION],
    timestamp: new Date().toISOString(),
  }, 'Webhook service is healthy');

  res.json(response);
});

/**
 * Process Shopify webhook based on topic
 * @param {string} store - Store domain
 * @param {string} topic - Webhook topic
 * @param {object} payload - Webhook payload
 * @param {object} req - Express request object
 */
async function processShopifyWebhook(store, topic, payload, req) {
  switch (topic) {
    case 'orders/create':
    case 'orders/updated':
      await handleOrderWebhook(store, topic, payload, req);
      break;

    case 'orders/paid':
      await handleOrderPaidWebhook(store, payload, req);
      break;

    case 'orders/cancelled':
      await handleOrderCancelledWebhook(store, payload, req);
      break;

    case 'orders/fulfilled':
      await handleOrderFulfilledWebhook(store, payload, req);
      break;

    case 'app/uninstalled':
      await handleAppUninstalledWebhook(store, payload, req);
      break;

    case 'customers/data_request':
    case 'customers/redact':
    case 'shop/redact':
      await handleGDPRWebhook(store, topic, payload, req);
      break;

    default:
      console.log(`[${req.requestId}] Unhandled webhook topic: ${topic}`);
  }
}

/**
 * Handle order creation/update webhooks
 */
async function handleOrderWebhook(store, topic, payload, req) {
  console.log(`[${req.requestId}] Processing ${topic} for order ${payload.id} in store ${store}`);

  try {
    // Import the OrderProcessingService
    const { OrderProcessingService } = await import('../../app/services/order/OrderProcessingService.js');

    const orderProcessingService = new OrderProcessingService();

    const result = await orderProcessingService.processOrder(
      payload.id,
      store,
      {
        skipValidation: false,
        dryRun: false,
        forceReprocess: topic === 'orders/updated' // Reprocess if it's an update
      }
    );

    if (result.success) {
      // Count processed vs skipped items
      const processedItems = result.result.lineItems.filter(item => !item.skipped);
      const skippedItems = result.result.lineItems.filter(item => item.skipped);

      // Format cost of goods for display (convert cents to dollars)
      const displayResult = {
        ...result,
        result: {
          ...result.result,
          totals: {
            ...result.result.totals,
            costOfGoods: `$${(result.result.totals.costOfGoods / 100).toFixed(2)}`
          }
        }
      };

      console.log(`[${req.requestId}] Successfully processed order ${payload.id}:`, displayResult);

      if (skippedItems.length > 0) {
        console.log(`[${req.requestId}] Skipped ${skippedItems.length} items:`,
          skippedItems.map(item => `${item.sku} (${item.reason})`));
      }

      console.log(`[${req.requestId}] Processed ${processedItems.length} items, skipped ${skippedItems.length} items`);
    } else {
      console.error(`[${req.requestId}] Failed to process order ${payload.id}:`, result);
    }

  } catch (error) {
    console.error(`[${req.requestId}] Error processing order ${payload.id}:`, error);

    // For validation errors, log the detailed information more clearly
    if (error.name === 'ValidationError' || error.name === 'BusinessLogicError') {
      console.log(`[${req.requestId}] Order ${payload.id} validation details:`);
      console.log(`[${req.requestId}] - Shop: ${store}`);
      console.log(`[${req.requestId}] - Order ID: ${payload.id}`);
      console.log(`[${req.requestId}] - Error Type: ${error.name}`);
      console.log(`[${req.requestId}] - Error Message: ${error.message}`);

      // If the error message contains line item details, format them nicely
      if (error.message.includes('Line Item')) {
        console.log(`[${req.requestId}] - Detailed Line Item Analysis:`);
        const lines = error.message.split('\n');
        lines.forEach(line => {
          if (line.trim()) {
            console.log(`[${req.requestId}]   ${line.trim()}`);
          }
        });
      }

      // Log order line items for context
      if (payload.line_items && payload.line_items.length > 0) {
        console.log(`[${req.requestId}] - Order contains ${payload.line_items.length} line items:`);
        payload.line_items.forEach((item, index) => {
          console.log(`[${req.requestId}]   ${index + 1}. "${item.title}" (SKU: ${item.sku || 'No SKU'}, Fulfillment: ${item.fulfillment_service || 'manual'}, Qty: ${item.quantity})`);
        });
      }
    }

    // Don't throw to avoid webhook failures
  }
}

/**
 * Handle order paid webhooks
 */
async function handleOrderPaidWebhook(store, payload, req) {
  console.log(`[${req.requestId}] Processing order paid for order ${payload.id} in store ${store}`);

  try {
    // Import the OrderProcessingService
    const { OrderProcessingService } = await import('../../app/services/order/OrderProcessingService.js');

    const orderProcessingService = new OrderProcessingService();

    // Process the order since it's now paid
    const result = await orderProcessingService.processOrder(
      payload.id,
      store,
      {
        skipValidation: false,
        dryRun: false,
        forceReprocess: true // Reprocess since payment status changed
      }
    );

    if (result.success) {
      // Format cost of goods for display (convert cents to dollars)
      const costOfGoodsDisplay = result.result?.totals?.costOfGoods
        ? `$${(result.result.totals.costOfGoods / 100).toFixed(2)}`
        : '$0.00';
      console.log(`[${req.requestId}] Successfully processed paid order ${payload.id} - Cost of Goods: ${costOfGoodsDisplay}`);
    } else {
      console.error(`[${req.requestId}] Failed to process paid order ${payload.id}:`, result);
    }

  } catch (error) {
    console.error(`[${req.requestId}] Error processing paid order ${payload.id}:`, error);
  }
}

/**
 * Handle order cancelled webhooks
 */
async function handleOrderCancelledWebhook(store, payload, req) {
  console.log(`[${req.requestId}] Processing order cancelled for order ${payload.id} in store ${store}`);

  try {
    // Import database client
    const db = await import('../../app/db.server.js');

    // Remove or mark cancelled order data
    await db.default.invoiceBalance.updateMany({
      where: {
        shop: store,
        orderId: String(payload.id)
      },
      data: {
        cancelled: true,
        cancelledAt: new Date()
      }
    });

    console.log(`[${req.requestId}] Marked order ${payload.id} as cancelled`);

  } catch (error) {
    console.error(`[${req.requestId}] Error handling cancelled order ${payload.id}:`, error);
  }
}

/**
 * Handle order fulfilled webhooks
 */
async function handleOrderFulfilledWebhook(store, payload, req) {
  console.log(`[${req.requestId}] Processing order fulfilled for order ${payload.id} in store ${store}`);

  try {
    // Import shipping cost service
    const { ShippingCostService } = await import('../../app/services/shipping/ShippingCostService.js');

    const shippingCostService = new ShippingCostService();

    // Update shipping costs for fulfilled order
    await shippingCostService.processFulfilledOrder(payload.id, store);

    console.log(`[${req.requestId}] Processed fulfillment for order ${payload.id}`);

  } catch (error) {
    console.error(`[${req.requestId}] Error processing fulfilled order ${payload.id}:`, error);
  }
}

/**
 * Handle app uninstalled webhooks
 */
async function handleAppUninstalledWebhook(store, payload, req) {
  console.log(`[${req.requestId}] Processing app uninstalled for store ${store}`);

  // TODO: Implement cleanup logic for uninstalled app
  // This might involve:
  // 1. Cleaning up sessions
  // 2. Removing store-specific data
  // 3. Notifying administrators
}

/**
 * Handle GDPR webhooks
 */
async function handleGDPRWebhook(store, topic, payload, req) {
  console.log(`[${req.requestId}] Processing GDPR webhook ${topic} for store ${store}`);

  // TODO: Implement GDPR compliance logic
}

/**
 * Process ShipStation webhook
 * @param {object} payload - Webhook payload
 * @param {object} req - Express request object
 */
async function processShipStationWebhook(payload, req) {
  console.log(`[${req.requestId}] Processing ShipStation webhook:`, payload);

  // TODO: Implement ShipStation webhook processing
  // This would typically involve:
  // 1. Parsing the webhook payload
  // 2. Updating shipping cost records
  // 3. Triggering reconciliation if needed
}

/**
 * Verify ShipStation webhook signature
 * @param {object} payload - Webhook payload
 * @param {string} signature - Webhook signature
 * @returns {boolean} True if signature is valid
 */
function verifyShipStationWebhook(payload, signature) {
  // TODO: Implement ShipStation webhook verification
  // This would involve verifying the signature using the webhook secret
  return true; // Placeholder
}

export default router;
