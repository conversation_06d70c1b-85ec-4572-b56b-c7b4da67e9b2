/**
 * Express Multistore Server
 * Handles multistore authentication, webhooks, and API endpoints
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';

// Import middleware
import {
  multistoreMiddleware,
  requestLoggingMiddleware,
  multistoreErrorHandler
} from './middleware/multistore.js';

// Import routes
import authRoutes from './routes/auth.js';
import webhookRoutes from './routes/webhooks.js';
import apiRoutes from './routes/api.js';

// Import configuration
import { config, expressConfig, environmentConfig } from '../shared/config/index.js';
import { formatSuccessResponse, getMemoryUsage } from '../shared/utils/index.js';
import { HTTP_STATUS } from '../shared/constants/index.js';

const app = express();

// Trust proxy for accurate IP addresses behind load balancers
app.set('trust proxy', 1);

// Security middleware
app.use(helmet(expressConfig.security.helmet));

// CORS configuration
app.use(cors(expressConfig.cors));

// Compression middleware
app.use(compression());

// Rate limiting
const limiter = rateLimit(expressConfig.rateLimit);
app.use(limiter);

// Request timeout middleware
app.use((req, res, next) => {
  req.setTimeout(expressConfig.timeout.request, () => {
    console.error(`Request timeout for ${req.method} ${req.path}`);
    if (!res.headersSent) {
      res.status(HTTP_STATUS.GATEWAY_TIMEOUT).json({
        success: false,
        error: {
          message: 'Request timeout',
          code: 'TIMEOUT_ERROR',
          timestamp: new Date().toISOString(),
        },
      });
    }
  });
  next();
});

// Body parsing middleware (before multistore middleware for webhooks)
app.use('/express/webhooks', express.raw({ type: 'application/json' }));
app.use(express.json({ limit: expressConfig.bodyParser.limit }));
app.use(express.urlencoded({
  extended: expressConfig.bodyParser.extended,
  limit: expressConfig.bodyParser.limit
}));

// Request logging middleware
app.use(requestLoggingMiddleware);

// Health check endpoints (no multistore middleware needed)
app.get('/health', (req, res) => {
  const memoryUsage = getMemoryUsage();
  const uptime = process.uptime();

  const response = formatSuccessResponse({
    service: 'express-multistore-server',
    status: 'healthy',
    version: process.env.npm_package_version || '1.0.0',
    environment: config.app.environment,
    uptime: `${Math.floor(uptime / 60)}m ${Math.floor(uptime % 60)}s`,
    memory: memoryUsage,
    timestamp: new Date().toISOString(),
  }, 'Express multistore server is healthy');

  res.json(response);
});

// Express health check endpoint for deployment validation
app.get('/express/health', (req, res) => {
  const memoryUsage = getMemoryUsage();
  const uptime = process.uptime();

  const response = formatSuccessResponse({
    service: 'express-multistore-server',
    status: 'healthy',
    version: process.env.npm_package_version || '1.0.0',
    environment: config.app.environment,
    uptime: `${Math.floor(uptime / 60)}m ${Math.floor(uptime % 60)}s`,
    memory: memoryUsage,
    timestamp: new Date().toISOString(),
  }, 'Express multistore server is healthy');

  res.json(response);
});

// Apply multistore middleware to routes that need shop context
app.use('/express/auth', multistoreMiddleware, authRoutes);
app.use('/express/webhooks', multistoreMiddleware, webhookRoutes);
app.use('/express/api', multistoreMiddleware, apiRoutes);

// Server information endpoint
app.get('/info', (req, res) => {
  const response = formatSuccessResponse({
    name: 'Express Multistore Server',
    version: process.env.npm_package_version || '1.0.0',
    environment: config.app.environment,
    supportedStores: config.multistore.supportedStores,
    features: [
      'multistore-authentication',
      'webhook-processing',
      'api-endpoints',
      'rate-limiting',
      'memory-management',
    ],
    endpoints: {
      auth: '/express/auth',
      webhooks: '/express/webhooks',
      api: '/express/api',
      health: '/health',
    },
  }, 'Server information');

  res.json(response);
});

// Catch-all route for undefined endpoints
app.use('*', (req, res) => {
  res.status(HTTP_STATUS.NOT_FOUND).json({
    success: false,
    error: {
      message: `Route not found: ${req.method} ${req.originalUrl}`,
      code: 'ROUTE_NOT_FOUND',
      timestamp: new Date().toISOString(),
    },
  });
});

// Error handling middleware (must be last)
app.use(multistoreErrorHandler);

// Global error handler for uncaught errors
app.use((error, req, res, next) => {
  console.error('Uncaught error:', error);

  if (res.headersSent) {
    return next(error);
  }

  res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
    success: false,
    error: {
      message: 'Internal server error',
      code: 'INTERNAL_SERVER_ERROR',
      timestamp: new Date().toISOString(),
    },
  });
});

// Server startup
const port = expressConfig.port;
const server = app.listen(port, () => {
  console.log(`🚀 Express multistore server running on port ${port}`);
  console.log(`📊 Environment: ${config.app.environment}`);
  console.log(`🏪 Supported stores: ${config.multistore.supportedStores.join(', ')}`);
  console.log(`🔗 Health check: http://localhost:${port}/health`);
  console.log(`ℹ️  Server info: http://localhost:${port}/info`);
});

// Set server timeout
server.timeout = expressConfig.timeout.server;

// Graceful shutdown handling
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

function gracefulShutdown(signal) {
  console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);

  server.close((err) => {
    if (err) {
      console.error('❌ Error during server shutdown:', err);
      process.exit(1);
    }

    console.log('✅ Express multistore server shut down gracefully');
    process.exit(0);
  });

  // Force shutdown after 30 seconds
  setTimeout(() => {
    console.error('⚠️  Forced shutdown after timeout');
    process.exit(1);
  }, 30000);
}

// Memory monitoring
if (environmentConfig.monitoring.enabled) {
  setInterval(() => {
    const memoryUsage = getMemoryUsage();
    const uptime = process.uptime();

    console.log(`📊 Memory usage - RSS: ${memoryUsage.rss}, Heap: ${memoryUsage.heapUsed}/${memoryUsage.heapTotal}, Uptime: ${Math.floor(uptime / 60)}m`);

    // Force garbage collection if available and memory usage is high
    if (global.gc && process.memoryUsage().heapUsed / process.memoryUsage().heapTotal > 0.8) {
      console.log('🧹 Running garbage collection due to high memory usage');
      global.gc();
    }
  }, environmentConfig.monitoring.healthCheck.interval);
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('💥 Uncaught Exception:', error);

  // Attempt graceful shutdown
  gracefulShutdown('UNCAUGHT_EXCEPTION');
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);

  // Attempt graceful shutdown
  gracefulShutdown('UNHANDLED_REJECTION');
});

export default app;
