/**
 * Multistore middleware for Express server
 * Handles dynamic Shopify app instance creation and store-specific authentication
 */

import { shopifyApp } from '@shopify/shopify-app-express';
import { PrismaSessionStorage } from '@shopify/shopify-app-session-storage-prisma';
import { ApiVersion, LogSeverity } from '@shopify/shopify-api';
import { restResources } from '@shopify/shopify-api/rest/admin/2025-01';

import { config, multistoreHelpers } from '../../shared/config/index.js';
import { formatErrorResponse, generateRequestId } from '../../shared/utils/index.js';
import { HTTP_STATUS, ERROR_CODES } from '../../shared/constants/index.js';
import prisma from '../../app/db.server.js';

// Cache for Shopify app instances to avoid recreating them
const shopifyApps = new Map();

/**
 * Create a Shopify app instance for a specific store
 * @param {object} storeConfig - Store-specific configuration
 * @param {string} storeDomain - Store domain
 * @returns {object} Shopify app instance
 */
function createShopifyApp(storeConfig, storeDomain) {
  // Temporarily disabled to avoid path-to-regexp issues
  // TODO: Re-enable once the basic server is working
  console.log(`Shopify app creation temporarily disabled for ${storeDomain}`);
  return null;

  /*
  try {
    return shopifyApp({
      apiKey: storeConfig.apiKey,
      apiSecretKey: storeConfig.apiSecret,
      apiVersion: ApiVersion.January25,
      scopes: config.shopify.scopes || ['read_orders', 'write_orders'],
      appUrl: config.app.appUrl,
      authPathPrefix: '/auth',
      sessionStorage: new PrismaSessionStorage(prisma),
      restResources,
      future: {
        unstable_newEmbeddedAuthStrategy: true,
        removeRest: false, // Keep REST API enabled
      },
      logger: {
        level: LogSeverity.Error, // Only log errors to reduce noise
      },
      ...(process.env.SHOP_CUSTOM_DOMAIN
        ? { customShopDomains: [process.env.SHOP_CUSTOM_DOMAIN] }
        : {}),
    });
  } catch (error) {
    console.error(`Failed to create Shopify app for ${storeDomain}:`, error);
    return null;
  }
  */
}

/**
 * Get or create Shopify app instance for a store
 * @param {string} storeDomain - Store domain
 * @returns {object|null} Shopify app instance or null if store not supported
 */
function getShopifyAppInstance(storeDomain) {
  // Check if store is supported
  if (!multistoreHelpers.isStoreSupported(storeDomain)) {
    return null;
  }

  // Return cached instance if available
  if (shopifyApps.has(storeDomain)) {
    return shopifyApps.get(storeDomain);
  }

  // Get store configuration
  const storeConfig = multistoreHelpers.getStoreConfig(storeDomain);
  if (!storeConfig) {
    return null;
  }

  // Create and cache new instance
  const shopifyApp = createShopifyApp(storeConfig, storeDomain);
  if (shopifyApp) {
    shopifyApps.set(storeDomain, shopifyApp);
    console.log(`Created Shopify app instance for store: ${storeDomain}`);
  }

  return shopifyApp;
}

/**
 * Multistore middleware function
 * Identifies the store from request and attaches appropriate Shopify app instance
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {Function} next - Next middleware function
 */
export function multistoreMiddleware(req, res, next) {
  // Generate unique request ID for tracking
  req.requestId = generateRequestId();

  // Extract shop domain from request
  const shop = multistoreHelpers.extractShopFromRequest(req);

  if (!shop) {
    const error = formatErrorResponse(
      'Shop parameter required',
      ERROR_CODES.VALIDATION_REQUIRED_FIELD,
      HTTP_STATUS.BAD_REQUEST
    );
    return res.status(error.statusCode).json(error);
  }

  // Normalize shop domain
  const normalizedShop = shop.includes('.myshopify.com')
    ? shop
    : `${shop}.myshopify.com`;

  // Check if store is supported
  if (!multistoreHelpers.isStoreSupported(normalizedShop)) {
    const error = formatErrorResponse(
      `Store not supported: ${normalizedShop}`,
      ERROR_CODES.STORE_NOT_SUPPORTED,
      HTTP_STATUS.FORBIDDEN
    );
    return res.status(error.statusCode).json(error);
  }

  // Get Shopify app instance for this store (optional for now)
  const shopifyAppInstance = getShopifyAppInstance(normalizedShop);

  // Attach shop and Shopify app instance to request
  req.shop = normalizedShop;
  req.shopifyApp = shopifyAppInstance; // May be null if creation failed

  // Log request for debugging
  console.log(`[${req.requestId}] Multistore request for shop: ${normalizedShop}`);

  next();
}

/**
 * Authentication middleware for Shopify OAuth
 * Uses the store-specific Shopify app instance for authentication
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {Function} next - Next middleware function
 */
export function shopifyAuthMiddleware(req, res, next) {
  if (!req.shopifyApp) {
    const error = formatErrorResponse(
      'Shopify app instance not found',
      ERROR_CODES.STORE_CONFIG_NOT_FOUND,
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );
    return res.status(error.statusCode).json(error);
  }

  // Use the store-specific Shopify app instance for authentication
  return req.shopifyApp.authenticate.admin(req, res, next);
}

/**
 * Webhook verification middleware
 * Verifies webhooks using the store-specific configuration
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {Function} next - Next middleware function
 */
export function webhookVerificationMiddleware(req, res, next) {
  if (!req.shopifyApp) {
    const error = formatErrorResponse(
      'Shopify app instance not found',
      ERROR_CODES.STORE_CONFIG_NOT_FOUND,
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );
    return res.status(error.statusCode).json(error);
  }

  // Use the store-specific Shopify app instance for webhook verification
  return req.shopifyApp.processWebhooks(req, res, next);
}

/**
 * Request logging middleware
 * Logs requests with store context
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {Function} next - Next middleware function
 */
export function requestLoggingMiddleware(req, res, next) {
  const startTime = Date.now();

  // Log request start
  console.log(`[${req.requestId}] ${req.method} ${req.path} - Shop: ${req.shop || 'unknown'}`);

  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(...args) {
    const duration = Date.now() - startTime;
    console.log(`[${req.requestId}] ${res.statusCode} - ${duration}ms`);
    originalEnd.apply(this, args);
  };

  next();
}

/**
 * Error handling middleware for multistore operations
 * @param {Error} error - Error object
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {Function} next - Next middleware function
 */
export function multistoreErrorHandler(error, req, res, next) {
  console.error(`[${req.requestId}] Multistore error:`, error);

  // Handle specific Shopify errors
  if (error.message?.includes('Invalid shop domain')) {
    const errorResponse = formatErrorResponse(
      'Invalid shop domain',
      ERROR_CODES.VALIDATION_INVALID_FORMAT,
      HTTP_STATUS.BAD_REQUEST
    );
    return res.status(errorResponse.statusCode).json(errorResponse);
  }

  if (error.message?.includes('Unauthorized')) {
    const errorResponse = formatErrorResponse(
      'Authentication required',
      ERROR_CODES.AUTH_MISSING_TOKEN,
      HTTP_STATUS.UNAUTHORIZED
    );
    return res.status(errorResponse.statusCode).json(errorResponse);
  }

  // Default error response
  const errorResponse = formatErrorResponse(
    error.message || 'Internal server error',
    ERROR_CODES.INTERNAL_SERVER_ERROR,
    HTTP_STATUS.INTERNAL_SERVER_ERROR
  );

  res.status(errorResponse.statusCode).json(errorResponse);
}

/**
 * Clear cached Shopify app instances (for testing or configuration updates)
 * @param {string} [storeDomain] - Specific store to clear, or all if not provided
 */
export function clearShopifyAppCache(storeDomain = null) {
  if (storeDomain) {
    shopifyApps.delete(storeDomain);
    console.log(`Cleared Shopify app cache for store: ${storeDomain}`);
  } else {
    shopifyApps.clear();
    console.log('Cleared all Shopify app cache');
  }
}

/**
 * Get cache statistics
 * @returns {object} Cache statistics
 */
export function getCacheStats() {
  return {
    cachedStores: Array.from(shopifyApps.keys()),
    cacheSize: shopifyApps.size,
    supportedStores: multistoreHelpers.getSupportedStores(),
  };
}

export default {
  multistoreMiddleware,
  shopifyAuthMiddleware,
  webhookVerificationMiddleware,
  requestLoggingMiddleware,
  multistoreErrorHandler,
  clearShopifyAppCache,
  getCacheStats,
};
