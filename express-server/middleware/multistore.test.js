/**
 * Tests for multistore middleware
 */

import { jest } from '@jest/globals';
import {
  multistoreMiddleware,
  shopifyAuthMiddleware,
  webhookVerificationMiddleware,
  requestLoggingMiddleware,
  multistoreErrorHandler,
  clearShopifyAppCache,
  getCacheStats
} from './multistore.js';

// Mock dependencies
jest.mock('../../shared/config/index.js', () => ({
  config: {
    multistore: {
      supportedStores: ['test-store.myshopify.com', 'another-store.myshopify.com'],
      storeConfigs: {
        'test-store.myshopify.com': {
          apiKey: 'test-api-key',
          apiSecret: 'test-api-secret',
          scopes: ['read_orders', 'write_orders'],
        },
        'another-store.myshopify.com': {
          apiKey: 'another-api-key',
          apiSecret: 'another-api-secret',
          scopes: ['read_orders'],
        },
      },
    },
  },
  multistoreHelpers: {
    isStoreSupported: jest.fn(),
    getStoreConfig: jest.fn(),
    extractShopFromRequest: jest.fn(),
  },
}));

jest.mock('../../shared/utils/index.js', () => ({
  formatErrorResponse: jest.fn(),
  generateRequestId: jest.fn(() => 'test-request-id'),
}));

jest.mock('../../shared/constants/index.js', () => ({
  HTTP_STATUS: {
    BAD_REQUEST: 400,
    FORBIDDEN: 403,
    INTERNAL_SERVER_ERROR: 500,
  },
  ERROR_CODES: {
    VALIDATION_REQUIRED_FIELD: 'VALIDATION_REQUIRED_FIELD',
    VALIDATION_INVALID_FORMAT: 'VALIDATION_INVALID_FORMAT',
    STORE_NOT_SUPPORTED: 'STORE_NOT_SUPPORTED',
    STORE_CONFIG_NOT_FOUND: 'STORE_CONFIG_NOT_FOUND',
    INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
    AUTH_MISSING_TOKEN: 'AUTH_MISSING_TOKEN',
  },
}));

jest.mock('@shopify/shopify-app-express', () => ({
  shopifyApp: jest.fn(() => ({
    authenticate: {
      admin: jest.fn(),
    },
    processWebhooks: jest.fn(),
  })),
}));

jest.mock('../../app/db.server.js', () => ({}));

describe('Multistore Middleware', () => {
  let req, res, next;

  beforeEach(() => {
    req = {
      query: {},
      body: {},
      headers: {},
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    next = jest.fn();

    // Clear all mocks
    jest.clearAllMocks();
    clearShopifyAppCache();
  });

  describe('multistoreMiddleware', () => {
    it('should generate request ID and extract shop from request', () => {
      const { multistoreHelpers } = require('../../shared/config/index.js');
      const { generateRequestId } = require('../../shared/utils/index.js');

      multistoreHelpers.extractShopFromRequest.mockReturnValue('test-store.myshopify.com');
      multistoreHelpers.isStoreSupported.mockReturnValue(true);
      multistoreHelpers.getStoreConfig.mockReturnValue({
        apiKey: 'test-api-key',
        apiSecret: 'test-api-secret',
      });

      multistoreMiddleware(req, res, next);

      expect(generateRequestId).toHaveBeenCalled();
      expect(req.requestId).toBe('test-request-id');
      expect(multistoreHelpers.extractShopFromRequest).toHaveBeenCalledWith(req);
      expect(next).toHaveBeenCalled();
    });

    it('should return error if no shop parameter is provided', () => {
      const { multistoreHelpers } = require('../../shared/config/index.js');
      const { formatErrorResponse } = require('../../shared/utils/index.js');

      multistoreHelpers.extractShopFromRequest.mockReturnValue(null);
      formatErrorResponse.mockReturnValue({
        statusCode: 400,
        error: 'Shop parameter required',
      });

      multistoreMiddleware(req, res, next);

      expect(formatErrorResponse).toHaveBeenCalledWith(
        'Shop parameter required',
        'VALIDATION_REQUIRED_FIELD',
        400
      );
      expect(res.status).toHaveBeenCalledWith(400);
      expect(next).not.toHaveBeenCalled();
    });

    it('should return error if store is not supported', () => {
      const { multistoreHelpers } = require('../../shared/config/index.js');
      const { formatErrorResponse } = require('../../shared/utils/index.js');

      multistoreHelpers.extractShopFromRequest.mockReturnValue('unsupported-store.myshopify.com');
      multistoreHelpers.isStoreSupported.mockReturnValue(false);
      formatErrorResponse.mockReturnValue({
        statusCode: 403,
        error: 'Store not supported',
      });

      multistoreMiddleware(req, res, next);

      expect(formatErrorResponse).toHaveBeenCalledWith(
        'Store not supported: unsupported-store.myshopify.com',
        'STORE_NOT_SUPPORTED',
        403
      );
      expect(res.status).toHaveBeenCalledWith(403);
      expect(next).not.toHaveBeenCalled();
    });

    it('should normalize shop domain and attach to request', () => {
      const { multistoreHelpers } = require('../../shared/config/index.js');

      multistoreHelpers.extractShopFromRequest.mockReturnValue('test-store');
      multistoreHelpers.isStoreSupported.mockReturnValue(true);
      multistoreHelpers.getStoreConfig.mockReturnValue({
        apiKey: 'test-api-key',
        apiSecret: 'test-api-secret',
      });

      multistoreMiddleware(req, res, next);

      expect(req.shop).toBe('test-store.myshopify.com');
      expect(req.shopifyApp).toBeDefined();
      expect(next).toHaveBeenCalled();
    });
  });

  describe('shopifyAuthMiddleware', () => {
    it('should use store-specific Shopify app instance for authentication', () => {
      const mockShopifyApp = {
        authenticate: {
          admin: jest.fn(),
        },
      };
      req.shopifyApp = mockShopifyApp;

      shopifyAuthMiddleware(req, res, next);

      expect(mockShopifyApp.authenticate.admin).toHaveBeenCalledWith(req, res, next);
    });

    it('should return error if no Shopify app instance is found', () => {
      const { formatErrorResponse } = require('../../shared/utils/index.js');

      formatErrorResponse.mockReturnValue({
        statusCode: 500,
        error: 'Shopify app instance not found',
      });

      shopifyAuthMiddleware(req, res, next);

      expect(formatErrorResponse).toHaveBeenCalledWith(
        'Shopify app instance not found',
        'STORE_CONFIG_NOT_FOUND',
        500
      );
      expect(res.status).toHaveBeenCalledWith(500);
      expect(next).not.toHaveBeenCalled();
    });
  });

  describe('webhookVerificationMiddleware', () => {
    it('should use store-specific Shopify app instance for webhook verification', () => {
      const mockShopifyApp = {
        processWebhooks: jest.fn(),
      };
      req.shopifyApp = mockShopifyApp;

      webhookVerificationMiddleware(req, res, next);

      expect(mockShopifyApp.processWebhooks).toHaveBeenCalledWith(req, res, next);
    });

    it('should return error if no Shopify app instance is found', () => {
      const { formatErrorResponse } = require('../../shared/utils/index.js');

      formatErrorResponse.mockReturnValue({
        statusCode: 500,
        error: 'Shopify app instance not found',
      });

      webhookVerificationMiddleware(req, res, next);

      expect(formatErrorResponse).toHaveBeenCalledWith(
        'Shopify app instance not found',
        'STORE_CONFIG_NOT_FOUND',
        500
      );
      expect(res.status).toHaveBeenCalledWith(500);
      expect(next).not.toHaveBeenCalled();
    });
  });

  describe('requestLoggingMiddleware', () => {
    it('should log request and response', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      req.requestId = 'test-request-id';
      req.method = 'GET';
      req.path = '/test';
      req.shop = 'test-store.myshopify.com';

      // Mock res.end and set statusCode
      res.statusCode = 200;
      const originalEnd = jest.fn();
      res.end = jest.fn((...args) => {
        originalEnd.apply(res, args);
      });

      requestLoggingMiddleware(req, res, next);

      expect(consoleSpy).toHaveBeenCalledWith(
        '[test-request-id] GET /test - Shop: test-store.myshopify.com'
      );
      expect(next).toHaveBeenCalled();

      // Simulate response end
      res.end();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringMatching(/\[test-request-id\] 200 - \d+ms/)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('multistoreErrorHandler', () => {
    it('should handle Shopify-specific errors', () => {
      const error = new Error('Invalid shop domain');
      const { formatErrorResponse } = require('../../shared/utils/index.js');

      req.requestId = 'test-request-id';
      formatErrorResponse.mockReturnValue({
        statusCode: 400,
        error: 'Invalid shop domain',
      });

      multistoreErrorHandler(error, req, res, next);

      expect(formatErrorResponse).toHaveBeenCalledWith(
        'Invalid shop domain',
        'VALIDATION_INVALID_FORMAT',
        400
      );
      expect(res.status).toHaveBeenCalledWith(400);
    });

    it('should handle generic errors', () => {
      const error = new Error('Generic error');
      const { formatErrorResponse } = require('../../shared/utils/index.js');

      req.requestId = 'test-request-id';
      formatErrorResponse.mockReturnValue({
        statusCode: 500,
        error: 'Generic error',
      });

      multistoreErrorHandler(error, req, res, next);

      expect(formatErrorResponse).toHaveBeenCalledWith(
        'Generic error',
        'INTERNAL_SERVER_ERROR',
        500
      );
      expect(res.status).toHaveBeenCalledWith(500);
    });
  });

  describe('Cache Management', () => {
    it('should clear cache for specific store', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      clearShopifyAppCache('test-store.myshopify.com');

      expect(consoleSpy).toHaveBeenCalledWith(
        'Cleared Shopify app cache for store: test-store.myshopify.com'
      );

      consoleSpy.mockRestore();
    });

    it('should clear all cache', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      clearShopifyAppCache();

      expect(consoleSpy).toHaveBeenCalledWith('Cleared all Shopify app cache');

      consoleSpy.mockRestore();
    });

    it('should return cache statistics', () => {
      const { multistoreHelpers } = require('../../shared/config/index.js');

      multistoreHelpers.getSupportedStores = jest.fn().mockReturnValue([
        'test-store.myshopify.com',
        'another-store.myshopify.com',
      ]);

      const stats = getCacheStats();

      expect(stats).toEqual({
        cachedStores: [],
        cacheSize: 0,
        supportedStores: ['test-store.myshopify.com', 'another-store.myshopify.com'],
      });
    });
  });
});
