#!/bin/bash

# Script to fetch a specific shipment from ShipStation API using the direct endpoint
# Usage: ./fetch-shipment-direct.sh <shipment_id>

# Check if shipment ID is provided
if [ -z "$1" ]; then
  echo "Error: Shipment ID is required"
  echo "Usage: ./fetch-shipment-direct.sh <shipment_id>"
  exit 1
fi

SHIPMENT_ID=$1

# Check if API key is available
if [ -z "$SHIPSTATION_API_KEY" ]; then
  echo "Error: SHIPSTATION_API_KEY environment variable is not set"
  echo "Please set it with: export SHIPSTATION_API_KEY=your_api_key"
  exit 1
fi

echo "Fetching shipment with ID: $SHIPMENT_ID"

# Use the direct shipment endpoint
echo "Using direct shipment endpoint..."
curl -s -X GET \
  "https://api.shipstation.com/v2/shipments/$SHIPMENT_ID" \
  -H "API-Key: $SHIPSTATION_API_KEY" \
  -H "Content-Type: application/json" > shipment-direct-result.json

# Display raw response for debugging
echo "Raw response:"
cat shipment-direct-result.json
echo ""

# Try to parse with jq
cat shipment-direct-result.json | jq . > shipment-direct-pretty.json

# Check if we got an error
ERROR_MSG=$(jq -r '.message // empty' shipment-direct-pretty.json)
if [ -z "$ERROR_MSG" ]; then
  echo "Successfully fetched shipment. Results saved to shipment-direct-pretty.json"
else
  echo "Error fetching shipment: $ERROR_MSG"
  
  # Try the alternative approach with shipmentId parameter
  echo "Trying with shipmentId parameter..."
  curl -s -X GET \
    "https://api.shipstation.com/v2/shipments?shipmentId=$SHIPMENT_ID" \
    -H "API-Key: $SHIPSTATION_API_KEY" \
    -H "Content-Type: application/json" > shipment-param-result.json
  
  echo "Raw response from parameter approach:"
  cat shipment-param-result.json
  echo ""
  
  # Parse with jq
  cat shipment-param-result.json | jq . > shipment-param-pretty.json
  
  # Check if we got any results
  SHIPMENT_COUNT=$(jq '.shipments | length // 0' shipment-param-pretty.json)
  if [ "$SHIPMENT_COUNT" -gt 0 ]; then
    echo "Found shipment using parameter approach. Results saved to shipment-param-pretty.json"
  else
    echo "No results from parameter approach either."
    echo "The shipment with ID $SHIPMENT_ID could not be found in the ShipStation API."
  fi
fi

echo "Done."
