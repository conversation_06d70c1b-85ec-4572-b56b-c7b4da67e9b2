# Deployment Guide - Americans United Inc Shopify App

This guide covers deploying the Americans United Inc Shopify App to Fly.io using the multistore architecture with separate web and multistore processes.

## Overview

The application uses a **multistore deployment architecture** with two separate processes:
- **Web Process (Remix)**: Handles the main Shopify app interface and individual store operations (port 3000)
- **Multistore Process (Express)**: Handles multistore authentication, webhooks, and cross-store operations (port 4000)

This architecture allows the app to:
- Authenticate with multiple Shopify stores simultaneously
- Handle webhooks from multiple stores and external services
- Provide a unified interface for managing multiple store operations
- Scale each service independently based on load

### Multistore Process Responsibilities

**Web Process (port 3000):**
- Shopify app interface for individual stores
- Store-specific authentication and sessions
- Shopify webhooks from all stores (`/webhooks/app`)
- ShipStation webhooks (`/api/shipstation-*`)
- Health checks (`/healthz`)

**Multistore Process (port 4000):**
- Cross-store authentication (`/express/auth/*`)
- Alternative Shopify webhook handlers (`/express/webhooks/shopify/:store/:topic`)
- Cross-store API endpoints (`/express/api/*`)
- Multistore health checks (`/express/health`)

## Prerequisites

### 1. Install Fly.io CLI

```bash
# Install flyctl
curl -L https://fly.io/install.sh | sh

# Login to Fly.io
flyctl auth login
```

### 2. Verify Project Structure

Ensure these files exist in your project root:
- `fly.toml` - Fly.io configuration
- `Dockerfile.multiprocess` - Multi-process container definition
- `start.sh` - Process startup script
- `infrastructure/scripts/deploy.sh` - Deployment script
- `infrastructure/scripts/health-check.sh` - Health check script

## Environment Setup

### 1. Create Fly.io App

```bash
# Create a new Fly.io app (if not already created)
flyctl apps create americans-united-inc

# Or use existing app
flyctl apps list
```

### 2. Set Required Secrets

```bash
# Database URL (Fly.io PostgreSQL)
flyctl secrets set DATABASE_URL="********************************************/database"

# Session secret for authentication
flyctl secrets set SESSION_SECRET="your-secure-session-secret"

# Shopify API credentials for each store
flyctl secrets set SHOPIFY_API_KEY_STYLISH_STITCHES_JORDIN_KOLMAN="your-api-key"
flyctl secrets set SHOPIFY_API_SECRET_STYLISH_STITCHES_JORDIN_KOLMAN="your-api-secret"

flyctl secrets set SHOPIFY_API_KEY_PLD_APP_DEVELOPMENT_STORE="your-api-key"
flyctl secrets set SHOPIFY_API_SECRET_PLD_APP_DEVELOPMENT_STORE="your-api-secret"

flyctl secrets set SHOPIFY_API_KEY_75TH_RRA_APP_DEVELOPMENT_STORE="your-api-key"
flyctl secrets set SHOPIFY_API_SECRET_75TH_RRA_APP_DEVELOPMENT_STORE="your-api-secret"

# ShipStation API credentials
flyctl secrets set SHIPSTATION_API_KEY="your-shipstation-api-key"
flyctl secrets set SHIPSTATION_API_SECRET="your-shipstation-api-secret"

# Admin shop configuration
flyctl secrets set ADMIN_SHOP="your-admin-shop.myshopify.com"
```

### 3. Set Up Database

```bash
# Create PostgreSQL database (if not already created)
flyctl postgres create --name americans-united-db --region ord

# Attach database to app
flyctl postgres attach americans-united-db

# Get database URL
flyctl postgres connect -a americans-united-db
```

## Deployment Methods

### Method 1: Automated Deployment (Recommended)

Use the provided deployment script:

```bash
# Deploy with all checks
npm run deploy:fly

# Deploy skipping tests (faster)
npm run deploy:fly:skip-tests

# Or run directly
bash infrastructure/scripts/deploy.sh
```

### Method 2: Manual Deployment

```bash
# Build and deploy manually
flyctl deploy --dockerfile Dockerfile.multiprocess

# Deploy with no cache (fresh build)
flyctl deploy --dockerfile Dockerfile.multiprocess --no-cache
```

## Post-Deployment Verification

### 1. Check Application Status

```bash
# Check app status
flyctl status

# View logs
flyctl logs

# Check health endpoints
curl https://your-app.fly.dev/healthz
curl https://your-app.fly.dev:4000/express/health
```

### 2. Configure Webhook URLs

After deployment, configure these webhook URLs in your external services:

#### Shopify Webhooks
Configure in each Shopify app configuration:
- **URL**: `https://your-app.fly.dev/webhooks/app`
- **Topics**: `app/uninstalled`, `orders/create`, `products/update`
- **API Version**: `2025-04`

#### ShipStation Webhooks
Configure in ShipStation account settings:
- **Order Webhook**: `https://your-app.fly.dev/api/shipstation-order-webhook`
- **Shipment Webhook**: `https://your-app.fly.dev/api/shipstation-webhook`
- **Authentication**: Use `X-API-Key` header with `SHIPSTATION_WEBHOOK_KEY` value

#### Alternative Shopify Webhook Handlers (Express)
For advanced multistore operations (accessible on port 4000):
- **Store-specific Shopify webhooks**: `https://your-app.fly.dev:4000/express/webhooks/shopify/:store/:topic`
  - Replace `:store` with store identifier (e.g., `stylish-stitches`)
  - Replace `:topic` with webhook topic (e.g., `orders/create`)
- **ShipStation webhooks (alternative)**: `https://your-app.fly.dev:4000/express/webhooks/shipstation`

#### Multistore Management Endpoints
- **Cross-store Auth**: `https://your-app.fly.dev:4000/express/auth/*`
- **Cross-store API**: `https://your-app.fly.dev:4000/express/api/*`

### 3. Run Health Checks

```bash
# Run health check script locally
npm run health-check

# Or run directly
bash infrastructure/scripts/health-check.sh
```

### 4. Test Multistore Application Functionality

1. **Web Interface**: Visit `https://your-app.fly.dev` (Remix app)
2. **Express Server**: Test `https://your-app.fly.dev:4000/express/health`
3. **Cross-store Auth**: Test `https://your-app.fly.dev:4000/express/auth/health`
4. **Webhook Handlers**: Test `https://your-app.fly.dev:4000/express/webhooks/health`
5. **Database**: Verify database connectivity through health endpoints
6. **Individual Stores**: Test authentication flows for each Shopify store:
   - stylish-stitches-jordin-kolman.myshopify.com
   - pld-app-development-store.myshopify.com
   - 75th-rra-app-development-store.myshopify.com

## Scaling and Management

### Scaling

```bash
# Scale both processes to multiple instances
flyctl scale count 2

# Scale specific process types
flyctl scale count web=2 multistore=1

# Scale memory for all processes
flyctl scale memory 2048

# Scale CPU for better performance
flyctl scale vm shared-cpu-2x

# Scale processes independently based on load
flyctl scale count web=3 multistore=2  # More web instances for UI load
```

### Monitoring

```bash
# View metrics
flyctl metrics

# SSH into running instance
flyctl ssh console

# View process status
flyctl status --all
```

## Troubleshooting

### Common Issues

1. **Health Check Failures**
   ```bash
   # Check logs for specific errors
   flyctl logs --app americans-united-inc

   # Test health endpoints manually
   curl -v https://your-app.fly.dev/healthz
   ```

2. **Database Connection Issues**
   ```bash
   # Verify database URL
   flyctl secrets list | grep DATABASE_URL

   # Test database connection
   flyctl ssh console
   npm run health-check
   ```

3. **Memory Issues**
   ```bash
   # Check memory usage
   flyctl metrics

   # Scale memory if needed
   flyctl scale memory 1024
   ```

### Debug Commands

```bash
# View detailed app information
flyctl info

# Check secrets
flyctl secrets list

# View deployment history
flyctl releases

# Rollback to previous version
flyctl releases rollback
```

## Environment Variables

### Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `********************************/db` |
| `SESSION_SECRET` | Session encryption key | `your-secure-secret` |
| `SHOPIFY_API_KEY_*` | Shopify API keys for each store | `your-api-key` |
| `SHOPIFY_API_SECRET_*` | Shopify API secrets for each store | `your-api-secret` |
| `SHIPSTATION_API_KEY` | ShipStation API key | `your-shipstation-key` |
| `SHIPSTATION_API_SECRET` | ShipStation API secret | `your-shipstation-secret` |
| `ADMIN_SHOP` | Admin shop domain | `admin.myshopify.com` |

### Optional Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Node environment | `production` |
| `PORT` | Remix server port | `3000` |
| `EXPRESS_PORT` | Express server port | `4000` |

## Security Considerations

1. **Secrets Management**: All sensitive data is stored as Fly.io secrets
2. **HTTPS**: All traffic is encrypted with automatic TLS certificates
3. **Network Security**: Internal communication between processes
4. **Container Security**: Non-root user execution in containers

## Backup and Recovery

### Database Backups

```bash
# Create manual backup
flyctl postgres backup create --app americans-united-db

# List backups
flyctl postgres backup list --app americans-united-db

# Restore from backup
flyctl postgres backup restore <backup-id> --app americans-united-db
```

### Application Rollback

```bash
# View release history
flyctl releases

# Rollback to previous version
flyctl releases rollback

# Rollback to specific version
flyctl releases rollback --version 42
```

## Support

For deployment issues:
1. Check the logs: `flyctl logs`
2. Verify health endpoints
3. Review environment variables
4. Check database connectivity
5. Contact support with specific error messages

## Useful Commands Reference

```bash
# Deployment
npm run deploy:fly                    # Full deployment with tests
npm run deploy:fly:skip-tests        # Quick deployment
flyctl deploy --dockerfile Dockerfile.multiprocess

# Monitoring
flyctl status                        # App status
flyctl logs                         # View logs
flyctl metrics                      # Performance metrics
npm run health-check                # Health check

# Management
flyctl scale count 2                # Scale instances
flyctl scale memory 1024           # Scale memory
flyctl ssh console                 # SSH access
flyctl secrets set KEY=value       # Set secrets

# Database
flyctl postgres connect            # Connect to database
flyctl postgres backup create     # Create backup
flyctl postgres backup list       # List backups
```
