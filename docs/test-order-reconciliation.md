# Test Order Reconciliation

This document explains the test order reconciliation process implemented to ensure that test orders are properly processed and reflected in invoice balances.

## Background

Shopify test orders (created with the `test: true` flag) do not trigger webhooks by default. This means that when test orders are created, the normal webhook-based processing doesn't occur, and invoice balances aren't updated.

To solve this issue, we've implemented an automated reconciliation process that periodically checks for test orders and processes them to update invoice balances.

## How It Works

The test order reconciliation process works as follows:

1. A scheduled job runs at regular intervals (default: every 5 minutes)
2. The job queries the Shopify Admin API for test orders created within a specified time window (default: last hour)
3. For each test order found, it processes the line items and updates the appropriate invoice balances
4. The job tracks processed orders to avoid duplicate processing

## Components

The test order reconciliation system consists of the following components:

### 1. Reconciliation Function

The `reconcileTestOrders` function in `app/utils/reconciliation.js` is responsible for:
- Querying the Shopify Admin API for test orders
- Processing each order's line items
- Updating invoice balances
- Tracking processed orders

### 2. Scheduled Reconciliation

The `reconcileAllTestOrders` function in `app/utils/scheduled-reconciliation.js` orchestrates the process for all shops:
- Retrieves all shops from the database
- Runs the reconciliation process for each shop
- Tracks overall progress and results

### 3. API Endpoint

The `/api/scheduled-reconciliation` endpoint in `app/routes/api.scheduled-reconciliation.jsx` provides an HTTP interface to:
- Trigger the reconciliation process manually
- Check the status of running reconciliation jobs
- View reconciliation job history

### 4. Cron Job Setup

The `scripts/setup-test-order-reconciliation.js` script sets up a cron job to:
- Run the reconciliation process at regular intervals
- Log the results
- Handle errors

## Configuration

The test order reconciliation process can be configured with the following options:

### Time Window

By default, the reconciliation process looks back 1 hour for test orders. This can be adjusted by setting the `hoursToLookBack` parameter when calling the API.

### Frequency

The default frequency is every 5 minutes. This can be adjusted in the `scripts/setup-test-order-reconciliation.js` script by changing the `INTERVAL_MINUTES` constant.

### Batch Size

By default, the reconciliation process processes up to 100 test orders per shop per run. This can be adjusted by setting the `limit` parameter in the `reconcileTestOrders` function.

## Running Manually

You can trigger the test order reconciliation process manually by making a POST request to the `/api/scheduled-reconciliation` endpoint:

```bash
# When running locally with Shopify CLI:
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-API-Key: YOUR_SHOPIFY_API_KEY" \
  -d '{"type": "test-orders", "hoursToLookBack": 1}' \
  /api/scheduled-reconciliation

# When running on a deployed server:
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-API-Key: YOUR_SHOPIFY_API_KEY" \
  -d '{"type": "test-orders", "hoursToLookBack": 1}' \
  https://your-app-url.com/api/scheduled-reconciliation
```

## Setting Up the Cron Job

To set up the cron job for automatic reconciliation:

1. **Start your Shopify app in development mode**:
   ```bash
   npm run dev
   ```
   This ensures that Shopify CLI assigns a Cloudflare URL to your app and updates the TOML file.

2. **In a separate terminal, run the setup script**:
   ```bash
   node scripts/setup-test-order-reconciliation.js
   ```
   The script will:
   - Read the application URL from the TOML file
   - Use your Shopify API key for authentication
   - Start a process that runs the reconciliation job every 5 minutes

3. **Keep both processes running**:
   - The Shopify app development server must remain running
   - The reconciliation script must remain running

> **Important**: The reconciliation script reads the URL from the TOML file, which is updated by Shopify CLI when you run `npm run dev`. If you restart your development server, you'll need to restart the reconciliation script as well to pick up the new URL.

## Monitoring

You can monitor the reconciliation process through:

1. Application logs - The process logs detailed information about each run
2. The `/api/scheduled-reconciliation` endpoint - Returns information about running jobs
3. The reconciliation job records in the database - Stores historical job information

## Troubleshooting

If invoice balances aren't being updated for test orders, check the following:

1. Ensure the reconciliation process is running (check logs or the API endpoint)
2. Verify that test orders are being created with the correct attributes
3. Check for errors in the reconciliation job logs
4. Verify that the Shopify API credentials have the necessary permissions

## Implementation Details

The test order reconciliation process uses the same core processing logic as the webhook-based order processing, ensuring consistent behavior between real and test orders.

Key differences:
- It specifically queries for orders with `test: true`
- It uses a shorter time window (hours instead of days)
- It runs at more frequent intervals
- It has a higher batch size limit
