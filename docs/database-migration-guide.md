# Database Migration Guide: SQLite to PostgreSQL

This guide walks you through migrating from SQLite to PostgreSQL and resolving the migration provider mismatch.

## Problem

You're seeing this error:
```
The datasource provider `postgresql` specified in your schema does not match the one specified in the migration_lock.toml, `sqlite`.
```

This happens because:
1. Your Prisma schema is configured for PostgreSQL
2. Your migration history was created for SQLite
3. Prisma requires a clean migration history when switching database providers

## Solution Options

### Option 1: Automated Script (Recommended)

Run the automated reset script:

```bash
node scripts/migration/reset-for-postgresql.js
```

This script will:
- Backup your existing SQLite migrations
- Remove the old migration directory
- Create a new PostgreSQL migration history
- Generate the Prisma client

### Option 2: Manual Steps

If you prefer to do it manually:

#### Step 1: Backup Current Data (if needed)

If you have important data in SQLite that you want to migrate:

```bash
# Export data from SQLite
npm run export-data
```

#### Step 2: Remove Migration History

```bash
# Remove the entire migrations directory
rm -rf prisma/migrations
```

#### Step 3: Verify PostgreSQL Connection

Make sure your `.env` file has the correct PostgreSQL connection:

```env
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"
```

#### Step 4: Create New Migration History

```bash
# Create initial migration for PostgreSQL
npx prisma migrate dev --name init
```

This will:
- Create a new `prisma/migrations` directory
- Generate the initial migration SQL for PostgreSQL
- Create a new `migration_lock.toml` with `provider = "postgresql"`
- Apply the migration to your PostgreSQL database

#### Step 5: Generate Prisma Client

```bash
npx prisma generate
```

#### Step 6: Import Data (if applicable)

If you exported data from SQLite:

```bash
npm run import-data
```

## Verification

After completing the migration reset:

1. **Check migration lock file:**
   ```bash
   cat prisma/migrations/migration_lock.toml
   ```
   Should show: `provider = "postgresql"`

2. **Verify database connection:**
   ```bash
   npx prisma db pull
   ```

3. **Test application:**
   ```bash
   npm run dev
   ```

4. **Run health check:**
   ```bash
   node -e "
   import('./app/utils/database-health.js').then(({ quickHealthCheck }) => {
     quickHealthCheck().then(result => {
       console.log('Health check:', result);
       process.exit(result.status === 'healthy' ? 0 : 1);
     });
   });
   "
   ```

## Common Issues and Solutions

### Issue: "Database does not exist"

**Solution:** Create the database first:
```sql
-- Connect to PostgreSQL as superuser
CREATE DATABASE your_database_name;
```

### Issue: "Connection refused"

**Solutions:**
1. Start PostgreSQL service:
   ```bash
   # macOS with Homebrew
   brew services start postgresql
   
   # Linux with systemd
   sudo systemctl start postgresql
   
   # Docker
   docker-compose up postgres
   ```

2. Check connection string format:
   ```
   postgresql://[user[:password]@][host][:port][/dbname][?param1=value1&...]
   ```

### Issue: "Permission denied"

**Solution:** Ensure the PostgreSQL user has proper permissions:
```sql
-- Grant permissions to your user
GRANT ALL PRIVILEGES ON DATABASE your_database_name TO your_username;
```

### Issue: "Migration failed"

**Solutions:**
1. Check if tables already exist in PostgreSQL:
   ```bash
   npx prisma db pull
   ```

2. If tables exist, you might need to reset the database:
   ```bash
   npx prisma migrate reset
   ```

## Data Migration Workflow

If you're migrating from an existing SQLite database:

1. **Export SQLite data:**
   ```bash
   npm run export-data
   ```

2. **Reset migration history** (using either method above)

3. **Import data to PostgreSQL:**
   ```bash
   npm run import-data
   ```

4. **Validate migration:**
   ```bash
   npm run validate-migration
   ```

## Environment Variables

Ensure these are set in your `.env` file:

```env
# PostgreSQL connection
DATABASE_URL="postgresql://user:password@localhost:5432/dbname"

# Optional: Test database for running tests
TEST_DATABASE_URL="postgresql://user:password@localhost:5432/test_dbname"

# Application settings
NODE_ENV="development"
PORT=3000
EXPRESS_PORT=4000

# Your other environment variables...
```

## Post-Migration Checklist

- [ ] Migration lock file shows PostgreSQL provider
- [ ] Database connection works
- [ ] All tables are created correctly
- [ ] Data is imported (if applicable)
- [ ] Application starts without errors
- [ ] Tests pass
- [ ] Health checks pass

## Rollback Plan

If you need to rollback to SQLite:

1. **Restore schema:**
   ```prisma
   datasource db {
     provider = "sqlite"
     url      = "file:./dev.sqlite"
   }
   ```

2. **Remove PostgreSQL migrations:**
   ```bash
   rm -rf prisma/migrations
   ```

3. **Restore SQLite migrations:**
   ```bash
   cp -r prisma/migrations-sqlite-backup prisma/migrations
   ```

4. **Generate client:**
   ```bash
   npx prisma generate
   ```

## Support

If you encounter issues:

1. Check the [Prisma documentation](https://www.prisma.io/docs/guides/database/developing-with-prisma-migrate/troubleshooting-development)
2. Review the error logs carefully
3. Ensure all environment variables are set correctly
4. Verify PostgreSQL is running and accessible
