#!/bin/bash

# Start script for Americans United Inc Shopify App
# Handles both Remix and Express server startup based on process type

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# Function to check if a port is available
check_port() {
    local port=$1
    if netstat -tuln | grep -q ":$port "; then
        return 1  # Port is in use
    else
        return 0  # Port is available
    fi
}

# Function to wait for database connection
wait_for_database() {
    log "Waiting for database connection..."

    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if npx prisma db push --accept-data-loss > /dev/null 2>&1; then
            success "Database connection established"
            return 0
        fi

        warn "Database connection attempt $attempt/$max_attempts failed, retrying in 2 seconds..."
        sleep 2
        attempt=$((attempt + 1))
    done

    error "Failed to connect to database after $max_attempts attempts"
    return 1
}

# Function to run database migrations
run_migrations() {
    log "Running database migrations..."

    if npx prisma migrate deploy; then
        success "Database migrations completed successfully"
    else
        error "Database migrations failed"
        return 1
    fi
}

# Function to start Remix server (web process)
start_web() {
    log "Starting Remix web server on port $PORT..."

    # Check if port is available
    if ! check_port $PORT; then
        error "Port $PORT is already in use"
        return 1
    fi

    # Start Remix server
    exec npm run start:production
}

# Function to start Express multistore server
start_multistore() {
    log "Starting Express multistore server on port $EXPRESS_PORT..."

    # Check if port is available
    if ! check_port $EXPRESS_PORT; then
        error "Port $EXPRESS_PORT is already in use"
        return 1
    fi

    # Start Express multistore server
    exec npm run start:express:production
}

# Legacy function names for backward compatibility
start_remix() {
    start_web
}

start_express() {
    start_multistore
}

# Function to start both servers with proper routing
start_both() {
    log "Starting both Remix and Express servers with routing..."

    # Check if ports are available
    if ! check_port $PORT; then
        error "Port $PORT is already in use"
        return 1
    fi

    if ! check_port $EXPRESS_PORT; then
        error "Port $EXPRESS_PORT is already in use"
        return 1
    fi

    # Start Express server in background
    log "Starting Express server on port $EXPRESS_PORT..."
    npm run start:express:production &
    EXPRESS_PID=$!

    # Wait a moment for Express to start
    sleep 3

    # Start Remix server with proxy configuration
    log "Starting Remix server on port $PORT with webhook routing..."
    exec npm run start:production
}

# Main execution logic
main() {
    log "Starting Americans United Inc Shopify App..."
    log "NODE_ENV: $NODE_ENV"
    log "PORT: $PORT"
    log "EXPRESS_PORT: $EXPRESS_PORT"

    # Wait for database connection
    if ! wait_for_database; then
        error "Failed to establish database connection"
        exit 1
    fi

    # Run database migrations
    if ! run_migrations; then
        error "Failed to run database migrations"
        exit 1
    fi

    # Determine which process to start based on environment or arguments
    case "${1:-$FLY_PROCESS_GROUP}" in
        "web")
            start_web
            ;;
        "multistore")
            start_multistore
            ;;
        "remix")
            start_remix  # Legacy compatibility
            ;;
        "express")
            start_express  # Legacy compatibility
            ;;
        "app"|"both"|"")
            start_both
            ;;
        *)
            error "Unknown process type: $1"
            error "Usage: $0 [web|multistore|both]"
            error "Legacy: [remix|express] (mapped to web|multistore)"
            exit 1
            ;;
    esac
}

# Handle signals gracefully
trap 'log "Received shutdown signal, exiting..."; exit 0' SIGTERM SIGINT

# Run main function
main "$@"
