/**
 * Jest global teardown
 * Runs once after all tests
 */

export default async function globalTeardown() {
  const testDuration = Date.now() - (global.testStartTime || Date.now());
  console.log(`🧪 Jest test suite completed in ${testDuration}ms`);
  
  // Clean up any global resources
  // This is where you would close database connections, etc.
  
  console.log('✅ Jest test environment teardown complete');
}
