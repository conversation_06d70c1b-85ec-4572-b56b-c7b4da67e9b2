import "@shopify/shopify-app-remix/adapters/node";
import {
  ApiVersion,
  AppDistribution,
  shopifyApp,
  LogSeverity,
} from "@shopify/shopify-app-remix/server";
import { PrismaSessionStorage } from "@shopify/shopify-app-session-storage-prisma";
import { restResources } from "@shopify/shopify-api/rest/admin/2025-01";
import prisma from "./db.server";
import { config } from "./lib/config/index.js";

// Silence deprecation warnings
const originalWarn = console.warn;
console.warn = function(message, ...args) {
  if (typeof message === 'string' &&
      (message.includes('API Deprecation Notice') ||
       message.includes('shopify-api/WARNING') ||
       message.toLowerCase().includes('deprecated') ||
       message.toLowerCase().includes('deprecation'))) {
    return;
  }
  originalWarn.apply(console, [message, ...args]);
};

// Use dynamic store configurations from the centralized config
const storeConfigs = config.multistore.storeConfigs;

console.log('Store configurations loaded:', Object.keys(storeConfigs));
console.log(`Supported stores: ${config.multistore.supportedStores.length}`);
console.log('Supported stores:', config.multistore.supportedStores);

// Cache for Shopify app instances
const shopifyApps = new Map();

/**
 * Get or create a Shopify app instance for a specific store
 * @param {string} shop - The shop domain
 * @returns {object} Shopify app instance
 */
function getShopifyAppForStore(shop) {
  if (shopifyApps.has(shop)) {
    return shopifyApps.get(shop);
  }

  const storeConfig = storeConfigs[shop];
  if (!storeConfig || !storeConfig.apiKey || !storeConfig.apiSecret) {
    console.warn(`No configuration found for store: ${shop}`);
    return null;
  }

  try {
    const shopifyAppInstance = shopifyApp({
      apiKey: storeConfig.apiKey,
      apiSecretKey: storeConfig.apiSecret,
      apiVersion: ApiVersion.January25,
      scopes: config.shopify.scopes,
      appUrl: config.app.appUrl,
      authPathPrefix: "/auth",
      sessionStorage: new PrismaSessionStorage(prisma),
      distribution: AppDistribution.AppStore,
      restResources,
      future: {
        unstable_newEmbeddedAuthStrategy: true,
        removeRest: false,
      },
      logger: {
        level: LogSeverity.Error,
      },
      ...(process.env.SHOP_CUSTOM_DOMAIN
        ? { customShopDomains: [process.env.SHOP_CUSTOM_DOMAIN] }
        : {}),
    });

    shopifyApps.set(shop, shopifyAppInstance);
    console.log(`Created Shopify app instance for store: ${shop}`);
    return shopifyAppInstance;
  } catch (error) {
    console.error(`Failed to create Shopify app for ${shop}:`, error);
    return null;
  }
}

/**
 * Extract shop domain from request
 * @param {Request} request - The request object
 * @returns {string|null} Shop domain
 */
function extractShopFromRequest(request) {
  const url = new URL(request.url);

  // Check JWT token in Authorization header first (for API requests)
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    try {
      const token = authHeader.substring(7); // Remove 'Bearer ' prefix
      // Simple JWT decode without verification (just to extract shop info)
      const payload = JSON.parse(atob(token.split('.')[1]));

      if (payload.iss) {
        // Extract shop from issuer URL (e.g., "https://americansunitedinc.myshopify.com/admin")
        const match = payload.iss.match(/https:\/\/([^\/]+)\.myshopify\.com/);
        if (match) {
          const shopDomain = `${match[1]}.myshopify.com`;
          console.log(`[Multistore Auth] Extracted shop from JWT: ${shopDomain}`);
          return shopDomain;
        }
      }
    } catch (error) {
      console.warn('Failed to extract shop from JWT token:', error.message);
    }
  }

  // Check query parameters
  const shopFromQuery = url.searchParams.get('shop');
  if (shopFromQuery) {
    return shopFromQuery.includes('.myshopify.com')
      ? shopFromQuery
      : `${shopFromQuery}.myshopify.com`;
  }

  // Check headers
  const shopFromHeader = request.headers.get('X-Shopify-Shop-Domain');
  if (shopFromHeader) {
    return shopFromHeader.includes('.myshopify.com')
      ? shopFromHeader
      : `${shopFromHeader}.myshopify.com`;
  }

  // Check for embedded app parameters (host parameter contains shop info)
  const hostParam = url.searchParams.get('host');
  if (hostParam) {
    try {
      // Host parameter is base64 encoded and contains shop domain
      const decodedHost = atob(hostParam);
      // Format: admin.shopify.com/store/shop-name
      const match = decodedHost.match(/admin\.shopify\.com\/store\/(.+)/);
      if (match) {
        const shopName = match[1];
        return shopName.includes('.myshopify.com')
          ? shopName
          : `${shopName}.myshopify.com`;
      }
    } catch (error) {
      console.warn('Failed to decode host parameter:', error);
    }
  }

  // Check for session parameter (contains shop info)
  const sessionParam = url.searchParams.get('session');
  if (sessionParam) {
    // Session parameter might contain shop information
    // This is a fallback for when other methods fail
    console.log('Found session parameter, but no shop extracted yet:', sessionParam);
  }

  return null;
}

/**
 * Multistore-aware authentication function
 * @param {Request} request - The request object
 * @returns {object|Response} Authentication result or redirect response
 */
export async function authenticateMultistore(request) {
  let shop = extractShopFromRequest(request);

  console.log(`[Multistore Auth] Extracted shop: ${shop}`);
  console.log(`[Multistore Auth] Request URL: ${request.url}`);

  // If we have a shop parameter, try to authenticate with that specific store
  if (shop) {
    const shopifyApp = getShopifyAppForStore(shop);
    if (!shopifyApp) {
      throw new Error(`No Shopify app configuration found for store: ${shop}`);
    }

    try {
      console.log(`[Multistore Auth] Attempting authentication for shop: ${shop}`);
      return await shopifyApp.authenticate.admin(request);
    } catch (error) {
      console.log(`[Multistore Auth] Authentication failed for ${shop}:`, error.message);
      // If it's a Response (like a redirect), return it instead of throwing
      if (error instanceof Response) {
        return error;
      }
      // For other errors, re-throw
      throw error;
    }
  }

  // If no shop parameter found, try each supported store until one works
  console.log(`[Multistore Auth] No shop parameter found, trying all supported stores`);
  const supportedStores = Object.keys(storeConfigs);

  for (const storeDomain of supportedStores) {
    console.log(`[Multistore Auth] Trying authentication with store: ${storeDomain}`);
    const shopifyApp = getShopifyAppForStore(storeDomain);

    if (!shopifyApp) {
      console.log(`[Multistore Auth] No app instance for ${storeDomain}, skipping`);
      continue;
    }

    try {
      const result = await shopifyApp.authenticate.admin(request);
      console.log(`[Multistore Auth] Successfully authenticated with store: ${storeDomain}`);
      return result;
    } catch (error) {
      console.log(`[Multistore Auth] Authentication failed for ${storeDomain}:`, error.message);

      // If it's a Response (like a redirect), return it - this might be the correct store
      if (error instanceof Response) {
        console.log(`[Multistore Auth] Got redirect response for ${storeDomain}, returning it`);
        return error;
      }

      // For other errors, continue to next store
      continue;
    }
  }

  // If all stores failed, fall back to default authentication
  console.log(`[Multistore Auth] All stores failed, falling back to default authentication`);
  try {
    const { authenticate } = await import("./shopify.server");
    const { session } = await authenticate.admin(request);
    console.log(`[Multistore Auth] Default authentication succeeded for: ${session.shop}`);
    return { session };
  } catch (error) {
    console.log(`[Multistore Auth] Default authentication also failed:`, error.message);
    throw new Error('Multistore authentication failed: No valid store configuration found for this request');
  }
}

// Default export for backward compatibility (uses first available store or fallback)
const defaultShop = config.multistore.supportedStores[0];
const defaultConfig = storeConfigs[defaultShop];

const defaultShopify = shopifyApp({
  apiKey: defaultConfig?.apiKey || process.env.SHOPIFY_API_KEY,
  apiSecretKey: defaultConfig?.apiSecret || process.env.SHOPIFY_API_SECRET || "",
  apiVersion: ApiVersion.January25,
  scopes: config.shopify.scopes,
  appUrl: config.app.appUrl,
  authPathPrefix: "/auth",
  sessionStorage: new PrismaSessionStorage(prisma),
  distribution: AppDistribution.AppStore,
  restResources,
  future: {
    unstable_newEmbeddedAuthStrategy: true,
    removeRest: false,
  },
  logger: {
    level: LogSeverity.Error,
  },
  ...(process.env.SHOP_CUSTOM_DOMAIN
    ? { customShopDomains: [process.env.SHOP_CUSTOM_DOMAIN] }
    : {}),
});

export default defaultShopify;
export const apiVersion = ApiVersion.January25;
export const addDocumentResponseHeaders = defaultShopify.addDocumentResponseHeaders;
export const authenticate = defaultShopify.authenticate;
export const unauthenticated = defaultShopify.unauthenticated;
export const login = defaultShopify.login;
export const registerWebhooks = defaultShopify.registerWebhooks;
export const sessionStorage = defaultShopify.sessionStorage;

/**
 * Get store configuration for a specific shop
 * @param {string} shop - The shop domain
 * @returns {object|null} Store configuration
 */
export function getStoreConfig(shop) {
  console.log(`[getStoreConfig] Looking up config for shop: ${shop}`);
  console.log(`[getStoreConfig] Available configs:`, Object.keys(storeConfigs));
  const config = storeConfigs[shop] || null;
  console.log(`[getStoreConfig] Found config:`, config ? 'yes' : 'no');
  if (config) {
    console.log(`[getStoreConfig] API key starts with: ${config.apiKey ? config.apiKey.substring(0, 8) + '...' : 'none'}`);
  }
  return config;
}

// Export multistore functions
export { getShopifyAppForStore, extractShopFromRequest };
