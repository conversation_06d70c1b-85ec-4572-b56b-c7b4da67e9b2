/**
 * Cache Service
 *
 * This service provides centralized caching functionality
 * with TTL support, memory management, and statistics.
 */

import { BusinessLogicError } from '../../lib/errors/AppError.js';

/**
 * Service for caching operations
 */
export class CacheService {
  constructor() {
    this.cache = new Map();
    this.timers = new Map();
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0
    };

    // Default TTL in milliseconds (1 hour)
    this.DEFAULT_TTL = 60 * 60 * 1000;

    // Maximum cache size (number of entries)
    this.MAX_CACHE_SIZE = 10000;
  }

  /**
   * Set a value in the cache
   * @param {string} key - Cache key
   * @param {*} value - Value to cache
   * @param {number} ttl - Time to live in milliseconds
   * @returns {void}
   */
  set(key, value, ttl = this.DEFAULT_TTL) {
    try {
      // Check if we need to evict entries due to size limit
      if (this.cache.size >= this.MAX_CACHE_SIZE && !this.cache.has(key)) {
        this.evictOldestEntry();
      }

      // Clear existing timer if key already exists
      if (this.timers.has(key)) {
        clearTimeout(this.timers.get(key));
      }

      // Set the value
      this.cache.set(key, {
        value,
        timestamp: Date.now(),
        ttl
      });

      // Set expiration timer
      if (ttl > 0) {
        const timer = setTimeout(() => {
          this.delete(key);
        }, ttl);
        this.timers.set(key, timer);
      }

      this.stats.sets++;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to set cache value: ${error.message}`,
        { key }
      );
    }
  }

  /**
   * Get a value from the cache
   * @param {string} key - Cache key
   * @returns {*} - Cached value or undefined
   */
  get(key) {
    try {
      const entry = this.cache.get(key);

      if (!entry) {
        this.stats.misses++;
        return undefined;
      }

      // Check if entry has expired
      if (entry.ttl > 0 && Date.now() - entry.timestamp > entry.ttl) {
        this.delete(key);
        this.stats.misses++;
        return undefined;
      }

      this.stats.hits++;
      return entry.value;
    } catch (error) {
      this.stats.misses++;
      return undefined;
    }
  }

  /**
   * Check if a key exists in the cache
   * @param {string} key - Cache key
   * @returns {boolean} - True if key exists and is not expired
   */
  has(key) {
    const value = this.get(key);
    return value !== undefined;
  }

  /**
   * Delete a value from the cache
   * @param {string} key - Cache key
   * @returns {boolean} - True if key was deleted
   */
  delete(key) {
    try {
      const existed = this.cache.has(key);

      if (existed) {
        this.cache.delete(key);

        // Clear timer if it exists
        if (this.timers.has(key)) {
          clearTimeout(this.timers.get(key));
          this.timers.delete(key);
        }

        this.stats.deletes++;
      }

      return existed;
    } catch (error) {
      return false;
    }
  }

  /**
   * Clear all cache entries
   * @returns {void}
   */
  clear() {
    try {
      // Clear all timers
      for (const timer of this.timers.values()) {
        clearTimeout(timer);
      }

      this.cache.clear();
      this.timers.clear();

      // Reset stats
      this.stats = {
        hits: 0,
        misses: 0,
        sets: 0,
        deletes: 0,
        evictions: 0
      };
    } catch (error) {
      throw new BusinessLogicError(`Failed to clear cache: ${error.message}`);
    }
  }

  /**
   * Get cache statistics
   * @returns {object} - Cache statistics
   */
  getStats() {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0;

    return {
      ...this.stats,
      size: this.cache.size,
      hitRate: parseFloat(hitRate.toFixed(2)),
      memoryUsage: this.getMemoryUsage()
    };
  }

  /**
   * Get memory usage estimate
   * @returns {object} - Memory usage information
   */
  getMemoryUsage() {
    try {
      let estimatedSize = 0;

      for (const [key, entry] of this.cache.entries()) {
        // Rough estimate of memory usage
        estimatedSize += key.length * 2; // UTF-16 characters
        estimatedSize += JSON.stringify(entry.value).length * 2;
        estimatedSize += 64; // Overhead for entry object
      }

      return {
        estimatedBytes: estimatedSize,
        estimatedMB: parseFloat((estimatedSize / (1024 * 1024)).toFixed(2))
      };
    } catch (error) {
      return {
        estimatedBytes: 0,
        estimatedMB: 0
      };
    }
  }

  /**
   * Evict the oldest entry from the cache
   * @returns {void}
   */
  evictOldestEntry() {
    try {
      let oldestKey = null;
      let oldestTimestamp = Date.now();

      for (const [key, entry] of this.cache.entries()) {
        if (entry.timestamp < oldestTimestamp) {
          oldestTimestamp = entry.timestamp;
          oldestKey = key;
        }
      }

      if (oldestKey) {
        this.delete(oldestKey);
        this.stats.evictions++;
      }
    } catch (error) {
      console.error('Error evicting oldest cache entry:', error);
    }
  }

  /**
   * Clean up expired entries
   * @returns {number} - Number of entries cleaned up
   */
  cleanupExpired() {
    try {
      let cleanedCount = 0;
      const now = Date.now();

      for (const [key, entry] of this.cache.entries()) {
        if (entry.ttl > 0 && now - entry.timestamp > entry.ttl) {
          this.delete(key);
          cleanedCount++;
        }
      }

      return cleanedCount;
    } catch (error) {
      console.error('Error cleaning up expired cache entries:', error);
      return 0;
    }
  }

  /**
   * Get or set a value with a factory function
   * @param {string} key - Cache key
   * @param {Function} factory - Function to generate value if not cached
   * @param {number} ttl - Time to live in milliseconds
   * @returns {Promise<*>} - Cached or generated value
   */
  async getOrSet(key, factory, ttl = this.DEFAULT_TTL) {
    try {
      let value = this.get(key);

      if (value === undefined) {
        value = await factory();
        this.set(key, value, ttl);
      }

      return value;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get or set cache value: ${error.message}`,
        { key }
      );
    }
  }

  /**
   * Get multiple values from cache
   * @param {Array} keys - Array of cache keys
   * @returns {object} - Map of keys to values
   */
  getMultiple(keys) {
    try {
      const results = {};

      for (const key of keys) {
        const value = this.get(key);
        if (value !== undefined) {
          results[key] = value;
        }
      }

      return results;
    } catch (error) {
      return {};
    }
  }

  /**
   * Set multiple values in cache
   * @param {object} entries - Map of keys to values
   * @param {number} ttl - Time to live in milliseconds
   * @returns {void}
   */
  setMultiple(entries, ttl = this.DEFAULT_TTL) {
    try {
      for (const [key, value] of Object.entries(entries)) {
        this.set(key, value, ttl);
      }
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to set multiple cache values: ${error.message}`,
        { entryCount: Object.keys(entries).length }
      );
    }
  }

  /**
   * Get all keys in the cache
   * @returns {Array} - Array of cache keys
   */
  keys() {
    return Array.from(this.cache.keys());
  }

  /**
   * Get cache size
   * @returns {number} - Number of entries in cache
   */
  size() {
    return this.cache.size;
  }

  /**
   * Check if cache is empty
   * @returns {boolean} - True if cache is empty
   */
  isEmpty() {
    return this.cache.size === 0;
  }

  /**
   * Cache prefix for variant fulfillment services
   */
  static VARIANT_FULFILLMENT_PREFIX = 'vfs:';

  /**
   * Get a variant fulfillment service cache key
   * @param {string} variantId - The variant ID
   * @returns {string} - The cache key
   */
  static getVariantFulfillmentKey(variantId) {
    return `${CacheService.VARIANT_FULFILLMENT_PREFIX}${variantId}`;
  }

  /**
   * Legacy compatibility methods to match memory-cache.js interface
   */

  /**
   * Get multiple values from the cache (legacy compatibility)
   * @param {Array<string>} keys - Array of cache keys
   * @returns {Object} - Object mapping keys to values (missing keys are not included)
   */
  getMany(keys) {
    return this.getMultiple(keys);
  }

  /**
   * Set multiple values in the cache (legacy compatibility)
   * @param {Object} items - Object mapping keys to values
   * @param {number} ttl - Time to live in milliseconds
   */
  setMany(items, ttl = this.DEFAULT_TTL) {
    this.setMultiple(items, ttl);
  }

  /**
   * Delete a value from the cache (legacy compatibility)
   * @param {string} key - The cache key
   */
  del(key) {
    return this.delete(key);
  }

  /**
   * Clean up expired items from the cache (legacy compatibility)
   */
  cleanup() {
    return this.cleanupExpired();
  }

  /**
   * Reset the cache (legacy compatibility)
   */
  reset() {
    this.clear();
  }
}
