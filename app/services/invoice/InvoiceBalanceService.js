/**
 * Invoice Balance Service
 *
 * This service manages invoice balance operations, including updating balances
 * by category, month, and year. This is the core functionality for tracking
 * product costs and quantities.
 */

import { container } from '../../lib/container/ServiceContainer.server.js';
import { InvoiceBalanceRepository } from '../../repositories/InvoiceBalanceRepository.js';
import { BusinessLogicError, ValidationError } from '../../lib/errors/AppError.js';

/**
 * Service for managing invoice balances
 */
export class InvoiceBalanceService {
  constructor(dependencies = {}) {
    this.dependencies = dependencies;
    this.prisma = null;
    this.repository = null;
    this.initialized = false;
  }

  async initialize() {
    if (!this.initialized) {
      this.prisma = this.dependencies.prisma || await container.resolve('database');
      this.repository = this.dependencies.repository || new InvoiceBalanceRepository(this.prisma);
      this.initialized = true;
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * Update balance for a category (core functionality from legacy)
   * @param {object} params - Update parameters
   * @param {string} params.shop - Shop domain
   * @param {string} params.category - Product category
   * @param {number} params.month - Month (0-11)
   * @param {number} params.year - Year
   * @param {number} params.quantity - Quantity to add
   * @param {number} params.amount - Amount to add
   * @returns {Promise<object>} - Updated balance
   */
  async updateBalance({ shop, category, month, year, quantity, amount }) {
    try {
      await this.ensureInitialized();

      // Validate inputs
      this.validateUpdateParams({ shop, category, month, year, quantity, amount });

      // Update the balance using the repository
      const result = await this.repository.upsertBalance(
        shop,
        category,
        month,
        year,
        quantity,
        amount
      );

      return result;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to update balance: ${error.message}`,
        { shop, category, month, year, quantity, amount }
      );
    }
  }

  /**
   * Get balance for a specific category
   * @param {string} shop - Shop domain
   * @param {string} category - Product category
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @returns {Promise<object|null>} - Balance object or null
   */
  async getBalance(shop, category, month, year) {
    try {
      await this.ensureInitialized();
      return await this.repository.getBalanceByCategory(shop, category, month, year);
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get balance: ${error.message}`,
        { shop, category, month, year }
      );
    }
  }

  /**
   * Get all balances for a shop by month/year
   * @param {string} shop - Shop domain
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @returns {Promise<Array>} - Array of balance objects
   */
  async getBalancesByMonth(shop, month, year) {
    try {
      await this.ensureInitialized();
      return await this.repository.getBalancesByMonth(shop, month, year);
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get balances by month: ${error.message}`,
        { shop, month, year }
      );
    }
  }

  /**
   * Get total balance for a shop by month/year
   * @param {string} shop - Shop domain
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @returns {Promise<number>} - Total balance amount
   */
  async getTotalBalance(shop, month, year) {
    try {
      await this.ensureInitialized();
      return await this.repository.getTotalBalance(shop, month, year);
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get total balance: ${error.message}`,
        { shop, month, year }
      );
    }
  }

  /**
   * Get balance summary by category
   * @param {string} shop - Shop domain
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @returns {Promise<Array>} - Balance summary by category
   */
  async getBalanceSummary(shop, month, year) {
    try {
      await this.ensureInitialized();
      return await this.repository.getBalanceSummaryByCategory(shop, month, year);
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get balance summary: ${error.message}`,
        { shop, month, year }
      );
    }
  }

  /**
   * Calculate total balance from array of balances (legacy compatibility)
   * @param {Array} balances - Array of balance objects
   * @returns {number} - Total balance
   */
  calculateTotalBalance(balances) {
    if (!balances || !Array.isArray(balances) || balances.length === 0) {
      return 0.0;
    }

    let sum = 0.0;
    for (const balance of balances) {
      if (balance && balance.balance) {
        const balanceValue = Number(balance.balance);
        if (!isNaN(balanceValue)) {
          sum += balanceValue;
        }
      }
    }

    return sum;
  }

  /**
   * Validate update parameters
   * @param {object} params - Parameters to validate
   * @throws {ValidationError} - If validation fails
   */
  validateUpdateParams({ shop, category, month, year, quantity, amount }) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    if (!category) {
      throw new ValidationError('Category is required');
    }

    if (typeof month !== 'number' || month < 0 || month > 11) {
      throw new ValidationError('Month must be a number between 0 and 11');
    }

    if (typeof year !== 'number' || year < 2000 || year > 2100) {
      throw new ValidationError('Year must be a valid number');
    }

    if (typeof quantity !== 'number' || isNaN(quantity)) {
      throw new ValidationError('Quantity must be a valid number');
    }

    if (typeof amount !== 'number' || isNaN(amount)) {
      throw new ValidationError('Amount must be a valid number');
    }
  }
}
