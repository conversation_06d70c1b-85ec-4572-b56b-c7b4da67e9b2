/**
 * Batch Processing Service
 *
 * This service handles batch processing operations with
 * concurrency control, memory management, and error handling.
 */

import { container } from '../../lib/container/ServiceContainer.js';
import { BusinessLogicError } from '../../lib/errors/AppError.js';

/**
 * Service for batch processing operations
 */
export class BatchProcessingService {
  constructor(dependencies = {}) {
    this.config = dependencies.config || container.resolve('config');

    // Default configuration
    this.DEFAULT_BATCH_SIZE = 50;
    this.DEFAULT_CONCURRENCY = 2;
    this.DEFAULT_DELAY_BETWEEN_BATCHES = 1000;
    this.DEFAULT_MAX_RETRIES = 3;
  }

  /**
   * Process items in batches with concurrency control
   * @param {Array} items - Items to process
   * @param {Function} processor - Function to process each item
   * @param {object} options - Processing options
   * @returns {Promise<Array>} - Array of processing results
   */
  async processBatches(items, processor, options = {}) {
    try {
      const {
        batchSize = this.DEFAULT_BATCH_SIZE,
        concurrency = this.DEFAULT_CONCURRENCY,
        delayBetweenBatches = this.DEFAULT_DELAY_BETWEEN_BATCHES,
        maxRetries = this.DEFAULT_MAX_RETRIES,
        progressCallback = null,
        continueOnError = true
      } = options;

      if (!Array.isArray(items)) {
        throw new BusinessLogicError('Items must be an array');
      }

      if (typeof processor !== 'function') {
        throw new BusinessLogicError('Processor must be a function');
      }

      const results = [];
      const totalItems = items.length;
      let processedCount = 0;

      // Split items into batches
      const batches = this.chunkArray(items, batchSize);

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];

        try {
          if (progressCallback) {
            progressCallback({
              batchIndex: batchIndex + 1,
              totalBatches: batches.length,
              processedItems: processedCount,
              totalItems: totalItems,
              currentBatchSize: batch.length
            });
          }

          // Process batch with concurrency control
          const batchResults = await this.processBatchWithConcurrency(
            batch,
            processor,
            {
              concurrency,
              maxRetries,
              continueOnError
            }
          );

          results.push(...batchResults);
          processedCount += batch.length;

          // Add delay between batches if specified
          if (delayBetweenBatches > 0 && batchIndex < batches.length - 1) {
            await this.sleep(delayBetweenBatches);
          }

        } catch (error) {
          if (continueOnError) {
            console.error(`Error processing batch ${batchIndex + 1}:`, error);
            // Add error results for this batch
            const errorResults = batch.map(item => ({
              success: false,
              item,
              error: error.message
            }));
            results.push(...errorResults);
            processedCount += batch.length;
          } else {
            throw error;
          }
        }
      }

      return results;

    } catch (error) {
      throw new BusinessLogicError(
        `Batch processing failed: ${error.message}`,
        { itemCount: items.length }
      );
    }
  }

  /**
   * Process a single batch with concurrency control
   * @param {Array} batch - Batch items to process
   * @param {Function} processor - Processing function
   * @param {object} options - Processing options
   * @returns {Promise<Array>} - Batch results
   */
  async processBatchWithConcurrency(batch, processor, options = {}) {
    const {
      concurrency = this.DEFAULT_CONCURRENCY,
      maxRetries = this.DEFAULT_MAX_RETRIES,
      continueOnError = true
    } = options;

    const results = [];
    const semaphore = new Semaphore(concurrency);

    const processItem = async (item) => {
      await semaphore.acquire();

      try {
        const result = await this.processItemWithRetry(item, processor, maxRetries);
        return {
          success: true,
          item,
          result
        };
      } catch (error) {
        if (continueOnError) {
          return {
            success: false,
            item,
            error: error.message
          };
        } else {
          throw error;
        }
      } finally {
        semaphore.release();
      }
    };

    // Process all items in the batch concurrently
    const promises = batch.map(item => processItem(item));
    const batchResults = await Promise.all(promises);

    return batchResults;
  }

  /**
   * Process a single item with retry logic
   * @param {*} item - Item to process
   * @param {Function} processor - Processing function
   * @param {number} maxRetries - Maximum retry attempts
   * @returns {Promise<*>} - Processing result
   */
  async processItemWithRetry(item, processor, maxRetries) {
    let lastError;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await processor(item);
      } catch (error) {
        lastError = error;

        if (attempt < maxRetries) {
          // Calculate exponential backoff delay
          const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
          await this.sleep(delay);
        }
      }
    }

    throw lastError;
  }

  /**
   * Split array into chunks
   * @param {Array} array - Array to chunk
   * @param {number} size - Chunk size
   * @returns {Array} - Array of chunks
   */
  chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * Sleep for specified milliseconds
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Process items with memory monitoring
   * @param {Array} items - Items to process
   * @param {Function} processor - Processing function
   * @param {object} options - Processing options
   * @returns {Promise<Array>} - Processing results
   */
  async processWithMemoryMonitoring(items, processor, options = {}) {
    const {
      memoryThreshold = 0.8,
      memoryCheckInterval = 100,
      ...batchOptions
    } = options;

    let processedCount = 0;
    const results = [];

    const memoryAwareProcessor = async (item) => {
      // Check memory usage periodically
      if (processedCount % memoryCheckInterval === 0) {
        const memoryUsage = process.memoryUsage();
        const heapUsedRatio = memoryUsage.heapUsed / memoryUsage.heapTotal;

        if (heapUsedRatio > memoryThreshold) {
          console.warn(`High memory usage detected: ${(heapUsedRatio * 100).toFixed(1)}%`);

          // Force garbage collection if available
          if (global.gc) {
            global.gc();
          }

          // Add a small delay to allow memory cleanup
          await this.sleep(100);
        }
      }

      processedCount++;
      return await processor(item);
    };

    return await this.processBatches(items, memoryAwareProcessor, batchOptions);
  }

  /**
   * Get processing statistics
   * @param {Array} results - Processing results
   * @returns {object} - Statistics
   */
  getProcessingStatistics(results) {
    const total = results.length;
    const successful = results.filter(r => r.success).length;
    const failed = total - successful;
    const successRate = total > 0 ? (successful / total) * 100 : 0;

    return {
      total,
      successful,
      failed,
      successRate: parseFloat(successRate.toFixed(2))
    };
  }

  /**
   * Legacy compatibility methods to match batch-processing.js interface
   */

  /**
   * Process items in batches with concurrency control (legacy compatibility)
   * @param {Array} items - Array of items to process
   * @param {Function} processFn - Async function to process each item
   * @param {Object} options - Processing options
   * @returns {Promise<Array>} - Array of results
   */
  async processBatches_Legacy(items, processFn, options = {}) {
    const {
      batchSize = 50,
      concurrency = 2,
      delayBetweenBatches = 1000,
      onBatchComplete = null,
      onProgress = null
    } = options;

    // Split items into batches
    const batches = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }

    const results = [];
    const totalBatches = batches.length;

    // Process batches with concurrency control
    for (let i = 0; i < batches.length; i += concurrency) {
      const batchPromises = batches.slice(i, i + concurrency).map(async (batch, index) => {
        const batchIndex = i + index;

        // Process each item in the batch
        const batchResults = await Promise.all(
          batch.map(item => processFn(item).catch(error => {
            console.error(`Error processing item: ${error.message}`);
            return { error, item };
          }))
        );

        // Call the batch complete callback if provided
        if (onBatchComplete) {
          onBatchComplete(batchIndex, batchResults, totalBatches);
        }

        // Call the progress callback if provided
        if (onProgress) {
          const progress = Math.round(((batchIndex + 1) / totalBatches) * 100);
          onProgress(progress, batchIndex + 1, totalBatches);
        }

        return batchResults;
      });

      // Wait for the current set of batches to complete
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults.flat());

      // Add a delay between batch sets to avoid rate limiting
      if (i + concurrency < batches.length) {
        await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
      }
    }

    return results;
  }

  /**
   * Groups items by a key function for batch processing
   * @param {Array} items - Array of items to group
   * @param {Function} keyFn - Function to extract the key from an item
   * @returns {Object} - Object with keys and arrays of items
   */
  groupItemsByKey(items, keyFn) {
    return items.reduce((groups, item) => {
      const key = keyFn(item);
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(item);
      return groups;
    }, {});
  }
}

/**
 * Semaphore class for concurrency control
 */
class Semaphore {
  constructor(maxConcurrency) {
    this.maxConcurrency = maxConcurrency;
    this.currentConcurrency = 0;
    this.queue = [];
  }

  async acquire() {
    return new Promise((resolve) => {
      if (this.currentConcurrency < this.maxConcurrency) {
        this.currentConcurrency++;
        resolve();
      } else {
        this.queue.push(resolve);
      }
    });
  }

  release() {
    this.currentConcurrency--;
    if (this.queue.length > 0) {
      const next = this.queue.shift();
      this.currentConcurrency++;
      next();
    }
  }
}
