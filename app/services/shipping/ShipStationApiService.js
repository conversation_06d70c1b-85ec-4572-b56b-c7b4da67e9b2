/**
 * ShipStation API Service
 *
 * This service handles all ShipStation API interactions including
 * authentication, rate limiting, and data fetching.
 */

import { container } from '../../lib/container/ServiceContainer.server.js';
import { BusinessLogicError, ValidationError } from '../../lib/errors/AppError.js';
import axios from 'axios';

/**
 * Service for ShipStation API operations
 */
export class ShipStationApiService {
  constructor(dependencies = {}) {
    this.prisma = dependencies.prisma;
    this.config = dependencies.config;
    this.initialized = false;

    if (!this.prisma || !this.config) {
      this.initializeAsync();
    } else {
      this.initialized = true;
    }
  }

  async initializeAsync() {
    if (!this.initialized) {
      this.prisma = this.prisma || await container.resolve('database');
      this.config = this.config || await container.resolve('config');
      this.initialized = true;
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      await this.initializeAsync();
    }
  }

  /**
   * Fetches the list of stores from ShipStation V1 API with retry and backoff
   * @param {number} maxRetries - Maximum number of retries (default: 3)
   * @param {number} initialBackoff - Initial backoff in milliseconds (default: 1000)
   * @returns {Promise<Array>} - Array of stores
   */
  async fetchStores(maxRetries = 3, initialBackoff = 1000) {
    try {
      await this.ensureInitialized();

      const API_KEY = process.env.SHIPSTATION_V1_KEY;
      const API_SECRET = process.env.SHIPSTATION_V1_SECRET;

      if (!API_KEY || !API_SECRET) {
        throw new ValidationError("ShipStation V1 API credentials are not configured");
      }

      const authToken = Buffer.from(`${API_KEY}:${API_SECRET}`).toString('base64');
      let retries = 0;
      let backoff = initialBackoff;

      while (retries <= maxRetries) {
        try {
          console.log('Fetching stores from ShipStation API V1');

          const response = await axios.get('https://ssapi.shipstation.com/stores', {
            headers: {
              'Authorization': `Basic ${authToken}`,
              'Content-Type': 'application/json'
            }
          });

          console.log(`Successfully fetched ${response.data.length} stores from ShipStation API`);
          return response.data;
        } catch (error) {
          if (retries === maxRetries) {
            console.error(`Error fetching ShipStation stores after ${maxRetries} retries: ${error.message}`);
            throw new BusinessLogicError(`Failed to fetch ShipStation stores: ${error.message}`);
          }

          if (error.response && error.response.status === 429) {
            retries++;
            const resetTime = error.response.headers && error.response.headers['x-rate-limit-reset'];

            if (resetTime) {
              const waitTime = (parseInt(resetTime, 10) * 1000) + 500;
              console.log(`Rate limited. Retry ${retries}/${maxRetries} after ${waitTime}ms`);
              await this.sleep(waitTime);
            } else {
              console.log(`Rate limited. Retry ${retries}/${maxRetries} after ${backoff}ms backoff`);
              await this.sleep(backoff);
              backoff = backoff * 2 * (0.8 + Math.random() * 0.4);
            }
          } else {
            throw error;
          }
        }
      }
    } catch (error) {
      throw new BusinessLogicError(
        `ShipStation API error: ${error.message}`,
        { operation: 'fetchStores' }
      );
    }
  }

  /**
   * Fetch labels from ShipStation API using the most efficient approach:
   * Fetch labels directly by date range using V2 API
   * @param {string} storeId - Store ID (used for filtering)
   * @param {string} startDate - Start date (ISO string)
   * @param {string} endDate - End date (ISO string)
   * @param {string} apiKey - API key
   * @param {object} progressOptions - Progress tracking options
   * @returns {Promise<object>} - Labels data
   */
  async fetchLabels(storeId, startDate, endDate, apiKey, progressOptions = null) {
    try {
      if (!apiKey) {
        throw new ValidationError("ShipStation API key is required");
      }

      const { jobId, updateProgress } = progressOptions || {};

      console.log(`Fetching labels directly for store ID: ${storeId}, from ${startDate} to ${endDate}`);

      if (jobId && updateProgress) {
        updateProgress(jobId, {
          status: 'fetching_labels',
          message: 'Fetching labels directly from ShipStation V2 API...',
          progress: 32
        });
      }

      // Fetch labels directly by date range using V2 API (more efficient)
      const allLabels = await this.fetchLabelsDirectly(storeId, startDate, endDate, apiKey, progressOptions);
      console.log(`Found ${allLabels.length} total labels for store ${storeId}`);

      return {
        labels: allLabels,
        formattedStartDate: startDate,
        formattedEndDate: endDate
      };
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to fetch ShipStation labels: ${error.message}`,
        { storeId, startDate, endDate }
      );
    }
  }

  /**
   * Fetch labels directly by date range using V2 API (most efficient approach)
   * @param {string} storeId - Store ID (used for filtering)
   * @param {string} startDate - Start date (ISO string)
   * @param {string} endDate - End date (ISO string)
   * @param {string} apiKey - API key
   * @param {object} progressOptions - Progress tracking options
   * @returns {Promise<Array>} - Array of labels
   */
  async fetchLabelsDirectly(storeId, startDate, endDate, apiKey, progressOptions = null) {
    try {
      const { jobId, updateProgress } = progressOptions || {};
      let allLabels = [];
      let page = 1;
      let hasMorePages = true;
      const pageSize = 100;

      while (hasMorePages) {
        console.log(`Fetching labels page ${page}...`);

        if (jobId && updateProgress) {
          updateProgress(jobId, {
            status: 'fetching_labels_page',
            message: `Fetching labels page ${page}...`,
            progress: 32 + Math.min(10, page * 0.5) // Small progress increment
          });
        }

        const requestParams = {
          created_at_start: startDate,
          created_at_end: endDate,
          page_size: pageSize,
          page: page
        };

        console.log(`API Request params for page ${page}:`, requestParams);

        const response = await this.makeApiRequestWithRetry(
          'https://api.shipstation.com/v2/labels',
          {
            headers: {
              'API-Key': apiKey,
              'Content-Type': 'application/json'
            },
            params: requestParams
          },
          3, // maxRetries
          1000 // initialBackoff
        );

        const labels = response.data.labels || [];

        // Debug: Log the first few labels to see their structure
        if (page === 1 && labels.length > 0) {
          console.log(`Sample labels from page 1 (first 3):`);
          labels.slice(0, 3).forEach((label, index) => {
            console.log(`  Label ${index + 1}: shipment_id=${label.shipment_id}, created_at=${label.created_at}, shipment_cost=${JSON.stringify(label.shipment_cost)}`);
          });
        }

        // Filter labels to only include those for the specified store and exclude voided labels
        const filteredLabels = await this.filterLabelsByStore(labels, storeId, apiKey);

        allLabels = [...allLabels, ...filteredLabels];
        console.log(`Page ${page}: Found ${labels.length} labels, filtered to ${filteredLabels.length} non-voided labels`);

        // Check if there are more pages
        hasMorePages = labels.length === pageSize;
        page++;

        // Add delay between pages to avoid rate limiting
        if (hasMorePages) {
          await this.sleep(1000); // 1 second delay
        }
      }

      console.log(`Total labels found: ${allLabels.length}`);
      return allLabels;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to fetch labels directly: ${error.message}`,
        { storeId, startDate, endDate }
      );
    }
  }

  /**
   * Filter labels by store ID using a more efficient approach:
   * 1. First get all shipment IDs for the store
   * 2. Then filter labels based on those shipment IDs
   * @param {Array} labels - Array of labels to filter
   * @param {string} storeId - Store ID to filter by
   * @param {string} apiKey - API key for shipment lookups
   * @returns {Promise<Array>} - Filtered array of labels
   */
  async filterLabelsByStore(labels, storeId, apiKey) {
    try {
      console.log(`Filtering ${labels.length} labels for store ${storeId}`);

      // First, get all shipment IDs for this store from the same date range
      const labelDates = labels.map(label => label.created_at).filter(Boolean);
      if (labelDates.length === 0) {
        console.log('No labels with created_at dates found');
        return [];
      }

      // Find the date range of the labels
      const sortedDates = labelDates.sort();
      const startDate = sortedDates[0];
      const endDate = sortedDates[sortedDates.length - 1];

      console.log(`Fetching shipments for store ${storeId} from ${startDate} to ${endDate}`);

      // Fetch shipments for this store and date range
      const shipments = await this.fetchShipmentsWithPagination(storeId, startDate, endDate, apiKey);
      const storeShipmentIds = new Set(shipments.map(s => s.shipment_id || s.id));

      console.log(`Found ${storeShipmentIds.size} shipments for store ${storeId}`);

      // Filter labels based on shipment IDs and voided status
      const filteredLabels = labels.filter(label => {
        // Skip voided labels
        if (label.voided || (label.status && label.status.toLowerCase() === 'voided')) {
          return false;
        }

        // Check if label's shipment belongs to this store
        const shipmentId = label.shipment_id;
        if (!shipmentId) {
          return false;
        }

        return storeShipmentIds.has(shipmentId);
      });

      console.log(`Filtered ${labels.length} labels down to ${filteredLabels.length} for store ${storeId}`);
      return filteredLabels;
    } catch (error) {
      console.error(`Error filtering labels by store: ${error.message}`);
      // Return empty array on error to be safe
      return [];
    }
  }



  /**
   * Fetch shipments with pagination (shipments ARE tied to stores)
   * @param {string} storeId - Store ID
   * @param {string} startDate - Start date (ISO string)
   * @param {string} endDate - End date (ISO string)
   * @param {string} apiKey - API key
   * @param {object} progressOptions - Progress tracking options
   * @returns {Promise<Array>} - Array of shipments
   */
  async fetchShipmentsWithPagination(storeId, startDate, endDate, apiKey, progressOptions = null) {
    try {
      const { jobId, updateProgress } = progressOptions || {};
      let allShipments = [];
      let page = 1;
      let hasMorePages = true;
      const pageSize = 100;

      while (hasMorePages) {
        console.log(`Fetching shipments page ${page}...`);

        if (jobId && updateProgress) {
          updateProgress(jobId, {
            status: 'fetching_shipments_page',
            message: `Fetching shipments page ${page}...`,
            progress: 32 + Math.min(5, page * 0.5) // Small progress increment
          });
        }

        // Convert numeric store ID to ShipStation v2 format (se-prefixed)
        const formattedStoreId = storeId.toString().startsWith('se-') ? storeId : `se-${storeId}`;

        const requestParams = {
          store_id: formattedStoreId,
          created_at_start: startDate,
          created_at_end: endDate,
          page_size: pageSize,
          page: page
        };

        console.log(`API Request params for page ${page}:`, requestParams);

        const response = await this.makeApiRequestWithRetry(
          'https://api.shipstation.com/v2/shipments',
          {
            headers: {
              'API-Key': apiKey,
              'Content-Type': 'application/json'
            },
            params: requestParams
          },
          3, // maxRetries
          1000 // initialBackoff
        );

        const shipments = response.data.shipments || [];

        // Debug: Log the first few shipments to see their store_id values
        if (page === 1 && shipments.length > 0) {
          console.log(`Sample shipments from page 1 (first 3):`);
          shipments.slice(0, 3).forEach((shipment, index) => {
            console.log(`  Shipment ${index + 1}: store_id=${shipment.store_id}, storeId=${shipment.storeId}, id=${shipment.shipment_id || shipment.id}`);
          });
        }

        // Filter shipments to only include those for the specified store
        const filteredShipments = shipments.filter(shipment => {
          const shipmentStoreId = shipment.store_id || shipment.storeId;

          // Handle both numeric and se-prefixed store IDs
          const normalizedShipmentStoreId = String(shipmentStoreId).replace(/^se-/, '');
          const normalizedFilterStoreId = String(storeId).replace(/^se-/, '');

          return normalizedShipmentStoreId === normalizedFilterStoreId;
        });

        allShipments = [...allShipments, ...filteredShipments];
        console.log(`Page ${page}: Found ${shipments.length} shipments, filtered to ${filteredShipments.length} for store ${storeId}`);

        // Check if there are more pages
        hasMorePages = shipments.length === pageSize;
        page++;

        // Add delay between pages to avoid rate limiting
        if (hasMorePages) {
          await this.sleep(1000); // 1 second delay
        }
      }

      console.log(`Total shipments found: ${allShipments.length}`);
      return allShipments;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to fetch shipments: ${error.message}`,
        { storeId, startDate, endDate }
      );
    }
  }

  /**
   * Fetch labels for shipments using shipment_id parameter
   * @param {Array} shipments - Array of shipments
   * @param {string} apiKey - API key
   * @param {object} progressOptions - Progress tracking options
   * @returns {Promise<Array>} - Array of labels
   */
  async fetchLabelsForShipments(shipments, apiKey, progressOptions = null) {
    try {
      const { jobId, updateProgress } = progressOptions || {};
      let allLabels = [];
      const totalShipments = shipments.length;
      const batchSize = 256; // Use large batch size like legacy code

      console.log(`Processing ${totalShipments} shipments in batches of ${batchSize}`);

      // Process shipments in batches
      for (let i = 0; i < totalShipments; i += batchSize) {
        const batchNumber = Math.floor(i / batchSize) + 1;
        const totalBatches = Math.ceil(totalShipments / batchSize);
        console.log(`Starting batch ${batchNumber}/${totalBatches} (shipments ${i + 1}-${Math.min(i + batchSize, totalShipments)})`);

        const batch = shipments.slice(i, i + batchSize);
        const batchPromises = batch.map((shipment, batchIndex) =>
          this.fetchLabelsForSingleShipment(shipment, apiKey, i + batchIndex, totalShipments)
        );

        try {
          const batchResults = await Promise.all(batchPromises);
          const batchLabels = batchResults.flat();
          allLabels = [...allLabels, ...batchLabels];

          console.log(`Batch ${batchNumber}/${totalBatches} completed. Added ${batchLabels.length} labels (total: ${allLabels.length})`);

          if (jobId && updateProgress) {
            const progress = 40 + Math.floor(((i + batchSize) / totalShipments) * 30); // Progress from 40% to 70%
            updateProgress(jobId, {
              status: 'processing_shipments',
              message: `Processed ${Math.min(i + batchSize, totalShipments)}/${totalShipments} shipments. Found ${allLabels.length} labels so far...`,
              progress: progress,
              totalItems: totalShipments,
              processedItems: Math.min(i + batchSize, totalShipments)
            });
          }

          // Add delay between batches
          if (i + batchSize < totalShipments) {
            await this.sleep(2000); // 2 second delay between batches
          }
        } catch (batchError) {
          console.error(`Error processing batch ${batchNumber}: ${batchError.message}`);
          // Continue with next batch instead of failing
        }
      }

      console.log(`Total labels found: ${allLabels.length}`);
      return allLabels;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to fetch labels for shipments: ${error.message}`,
        { shipmentCount: shipments.length }
      );
    }
  }

  /**
   * Fetch labels for a single shipment
   * @param {object} shipment - Shipment object
   * @param {string} apiKey - API key
   * @param {number} index - Shipment index
   * @param {number} total - Total shipments
   * @returns {Promise<Array>} - Array of labels for this shipment
   */
  async fetchLabelsForSingleShipment(shipment, apiKey, index, total) {
    const shipmentId = shipment.shipment_id || shipment.id;

    try {
      const response = await this.makeApiRequestWithRetry(
        'https://api.shipstation.com/v2/labels',
        {
          headers: {
            'API-Key': apiKey,
            'Content-Type': 'application/json'
          },
          params: {
            shipment_id: shipmentId
          }
        },
        3, // maxRetries
        1000 // initialBackoff
      );

      let shipmentLabels = [];
      if (response.data && response.data.labels && Array.isArray(response.data.labels)) {
        shipmentLabels = response.data.labels;
      } else if (Array.isArray(response.data)) {
        shipmentLabels = response.data;
      }

      // Filter out voided labels and enrich with shipment data
      const filteredLabels = shipmentLabels
        .filter(label => {
          if (label.status && label.status.toLowerCase() === "voided") {
            console.log(`Skipping voided label for shipment ${shipmentId}: ${label.label_id || 'unknown'}`);
            return false;
          }
          return true;
        })
        .map(label => {
          const enrichedLabel = { ...label };

          // Add shipment information to label
          if (!enrichedLabel.shipment_id) {
            enrichedLabel.shipment_id = shipmentId;
          }
          if (!enrichedLabel.ship_date && shipment.ship_date) {
            enrichedLabel.ship_date = shipment.ship_date;
          }
          if (!enrichedLabel.shipment_cost && (shipment.shipment_cost || shipment.shipping_cost)) {
            enrichedLabel.shipment_cost = shipment.shipment_cost || {
              amount: shipment.shipping_cost || 0,
              currency: "USD"
            };
          }

          return enrichedLabel;
        });

      return filteredLabels;
    } catch (error) {
      console.error(`Error fetching labels for shipment ${shipmentId}: ${error.message}`);

      // If we couldn't fetch labels but have shipment cost data, create a basic label
      if (shipment.shipment_cost || shipment.shipping_cost) {
        console.log(`Creating basic label from shipment ${shipmentId} data`);
        return [{
          shipment_id: shipmentId,
          ship_date: shipment.ship_date,
          shipment_cost: shipment.shipment_cost || {
            amount: shipment.shipping_cost || 0,
            currency: "USD"
          }
        }];
      }

      return []; // Return empty array if we couldn't create a label
    }
  }

  /**
   * Makes an API request with retry and backoff for rate limiting
   * @param {string} url - The URL to request
   * @param {object} options - Request options (headers, params, etc.)
   * @param {number} maxRetries - Maximum number of retries (default: 3)
   * @param {number} initialBackoff - Initial backoff in milliseconds (default: 1000)
   * @param {object} progressOptions - Progress tracking options
   * @returns {Promise<object>} - The API response
   */
  async makeApiRequestWithRetry(url, options, maxRetries = 3, initialBackoff = 1000, progressOptions = null) {
    let retries = 0;
    let backoff = initialBackoff;

    const { jobId, updateProgress, baseProgress = 32, requestType = 'API' } = progressOptions || {};

    const updateProgressIfEnabled = (message, progressIncrement = 0) => {
      if (jobId && updateProgress) {
        updateProgress(jobId, {
          status: 'waiting_response',
          message,
          progress: baseProgress + progressIncrement
        });
      }
    };

    while (retries <= maxRetries) {
      try {
        updateProgressIfEnabled(`Making ${requestType} request (attempt ${retries + 1}/${maxRetries + 1})`);
        return await axios.get(url, options);
      } catch (error) {
        if (retries === maxRetries) {
          console.error(`Error making API request after ${maxRetries} retries: ${error.message}`);
          throw error;
        }

        if (error.response && error.response.status === 429) {
          retries++;
          const resetTime = error.response.headers && error.response.headers['x-rate-limit-reset'];

          if (resetTime) {
            const waitTime = (parseInt(resetTime, 10) * 1000) + 500;
            updateProgressIfEnabled(`Rate limited. Waiting ${waitTime}ms...`);
            await this.sleep(waitTime);
          } else {
            updateProgressIfEnabled(`Rate limited. Backing off ${backoff}ms...`);
            await this.sleep(backoff);
            backoff = backoff * 2 * (0.8 + Math.random() * 0.4);
          }
        } else {
          throw error;
        }
      }
    }

    throw new Error(`Failed to make API request after ${maxRetries} retries`);
  }

  /**
   * Sleep for a specified number of milliseconds
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get ShipStation store mappings for a shop
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<Array>} - Store mappings
   */
  async getStoreMappings(shopDomain) {
    try {
      await this.ensureInitialized();

      const mappings = await this.prisma.shipStationStoreMapping.findMany({
        where: {
          shop: shopDomain,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return mappings;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get store mappings: ${error.message}`,
        { shopDomain }
      );
    }
  }

  /**
   * Get all ShipStation store mappings
   * @returns {Promise<Array>} - All store mappings
   */
  async getAllStoreMappings() {
    try {
      await this.ensureInitialized();

      const mappings = await this.prisma.shipStationStoreMapping.findMany({
        orderBy: {
          createdAt: 'desc',
        },
      });

      return mappings;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get all store mappings: ${error.message}`
      );
    }
  }

  /**
   * Create or update store mapping
   * @param {string} shopDomain - Shop domain
   * @param {string} storeId - ShipStation store ID
   * @param {string} storeName - Store name
   * @returns {Promise<object>} - Created/updated mapping
   */
  async createOrUpdateStoreMapping(shopDomain, storeId, storeName) {
    try {
      await this.ensureInitialized();

      const mapping = await this.prisma.shipStationStoreMapping.upsert({
        where: {
          shop_storeId: {
            shop: shopDomain,
            storeId: storeId,
          },
        },
        update: {
          storeName: storeName,
          updatedAt: new Date(),
        },
        create: {
          shop: shopDomain,
          storeId: storeId,
          storeName: storeName,
        },
      });

      return mapping;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to create/update store mapping: ${error.message}`,
        { shopDomain, storeId, storeName }
      );
    }
  }

  /**
   * Bulk create or update ShipStation stores
   * @param {Array} stores - Array of store objects
   * @returns {Promise<object>} - Operation result
   */
  async bulkCreateOrUpdateStores(stores) {
    try {
      await this.ensureInitialized();

      const results = {
        created: 0,
        updated: 0,
        errors: []
      };

      for (const store of stores) {
        try {
          const existing = await this.prisma.shipStationStoreMapping.findFirst({
            where: {
              storeId: store.storeId.toString(),
            },
          });

          if (existing) {
            await this.prisma.shipStationStoreMapping.update({
              where: { storeId: store.storeId.toString() },
              data: {
                storeName: store.storeName,
                updatedAt: new Date(),
              },
            });
            results.updated++;
          } else {
            await this.prisma.shipStationStoreMapping.create({
              data: {
                shop: '', // Will be set when mapping is configured
                storeId: store.storeId.toString(),
                storeName: store.storeName,
              },
            });
            results.created++;
          }
        } catch (error) {
          results.errors.push({
            store: store.storeId,
            error: error.message,
          });
        }
      }

      return results;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to bulk update stores: ${error.message}`,
        { storeCount: stores.length }
      );
    }
  }
}
