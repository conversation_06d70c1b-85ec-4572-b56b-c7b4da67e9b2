/**
 * ShipStation Order Service
 *
 * This service handles ShipStation order operations including
 * fetching orders, extracting carrier fees, and processing shipments.
 */

import { container } from '../../lib/container/ServiceContainer.server.js';
import { BusinessLogicError, ValidationError } from '../../lib/errors/AppError.js';
import axios from 'axios';

/**
 * Service for ShipStation order operations
 */
export class ShipStationOrderService {
  constructor(dependencies = {}) {
    this.prisma = dependencies.prisma;
    this.shipstationApiService = dependencies.shipstationApiService;
    this.initialized = false;

    if (!this.prisma || !this.shipstationApiService) {
      this.initializeAsync();
    } else {
      this.initialized = true;
    }
  }

  async initializeAsync() {
    if (!this.initialized) {
      this.prisma = this.prisma || await container.resolve('database');
      this.shipstationApiService = this.shipstationApiService || await container.resolve('shipstationApiService');
      this.initialized = true;
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      await this.initializeAsync();
    }
  }

  /**
   * Fetch orders from ShipStation
   * @param {string} storeId - Store ID
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @param {object} options - Additional options
   * @returns {Promise<Array>} - Array of orders
   */
  async fetchOrders(storeId, startDate, endDate, options = {}) {
    try {
      const {
        pageSize = 100,
        maxPages = 10,
        progressCallback = null
      } = options;

      const API_KEY = process.env.SHIPSTATION_API_KEY;
      if (!API_KEY) {
        throw new ValidationError("ShipStation API key is not configured");
      }

      let allOrders = [];
      let page = 1;
      let hasMorePages = true;

      while (hasMorePages && page <= maxPages) {
        try {
          if (progressCallback) {
            progressCallback(`Fetching page ${page} of orders...`);
          }

          const response = await this.shipstationApiService.makeApiRequestWithRetry(
            'https://api.shipstation.com/v2/orders',
            {
              headers: {
                'API-Key': API_KEY,
                'Content-Type': 'application/json'
              },
              params: {
                store_id: storeId,
                created_at_start: startDate,
                created_at_end: endDate,
                page_size: pageSize,
                page: page
              }
            }
          );

          const orders = response.data.orders || [];
          allOrders = allOrders.concat(orders);

          // Check if there are more pages
          hasMorePages = orders.length === pageSize;
          page++;

          if (progressCallback) {
            progressCallback(`Fetched ${allOrders.length} orders so far...`);
          }

        } catch (error) {
          console.error(`Error fetching orders page ${page}:`, error.message);
          break;
        }
      }

      return allOrders;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to fetch ShipStation orders: ${error.message}`,
        { storeId, startDate, endDate }
      );
    }
  }

  /**
   * Extract carrier fee from order
   * @param {object} order - Order object
   * @returns {number} - Carrier fee amount
   */
  extractCarrierFee(order) {
    try {
      let carrierFee = 0;

      // Check various possible fields for carrier fee
      if (order.shipment_cost && order.shipment_cost.amount) {
        carrierFee = parseFloat(order.shipment_cost.amount);
      } else if (order.shipment_cost && typeof order.shipment_cost === 'number') {
        carrierFee = order.shipment_cost;
      } else if (order.shipping_cost) {
        carrierFee = parseFloat(order.shipping_cost);
      } else if (order.carrier_fee) {
        carrierFee = parseFloat(order.carrier_fee);
      }

      return isNaN(carrierFee) ? 0 : carrierFee;
    } catch (error) {
      console.error(`Error extracting carrier fee from order:`, error);
      return 0;
    }
  }

  /**
   * Fetch shipments from ShipStation
   * @param {string} storeId - Store ID
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @param {object} options - Additional options
   * @returns {Promise<Array>} - Array of shipments
   */
  async fetchShipments(storeId, startDate, endDate, options = {}) {
    try {
      const {
        pageSize = 100,
        maxPages = 10,
        progressCallback = null
      } = options;

      const API_KEY = process.env.SHIPSTATION_API_KEY;
      if (!API_KEY) {
        throw new ValidationError("ShipStation API key is not configured");
      }

      let allShipments = [];
      let page = 1;
      let hasMorePages = true;

      while (hasMorePages && page <= maxPages) {
        try {
          if (progressCallback) {
            progressCallback(`Fetching page ${page} of shipments...`);
          }

          const response = await this.shipstationApiService.makeApiRequestWithRetry(
            'https://api.shipstation.com/v2/shipments',
            {
              headers: {
                'API-Key': API_KEY,
                'Content-Type': 'application/json'
              },
              params: {
                store_id: storeId,
                ship_date_start: startDate,
                ship_date_end: endDate,
                page_size: pageSize,
                page: page
              }
            }
          );

          const shipments = response.data.shipments || [];
          allShipments = allShipments.concat(shipments);

          hasMorePages = shipments.length === pageSize;
          page++;

          if (progressCallback) {
            progressCallback(`Fetched ${allShipments.length} shipments so far...`);
          }

        } catch (error) {
          console.error(`Error fetching shipments page ${page}:`, error.message);
          break;
        }
      }

      return allShipments;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to fetch ShipStation shipments: ${error.message}`,
        { storeId, startDate, endDate }
      );
    }
  }

  /**
   * Fetch shipment label details
   * @param {string} labelId - Label ID
   * @returns {Promise<object>} - Label details
   */
  async fetchShipmentLabel(labelId) {
    try {
      const API_KEY = process.env.SHIPSTATION_API_KEY;
      if (!API_KEY) {
        throw new ValidationError("ShipStation API key is not configured");
      }

      const response = await this.shipstationApiService.makeApiRequestWithRetry(
        `https://api.shipstation.com/v2/labels/${labelId}`,
        {
          headers: {
            'API-Key': API_KEY,
            'Content-Type': 'application/json'
          }
        }
      );

      return response.data;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to fetch shipment label: ${error.message}`,
        { labelId }
      );
    }
  }

  /**
   * Process labels and extract shipping costs
   * @param {Array} labels - Array of labels
   * @param {string} shopDomain - Shop domain
   * @param {string} storeId - Store ID
   * @param {object} options - Processing options
   * @returns {Promise<object>} - Processing results
   */
  async processLabelsForShippingCosts(labels, shopDomain, storeId, options = {}) {
    try {
      const {
        progressCallback = null,
        batchSize = 50
      } = options;

      let totalShippingCost = 0;
      let processedLabels = 0;
      let skippedLabels = 0;

      // Process labels in batches
      for (let i = 0; i < labels.length; i += batchSize) {
        const batch = labels.slice(i, i + batchSize);

        for (const label of batch) {
          try {
            // Skip voided labels
            if (label.status && label.status.toLowerCase() === "voided") {
              skippedLabels++;
              continue;
            }

            // Extract shipping cost
            let shippingCost = 0;
            if (label.shipment_cost && label.shipment_cost.amount) {
              shippingCost = parseFloat(label.shipment_cost.amount);
            } else if (label.shipment_cost && typeof label.shipment_cost === 'number') {
              shippingCost = label.shipment_cost;
            }

            if (shippingCost > 0) {
              // Get label date
              let labelDate = null;
              if (label.created_at) {
                labelDate = new Date(label.created_at);
              } else if (label.createDate) {
                labelDate = new Date(label.createDate);
              }

              if (labelDate) {
                // Update shipping cost in database
                await this.updateShippingCostRecord(
                  shopDomain,
                  storeId,
                  shippingCost,
                  labelDate
                );

                totalShippingCost += shippingCost;
                processedLabels++;
              } else {
                skippedLabels++;
              }
            } else {
              skippedLabels++;
            }
          } catch (error) {
            console.error(`Error processing label:`, error);
            skippedLabels++;
          }
        }

        if (progressCallback) {
          const progress = Math.min(100, ((i + batchSize) / labels.length) * 100);
          progressCallback(`Processed ${processedLabels} labels (${progress.toFixed(1)}%)`);
        }
      }

      return {
        totalShippingCost,
        processedLabels,
        skippedLabels
      };
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to process labels: ${error.message}`,
        { shopDomain, storeId, labelCount: labels.length }
      );
    }
  }

  /**
   * Update shipping cost record in database
   * @param {string} shopDomain - Shop domain
   * @param {string} storeId - Store ID
   * @param {number} shippingCost - Shipping cost amount
   * @param {Date} labelDate - Label date
   * @returns {Promise<void>}
   */
  async updateShippingCostRecord(shopDomain, storeId, shippingCost, labelDate) {
    try {
      await this.prisma.shippingCost.upsert({
        where: {
          shop_storeId: {
            shop: shopDomain,
            storeId: storeId,
          },
        },
        update: {
          cost: {
            increment: shippingCost,
          },
          updatedAt: new Date(),
        },
        create: {
          shop: shopDomain,
          storeId: storeId,
          cost: shippingCost,
          date: labelDate,
        },
      });

      // Also update fulfillment costs
      await this.prisma.shippingCost.upsert({
        where: {
          shop_storeId: {
            shop: shopDomain,
            storeId: 'fulfillment',
          },
        },
        update: {
          cost: {
            increment: 1.50, // Default fulfillment cost
          },
          updatedAt: new Date(),
        },
        create: {
          shop: shopDomain,
          storeId: 'fulfillment',
          cost: 1.50,
          date: labelDate,
        },
      });
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to update shipping cost record: ${error.message}`,
        { shopDomain, storeId, shippingCost }
      );
    }
  }
}
