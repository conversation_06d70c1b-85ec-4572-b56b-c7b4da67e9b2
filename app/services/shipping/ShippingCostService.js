/**
 * Shipping Cost Service
 *
 * This service handles shipping cost calculations and updates
 * based on ShipStation data. It tracks costs by shop, store, month, and year.
 */

import { container } from '../../lib/container/ServiceContainer.server.js';
import { BusinessLogicError, ValidationError } from '../../lib/errors/AppError.js';
import { normalizeShopDomain } from '../../utils/id-normalization.server.js';

/**
 * Service for managing shipping costs and fulfillment processing
 */
export class ShippingCostService {
  constructor(dependencies = {}) {
    this.prisma = dependencies.prisma;
    this.initialized = false;

    if (!this.prisma) {
      this.initializeAsync();
    } else {
      this.initialized = true;
    }
  }

  async initializeAsync() {
    if (!this.initialized) {
      this.prisma = await container.resolve('database');
      this.initialized = true;
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      await this.initializeAsync();
    }
  }

  /**
   * Update shipping cost (core functionality from legacy)
   * @param {string} shop - Shop domain
   * @param {string} storeId - ShipStation store ID
   * @param {number} shippingAmount - Shipping cost amount
   * @param {number} markupPercentage - Markup percentage (default: 10)
   * @param {Date} labelDate - Date of the label (optional, defaults to current date)
   * @returns {Promise<object>} - Updated shipping cost record
   */
  async updateShippingCost(shop, storeId, shippingAmount, markupPercentage = 10, labelDate = null) {
    try {
      await this.ensureInitialized();

      // Validate inputs
      if (!shop || !storeId || isNaN(shippingAmount) || shippingAmount <= 0) {
        throw new ValidationError('Invalid shipping cost parameters');
      }

      const normalizedShop = normalizeShopDomain(shop);

      // Calculate markup amount
      const markupAmount = (shippingAmount * markupPercentage) / 100;
      const totalAmount = shippingAmount + markupAmount;

      // Use label date if provided, otherwise use current date
      const dateToUse = labelDate instanceof Date ? labelDate : new Date();
      const month = dateToUse.getMonth();
      const year = dateToUse.getFullYear();

      // Check if a shipping cost record already exists for this store
      const existingRecord = await this.prisma.shippingCost.findFirst({
        where: {
          shop: normalizedShop,
          storeId,
          month,
          year
        }
      });

      if (existingRecord) {
        // Update existing record
        const updatedRecord = await this.prisma.shippingCost.update({
          where: { id: existingRecord.id },
          data: {
            quantity: { increment: 1 }, // Increment quantity for each shipment
            shippingCost: { increment: shippingAmount },
            markupAmount: { increment: markupAmount },
            totalAmount: { increment: totalAmount },
            updatedAt: new Date()
          }
        });

        // Create a transaction record for this update
        await this.prisma.shippingTransaction.create({
          data: {
            shippingCostId: existingRecord.id,
            amount: totalAmount,
            shippingAmount: shippingAmount,
            markupAmount: markupAmount,
            description: `Shipping cost: $${shippingAmount.toFixed(2)} + ${markupPercentage}% markup: $${markupAmount.toFixed(2)}`,
            metadata: JSON.stringify({
              shippingAmount,
              markupAmount,
              markupPercentage,
              storeId
            })
          }
        });

        return updatedRecord;
      } else {
        // Create new record
        const newRecord = await this.prisma.shippingCost.create({
          data: {
            shop: normalizedShop,
            storeId,
            month,
            year,
            quantity: 1, // Start with 1 for the first shipment
            shippingCost: shippingAmount,
            markupAmount: markupAmount,
            totalAmount: totalAmount
          }
        });

        // Create a transaction record for this new record
        await this.prisma.shippingTransaction.create({
          data: {
            shippingCostId: newRecord.id,
            amount: totalAmount,
            shippingAmount: shippingAmount,
            markupAmount: markupAmount,
            description: `Shipping cost: $${shippingAmount.toFixed(2)} + ${markupPercentage}% markup: $${markupAmount.toFixed(2)}`,
            metadata: JSON.stringify({
              shippingAmount,
              markupAmount,
              markupPercentage,
              storeId
            })
          }
        });

        return newRecord;
      }
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to update shipping cost: ${error.message}`,
        { shop, storeId, shippingAmount, markupPercentage }
      );
    }
  }

  /**
   * Update fulfillment cost (from legacy functionality)
   * @param {string} shop - Shop domain
   * @param {number} fulfillmentCost - Fulfillment cost per order (default: 1.50)
   * @param {Date} labelDate - Date of the label (optional, defaults to current date)
   * @returns {Promise<object>} - Updated invoice balance record
   */
  async updateFulfillmentCost(shop, fulfillmentCost = 1.50, labelDate = null) {
    try {
      await this.ensureInitialized();

      // Validate inputs
      if (!shop || isNaN(fulfillmentCost) || fulfillmentCost <= 0) {
        throw new ValidationError('Invalid fulfillment cost parameters');
      }

      const normalizedShop = normalizeShopDomain(shop);

      // Use label date if provided, otherwise use current date
      const dateToUse = labelDate instanceof Date ? labelDate : new Date();
      const month = dateToUse.getMonth();
      const year = dateToUse.getFullYear();

      // Check if a fulfillment cost record already exists for this month
      const existingRecord = await this.prisma.invoiceBalance.findFirst({
        where: {
          shop: normalizedShop,
          category: 'Fulfillment Costs',
          month,
          year
        }
      });

      if (existingRecord) {
        // Update existing record
        const updatedRecord = await this.prisma.invoiceBalance.update({
          where: { id: existingRecord.id },
          data: {
            quantity: { increment: 1 }, // Increment quantity for each order
            balance: { increment: fulfillmentCost } // Add the fulfillment cost
          }
        });

        // Create a transaction record for this update
        await this.prisma.invoiceTransaction.create({
          data: {
            invoiceBalanceId: existingRecord.id,
            amount: fulfillmentCost,
            description: `Fulfillment cost for shipped order: $${fulfillmentCost.toFixed(2)}`,
            metadata: JSON.stringify({
              fulfillmentCost,
              date: new Date().toISOString()
            })
          }
        });

        return updatedRecord;
      } else {
        // Create new record
        const newRecord = await this.prisma.invoiceBalance.create({
          data: {
            shop: normalizedShop,
            category: 'Fulfillment Costs',
            month,
            year,
            quantity: 1, // Start with 1 for the first order
            balance: fulfillmentCost
          }
        });

        // Create a transaction record for this new record
        await this.prisma.invoiceTransaction.create({
          data: {
            invoiceBalanceId: newRecord.id,
            amount: fulfillmentCost,
            description: `Fulfillment cost for shipped order: $${fulfillmentCost.toFixed(2)}`,
            metadata: JSON.stringify({
              fulfillmentCost,
              date: new Date().toISOString()
            })
          }
        });

        return newRecord;
      }
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to update fulfillment cost: ${error.message}`,
        { shop, fulfillmentCost }
      );
    }
  }

  /**
   * Get shipping costs for a shop by month and year
   * @param {string} shop - Shop domain
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @returns {Promise<Array>} - Array of shipping costs
   */
  async getShippingCostsByMonth(shop, month, year) {
    try {
      await this.ensureInitialized();

      const normalizedShop = normalizeShopDomain(shop);

      const records = await this.prisma.shippingCost.findMany({
        where: {
          shop: normalizedShop,
          month,
          year
        },
        include: {
          transactions: {
            orderBy: { createdAt: 'desc' },
            take: 10
          }
        }
      });

      return records;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get shipping costs by month: ${error.message}`,
        { shop, month, year }
      );
    }
  }

  /**
   * Get all shipping costs for a shop
   * @param {string} shop - Shop domain
   * @returns {Promise<Array>} - Array of shipping costs
   */
  async getAllShippingCosts(shop) {
    try {
      await this.ensureInitialized();

      const normalizedShop = normalizeShopDomain(shop);

      const records = await this.prisma.shippingCost.findMany({
        where: { shop: normalizedShop },
        orderBy: [
          { year: 'desc' },
          { month: 'desc' }
        ],
        include: {
          transactions: {
            orderBy: { createdAt: 'desc' },
            take: 5
          }
        }
      });

      return records;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get all shipping costs: ${error.message}`,
        { shop }
      );
    }
  }

  /**
   * Get store mapping by shop domain
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<object|null>} - Store mapping or null
   */
  async getStoreMappingByShop(shopDomain) {
    try {
      await this.ensureInitialized();

      const normalizedShop = normalizeShopDomain(shopDomain);

      const mapping = await this.prisma.shipStationStoreMapping.findFirst({
        where: {
          shop: normalizedShop,
        },
      });

      return mapping;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get store mapping: ${error.message}`,
        { shopDomain }
      );
    }
  }
}
