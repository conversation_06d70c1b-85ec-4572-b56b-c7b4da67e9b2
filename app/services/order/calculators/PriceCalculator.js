/**
 * Price Calculator Service
 * 
 * Handles price calculations for order line items including
 * taxes, discounts, and total calculations.
 */

import { BusinessLogicError } from '../../../lib/errors/AppError.js';

/**
 * Service for calculating prices and totals for order line items
 */
export class PriceCalculator {
  constructor() {
    // Default tax rates and calculation settings
    this.defaultTaxRate = 0.0875; // 8.75% default tax rate
    this.roundingPrecision = 2;
  }

  /**
   * Calculate line item total including taxes and discounts
   * @param {object} lineItem - Order line item
   * @param {object} options - Calculation options
   * @returns {object} - Calculated totals
   */
  calculateLineItemTotal(lineItem, options = {}) {
    try {
      const {
        includeTax = true,
        taxRate = this.defaultTaxRate,
        discountAmount = 0,
        discountPercentage = 0,
      } = options;

      // Validate line item
      this.validateLineItem(lineItem);

      const quantity = parseInt(lineItem.quantity) || 1;
      const unitPrice = parseFloat(lineItem.price) || 0;

      // Calculate subtotal
      const subtotal = this.roundToDecimal(quantity * unitPrice);

      // Apply discounts
      let discountedSubtotal = subtotal;
      
      if (discountPercentage > 0) {
        const percentageDiscount = this.roundToDecimal(subtotal * (discountPercentage / 100));
        discountedSubtotal = this.roundToDecimal(subtotal - percentageDiscount);
      }
      
      if (discountAmount > 0) {
        discountedSubtotal = this.roundToDecimal(discountedSubtotal - discountAmount);
      }

      // Ensure discounted subtotal is not negative
      discountedSubtotal = Math.max(0, discountedSubtotal);

      // Calculate tax
      const taxAmount = includeTax 
        ? this.roundToDecimal(discountedSubtotal * taxRate)
        : 0;

      // Calculate total
      const total = this.roundToDecimal(discountedSubtotal + taxAmount);

      return {
        quantity,
        unitPrice,
        subtotal,
        discountAmount: this.roundToDecimal(subtotal - discountedSubtotal),
        discountedSubtotal,
        taxAmount,
        taxRate: includeTax ? taxRate : 0,
        total,
        calculations: {
          originalSubtotal: subtotal,
          appliedDiscounts: {
            percentage: discountPercentage,
            amount: discountAmount,
            totalDiscount: this.roundToDecimal(subtotal - discountedSubtotal),
          },
          taxCalculation: {
            taxableAmount: discountedSubtotal,
            taxRate: includeTax ? taxRate : 0,
            taxAmount,
          },
        },
      };

    } catch (error) {
      throw new BusinessLogicError(
        `Failed to calculate line item total: ${error.message}`,
        { lineItem, options, originalError: error.message }
      );
    }
  }

  /**
   * Calculate order total from multiple line items
   * @param {Array} lineItems - Array of line items
   * @param {object} options - Calculation options
   * @returns {object} - Order totals
   */
  calculateOrderTotal(lineItems, options = {}) {
    try {
      const {
        shippingCost = 0,
        orderLevelDiscount = 0,
        orderLevelDiscountPercentage = 0,
      } = options;

      if (!Array.isArray(lineItems) || lineItems.length === 0) {
        throw new Error('Line items array is required and cannot be empty');
      }

      // Calculate totals for each line item
      const lineItemTotals = lineItems.map(lineItem => 
        this.calculateLineItemTotal(lineItem, options)
      );

      // Sum up all line item totals
      const subtotal = this.roundToDecimal(
        lineItemTotals.reduce((sum, item) => sum + item.subtotal, 0)
      );

      const totalTax = this.roundToDecimal(
        lineItemTotals.reduce((sum, item) => sum + item.taxAmount, 0)
      );

      const totalDiscounts = this.roundToDecimal(
        lineItemTotals.reduce((sum, item) => sum + item.discountAmount, 0)
      );

      // Apply order-level discounts
      let orderSubtotal = subtotal - totalDiscounts;
      let orderLevelDiscountAmount = 0;

      if (orderLevelDiscountPercentage > 0) {
        orderLevelDiscountAmount += this.roundToDecimal(
          orderSubtotal * (orderLevelDiscountPercentage / 100)
        );
      }

      if (orderLevelDiscount > 0) {
        orderLevelDiscountAmount += orderLevelDiscount;
      }

      orderSubtotal = Math.max(0, orderSubtotal - orderLevelDiscountAmount);

      // Calculate final total
      const shippingAmount = parseFloat(shippingCost) || 0;
      const grandTotal = this.roundToDecimal(orderSubtotal + totalTax + shippingAmount);

      return {
        lineItemCount: lineItems.length,
        lineItemTotals,
        subtotal,
        totalDiscounts: this.roundToDecimal(totalDiscounts + orderLevelDiscountAmount),
        orderLevelDiscountAmount,
        taxableAmount: orderSubtotal,
        totalTax,
        shippingCost: shippingAmount,
        grandTotal,
        breakdown: {
          originalSubtotal: subtotal,
          lineItemDiscounts: totalDiscounts,
          orderLevelDiscounts: orderLevelDiscountAmount,
          finalSubtotal: orderSubtotal,
          tax: totalTax,
          shipping: shippingAmount,
          total: grandTotal,
        },
      };

    } catch (error) {
      throw new BusinessLogicError(
        `Failed to calculate order total: ${error.message}`,
        { lineItems, options, originalError: error.message }
      );
    }
  }

  /**
   * Calculate cost of goods and profit margins
   * @param {object} lineItem - Line item with cost information
   * @param {object} totals - Calculated totals from calculateLineItemTotal
   * @returns {object} - Cost and profit calculations
   */
  calculateCostAndProfit(lineItem, totals) {
    try {
      const costOfGoods = parseFloat(lineItem.cost_of_goods) || 0;
      const totalCost = this.roundToDecimal(costOfGoods * totals.quantity);
      const profit = this.roundToDecimal(totals.total - totalCost);
      const profitMargin = totals.total > 0 
        ? this.roundToDecimal((profit / totals.total) * 100)
        : 0;

      return {
        unitCost: costOfGoods,
        totalCost,
        revenue: totals.total,
        profit,
        profitMargin,
        profitPercentage: profitMargin,
      };

    } catch (error) {
      throw new BusinessLogicError(
        `Failed to calculate cost and profit: ${error.message}`,
        { lineItem, totals, originalError: error.message }
      );
    }
  }

  /**
   * Validate line item data
   * @param {object} lineItem - Line item to validate
   * @throws {Error} If validation fails
   */
  validateLineItem(lineItem) {
    if (!lineItem) {
      throw new Error('Line item is required');
    }

    if (!lineItem.price && lineItem.price !== 0) {
      throw new Error('Line item price is required');
    }

    if (!lineItem.quantity && lineItem.quantity !== 0) {
      throw new Error('Line item quantity is required');
    }

    const price = parseFloat(lineItem.price);
    const quantity = parseInt(lineItem.quantity);

    if (isNaN(price) || price < 0) {
      throw new Error('Line item price must be a non-negative number');
    }

    if (isNaN(quantity) || quantity < 0) {
      throw new Error('Line item quantity must be a non-negative integer');
    }
  }

  /**
   * Round number to specified decimal places
   * @param {number} value - Value to round
   * @param {number} precision - Decimal places (default: 2)
   * @returns {number} - Rounded value
   */
  roundToDecimal(value, precision = this.roundingPrecision) {
    const factor = Math.pow(10, precision);
    return Math.round(value * factor) / factor;
  }

  /**
   * Format currency value for display
   * @param {number} value - Numeric value
   * @param {string} currency - Currency code (default: USD)
   * @returns {string} - Formatted currency string
   */
  formatCurrency(value, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  }

  /**
   * Calculate percentage change between two values
   * @param {number} oldValue - Original value
   * @param {number} newValue - New value
   * @returns {number} - Percentage change
   */
  calculatePercentageChange(oldValue, newValue) {
    if (oldValue === 0) {
      return newValue === 0 ? 0 : 100;
    }
    return this.roundToDecimal(((newValue - oldValue) / oldValue) * 100);
  }

  /**
   * Get calculation summary for reporting
   * @param {object} calculations - Calculation results
   * @returns {object} - Summary information
   */
  getCalculationSummary(calculations) {
    return {
      timestamp: new Date().toISOString(),
      calculationType: 'price_calculation',
      summary: {
        subtotal: calculations.subtotal || 0,
        discounts: calculations.totalDiscounts || 0,
        tax: calculations.totalTax || 0,
        shipping: calculations.shippingCost || 0,
        total: calculations.grandTotal || calculations.total || 0,
      },
      metadata: {
        precision: this.roundingPrecision,
        calculatedAt: new Date().toISOString(),
      },
    };
  }
}
