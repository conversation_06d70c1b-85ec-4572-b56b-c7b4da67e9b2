/**
 * Product Processor Factory
 *
 * This factory creates appropriate processors for different product types
 * based on SKU patterns and product characteristics.
 */

import { ProductProcessor } from './ProductProcessor.js';
import { BusinessLogicError } from '../../../lib/errors/AppError.js';

/**
 * Factory for creating product processors
 */
export class ProcessorFactory {
  constructor(dependencies = {}) {
    this.dependencies = dependencies;
    this.processors = new Map();
    this.registerDefaultProcessors();
  }

  /**
   * Register default processors
   */
  registerDefaultProcessors() {
    // Use the ProductProcessor that contains all the actual legacy business logic
    this.processors.set('product', ProductProcessor);
  }

  /**
   * Register a custom processor
   * @param {string} type - Processor type
   * @param {class} ProcessorClass - Processor class
   */
  registerProcessor(type, ProcessorClass) {
    this.processors.set(type, ProcessorClass);
  }

  /**
   * Get appropriate processor for a line item
   * @param {object} lineItem - Shopify line item
   * @returns {object} - Processor instance
   */
  getProcessor(lineItem) {
    const processorType = this.determineProcessorType(lineItem);
    const ProcessorClass = this.processors.get(processorType);

    if (!ProcessorClass) {
      throw new BusinessLogicError(`No processor found for type: ${processorType}`);
    }

    return new ProcessorClass(this.dependencies);
  }

  /**
   * Determine processor type based on line item characteristics
   * @param {object} lineItem - Shopify line item
   * @returns {string} - Processor type
   */
  determineProcessorType(lineItem) {
    // Always use the ProductProcessor which contains all the legacy business logic
    return 'product';
  }

  /**
   * Get all registered processor types
   * @returns {Array<string>} - Array of processor types
   */
  getProcessorTypes() {
    return Array.from(this.processors.keys());
  }

  /**
   * Check if processor type exists
   * @param {string} type - Processor type
   * @returns {boolean} - True if processor exists
   */
  hasProcessor(type) {
    return this.processors.has(type);
  }

  /**
   * Create processor by type
   * @param {string} type - Processor type
   * @returns {object} - Processor instance
   */
  createProcessor(type) {
    const ProcessorClass = this.processors.get(type);

    if (!ProcessorClass) {
      throw new BusinessLogicError(`No processor found for type: ${type}`);
    }

    return new ProcessorClass(this.dependencies);
  }

  /**
   * Get processor statistics
   * @returns {object} - Processor usage statistics
   */
  getStatistics() {
    return {
      totalProcessors: this.processors.size,
      processorTypes: this.getProcessorTypes(),
    };
  }
}
