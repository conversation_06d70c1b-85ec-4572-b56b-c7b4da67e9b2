/**
 * Base Product Processor
 *
 * This abstract base class defines the interface and common functionality
 * for all product processors in the order processing system.
 */

import { container } from '../../../lib/container/ServiceContainer.server.js';
import { normalizeSKU, normalizeCurrencyToCents } from '../../../utils/id-normalization.server.js';
import { BusinessLogicError } from '../../../lib/errors/AppError.js';

/**
 * Abstract base class for product processors
 */
export class BaseProcessor {
  constructor(dependencies = {}) {
    this.prisma = dependencies.prisma || null;
    this.priceCalculator = null; // Will be injected by subclasses if needed
  }

  /**
   * Process a line item (abstract method)
   * @param {object} lineItem - Shopify line item
   * @param {object} orderData - Complete order data
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<object>} - Processed line item data
   */
  async processLineItem(lineItem, orderData, shopDomain) {
    throw new Error('processLineItem must be implemented by subclasses');
  }

  /**
   * Extract basic line item information
   * @param {object} lineItem - Shopify line item
   * @returns {object} - Basic line item data
   */
  extractBasicInfo(lineItem) {
    return {
      id: lineItem.id,
      productId: lineItem.product_id,
      variantId: lineItem.variant_id,
      sku: lineItem.sku || '',
      title: lineItem.title || '',
      variantTitle: lineItem.variant_title || '',
      quantity: lineItem.quantity || 1,
      price: 0, // Price comes from separate source
      totalDiscount: parseFloat(lineItem.total_discount || 0),
      fulfillmentService: lineItem.fulfillment_service || 'manual',
      requiresShipping: lineItem.requires_shipping !== false,
      taxable: lineItem.taxable !== false,
      grams: lineItem.grams || 0,
      vendor: lineItem.vendor || '',
      properties: lineItem.properties || [],
    };
  }

  /**
   * Calculate line item totals
   * @param {object} basicInfo - Basic line item information
   * @param {object} orderData - Complete order data
   * @returns {object} - Calculated totals
   */
  calculateTotals(basicInfo, orderData) {
    const subtotal = basicInfo.price * basicInfo.quantity;
    const discountAmount = basicInfo.totalDiscount;
    const netSubtotal = subtotal - discountAmount;

    // Calculate tax (proportional to line item)
    const orderSubtotal = parseFloat(orderData.subtotal_price || 0);
    const orderTax = parseFloat(orderData.total_tax || 0);
    const taxRate = orderSubtotal > 0 ? orderTax / orderSubtotal : 0;
    const tax = netSubtotal * taxRate;

    const total = netSubtotal + tax;

    return {
      subtotal: this.roundCurrency(subtotal),
      discountAmount: this.roundCurrency(discountAmount),
      netSubtotal: this.roundCurrency(netSubtotal),
      tax: this.roundCurrency(tax),
      total: this.roundCurrency(total),
      taxRate: this.roundCurrency(taxRate, 4),
    };
  }

  /**
   * Check if line item should be included in processing
   * @param {object} lineItem - Shopify line item
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<boolean>} - True if should be included
   */
  async shouldIncludeLineItem(lineItem, shopDomain) {
    // Check fulfillment service
    if (lineItem.fulfillment_service !== 'manual') {
      return false;
    }

    // Check for specific shop exclusions
    if (this.isExcludedForShop(lineItem, shopDomain)) {
      return false;
    }

    return true;
  }

  /**
   * Check if line item is excluded for specific shop
   * @param {object} lineItem - Shopify line item
   * @param {string} shopDomain - Shop domain
   * @returns {boolean} - True if excluded
   */
  isExcludedForShop(lineItem, shopDomain) {
    const sku = lineItem.sku || '';

    // For specific shops, include flag/patch items (*F prefix)
    const includeShops = [
      'american-trigger-pullers.myshopify.com',
      'phaselineco.myshopify.com',
    ];

    if (includeShops.includes(shopDomain) && sku.startsWith('*F')) {
      return false; // Don't exclude flag/patch items for these shops
    }

    // Default exclusion logic can be added here
    return false;
  }

  /**
   * Get fulfillment service information
   * @param {object} lineItem - Shopify line item
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<object>} - Fulfillment service info
   */
  async getFulfillmentServiceInfo(lineItem, shopDomain) {
    try {
      const variantId = lineItem.variant_id;

      const fulfillmentService = await this.prisma.variantFulfillmentService.findFirst({
        where: {
          variantId: String(variantId),
        },
        orderBy: {
          lastUpdated: 'desc',
        },
      });

      return {
        service: fulfillmentService?.fulfillmentService || lineItem.fulfillment_service || 'manual',
        isManual: (fulfillmentService?.fulfillmentService || lineItem.fulfillment_service) === 'manual',
        lastUpdated: fulfillmentService?.updatedAt || null,
      };
    } catch (error) {
      console.warn(`Failed to get fulfillment service for variant ${lineItem.variant_id}:`, error);
      return {
        service: lineItem.fulfillment_service || 'manual',
        isManual: (lineItem.fulfillment_service || 'manual') === 'manual',
        lastUpdated: null,
      };
    }
  }

  /**
   * Round currency amount to 2 decimal places
   * @param {number} amount - Amount to round
   * @param {number} decimals - Number of decimal places (default: 2)
   * @returns {number} - Rounded amount
   */
  roundCurrency(amount, decimals = 2) {
    return Math.round(amount * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  /**
   * Validate line item data
   * @param {object} lineItem - Shopify line item
   * @returns {void}
   * @throws {BusinessLogicError} - If validation fails
   */
  validateLineItem(lineItem) {
    if (!lineItem.id) {
      throw new BusinessLogicError('Line item missing ID');
    }

    if (!lineItem.product_id) {
      throw new BusinessLogicError('Line item missing product ID');
    }

    if (!lineItem.variant_id) {
      throw new BusinessLogicError('Line item missing variant ID');
    }

    if (typeof lineItem.quantity !== 'number' || lineItem.quantity <= 0) {
      throw new BusinessLogicError('Line item has invalid quantity');
    }

    // Price validation removed - price comes from separate source
  }

  /**
   * Create processing metadata
   * @param {object} lineItem - Shopify line item
   * @param {string} processorType - Type of processor used
   * @returns {object} - Processing metadata
   */
  createMetadata(lineItem, processorType) {
    return {
      processorType,
      processedAt: new Date(),
      lineItemId: lineItem.id,
      productId: lineItem.product_id,
      variantId: lineItem.variant_id,
      sku: lineItem.sku || '',
      fulfillmentService: lineItem.fulfillment_service || 'manual',
    };
  }

  /**
   * Log processing warning
   * @param {string} message - Warning message
   * @param {object} context - Additional context
   */
  logWarning(message, context = {}) {
    console.warn(`[${this.constructor.name}] ${message}`, context);
  }

  /**
   * Log processing info
   * @param {string} message - Info message
   * @param {object} context - Additional context
   */
  logInfo(message, context = {}) {
    console.log(`[${this.constructor.name}] ${message}`, context);
  }
}
