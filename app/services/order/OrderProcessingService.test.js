/**
 * Order Processing Service Tests
 *
 * Unit tests for the OrderProcessingService class
 */

import { jest } from '@jest/globals';
import { OrderProcessingService } from './OrderProcessingService.js';
import { TransactionManager } from '../../lib/database/TransactionManager.js';
import { ProcessorFactory } from './processors/ProcessorFactory.js';
import { FulfillmentValidator } from './validators/FulfillmentValidator.js';
import { BusinessLogicError, ValidationError } from '../../lib/errors/AppError.js';

// Mock dependencies
const mockPrisma = {
  processedWebhook: {
    findFirst: jest.fn(),
    create: jest.fn(),
    count: jest.fn(),
  },
  invoiceBalance: {
    upsert: jest.fn(),
  },
  unprocessable: {
    create: jest.fn(),
    count: jest.fn(),
  },
};

const mockShopifyClient = {
  setStoreContext: jest.fn(),
  getOrder: jest.fn(),
};

const mockTransactionManager = {
  executeTransaction: jest.fn(),
};

const mockProcessorFactory = {
  getProcessor: jest.fn(),
};

const mockFulfillmentValidator = {
  validateOrder: jest.fn(),
};

describe('OrderProcessingService', () => {
  let orderProcessingService;

  beforeEach(() => {
    jest.clearAllMocks();

    orderProcessingService = new OrderProcessingService({
      prisma: mockPrisma,
      shopifyClient: mockShopifyClient,
      transactionManager: mockTransactionManager,
      processorFactory: mockProcessorFactory,
      fulfillmentValidator: mockFulfillmentValidator,
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('processOrder', () => {
    const validOrderId = '12345';
    const validShopDomain = 'test-shop.myshopify.com';
    const mockOrderData = {
      id: '12345',
      name: '#1001',
      created_at: '2023-01-01T00:00:00Z',
      total_price: '100.00',
      financial_status: 'paid',
      line_items: [
        {
          id: '67890',
          product_id: '11111',
          variant_id: '22222',
          sku: 'TEST-SKU',
          title: 'Test Product',
          quantity: 1,
          price: '100.00',
          fulfillment_service: 'manual',
        },
      ],
    };

    it('should process a valid order successfully', async () => {
      // Setup mocks
      mockPrisma.processedWebhook.findFirst.mockResolvedValue(null);
      mockShopifyClient.getOrder.mockResolvedValue({ order: mockOrderData });
      mockFulfillmentValidator.validateOrder.mockResolvedValue({ isValid: true, errors: [] });

      const mockProcessor = {
        processLineItem: jest.fn().mockResolvedValue({
          id: '67890',
          sku: 'TEST-SKU',
          subtotal: 100,
          tax: 10,
          total: 110,
          costOfGoods: 50,
          included: true,
        }),
      };
      mockProcessorFactory.getProcessor.mockReturnValue(mockProcessor);

      mockTransactionManager.executeTransaction.mockImplementation(async (fn) => {
        return await fn(mockPrisma);
      });

      // Execute
      const result = await orderProcessingService.processOrder(validOrderId, validShopDomain);

      // Assertions
      expect(result.success).toBe(true);
      expect(result.orderId).toBe(validOrderId);
      expect(result.shopDomain).toBe(validShopDomain);
      expect(mockShopifyClient.setStoreContext).toHaveBeenCalledWith(validShopDomain);
      expect(mockShopifyClient.getOrder).toHaveBeenCalledWith(validOrderId, expect.any(Object));
      expect(mockFulfillmentValidator.validateOrder).toHaveBeenCalledWith(mockOrderData, validShopDomain);
      expect(mockTransactionManager.executeTransaction).toHaveBeenCalled();
    });

    it('should skip already processed orders', async () => {
      // Setup mocks
      const existingProcessing = {
        id: 'existing-id',
        orderId: validOrderId,
        shop: validShopDomain,
        processedAt: new Date(),
      };
      mockPrisma.processedWebhook.findFirst.mockResolvedValue(existingProcessing);

      // Execute
      const result = await orderProcessingService.processOrder(validOrderId, validShopDomain);

      // Assertions
      expect(result.success).toBe(true);
      expect(result.skipped).toBe(true);
      expect(result.reason).toBe('Order already processed');
      expect(mockShopifyClient.getOrder).not.toHaveBeenCalled();
    });

    it('should force reprocess when forceReprocess is true', async () => {
      // Setup mocks
      const existingProcessing = {
        id: 'existing-id',
        orderId: validOrderId,
        shop: validShopDomain,
        processedAt: new Date(),
      };
      mockPrisma.processedWebhook.findFirst.mockResolvedValue(existingProcessing);
      mockShopifyClient.getOrder.mockResolvedValue({ order: mockOrderData });
      mockFulfillmentValidator.validateOrder.mockResolvedValue({ isValid: true, errors: [] });

      const mockProcessor = {
        processLineItem: jest.fn().mockResolvedValue({
          id: '67890',
          sku: 'TEST-SKU',
          subtotal: 100,
          tax: 10,
          total: 110,
          costOfGoods: 50,
          included: true,
        }),
      };
      mockProcessorFactory.getProcessor.mockReturnValue(mockProcessor);

      mockTransactionManager.executeTransaction.mockImplementation(async (fn) => {
        return await fn(mockPrisma);
      });

      // Execute
      const result = await orderProcessingService.processOrder(validOrderId, validShopDomain, {
        forceReprocess: true,
      });

      // Assertions
      expect(result.success).toBe(true);
      expect(result.skipped).toBeUndefined();
      expect(mockShopifyClient.getOrder).toHaveBeenCalled();
    });

    it('should throw BusinessLogicError for invalid order ID', async () => {
      // Execute & Assert
      await expect(
        orderProcessingService.processOrder('', validShopDomain)
      ).rejects.toThrow(BusinessLogicError);
    });

    it('should throw BusinessLogicError for invalid shop domain', async () => {
      // Execute & Assert
      await expect(
        orderProcessingService.processOrder(validOrderId, '')
      ).rejects.toThrow(BusinessLogicError);
    });

    it('should throw BusinessLogicError when order not found', async () => {
      // Setup mocks
      mockPrisma.processedWebhook.findFirst.mockResolvedValue(null);
      mockShopifyClient.getOrder.mockResolvedValue({ order: null });

      // Execute & Assert
      await expect(
        orderProcessingService.processOrder(validOrderId, validShopDomain)
      ).rejects.toThrow(BusinessLogicError);
    });

    it('should throw BusinessLogicError when order validation fails', async () => {
      // Setup mocks
      mockPrisma.processedWebhook.findFirst.mockResolvedValue(null);
      mockShopifyClient.getOrder.mockResolvedValue({ order: mockOrderData });
      mockFulfillmentValidator.validateOrder.mockResolvedValue({
        isValid: false,
        errors: ['Order is not paid'],
      });

      // Execute & Assert
      await expect(
        orderProcessingService.processOrder(validOrderId, validShopDomain)
      ).rejects.toThrow(BusinessLogicError);
    });

    it('should handle Shopify API errors gracefully', async () => {
      // Setup mocks
      mockPrisma.processedWebhook.findFirst.mockResolvedValue(null);
      mockShopifyClient.getOrder.mockRejectedValue(new Error('API Error'));

      // Execute & Assert
      await expect(
        orderProcessingService.processOrder(validOrderId, validShopDomain)
      ).rejects.toThrow(BusinessLogicError);

      expect(mockPrisma.unprocessable.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          orderId: validOrderId,
          shop: validShopDomain,
          reason: expect.stringContaining('API Error'),
        }),
      });
    });
  });

  describe('processBatch', () => {
    const orders = [
      { orderId: '1', shopDomain: 'shop1.myshopify.com' },
      { orderId: '2', shopDomain: 'shop1.myshopify.com' },
      { orderId: '3', shopDomain: 'shop2.myshopify.com' },
    ];

    it('should process multiple orders successfully', async () => {
      // Setup mocks
      jest.spyOn(orderProcessingService, 'processOrder').mockResolvedValue({
        success: true,
        orderId: '1',
        shopDomain: 'shop1.myshopify.com',
      });

      // Execute
      const result = await orderProcessingService.processBatch(orders, {
        batchSize: 2,
        continueOnError: true,
      });

      // Assertions
      expect(result.totalProcessed).toBe(3);
      expect(result.successful).toBe(3);
      expect(result.failed).toBe(0);
      expect(orderProcessingService.processOrder).toHaveBeenCalledTimes(3);
    });

    it('should handle errors and continue processing when continueOnError is true', async () => {
      // Setup mocks
      jest.spyOn(orderProcessingService, 'processOrder')
        .mockResolvedValueOnce({ success: true, orderId: '1' })
        .mockRejectedValueOnce(new Error('Processing failed'))
        .mockResolvedValueOnce({ success: true, orderId: '3' });

      // Execute
      const result = await orderProcessingService.processBatch(orders, {
        continueOnError: true,
      });

      // Assertions
      expect(result.totalProcessed).toBe(3);
      expect(result.successful).toBe(2);
      expect(result.failed).toBe(1);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].error).toBe('Processing failed');
    });

    it('should stop processing on first error when continueOnError is false', async () => {
      // Setup mocks
      jest.spyOn(orderProcessingService, 'processOrder')
        .mockResolvedValueOnce({ success: true, orderId: '1' })
        .mockRejectedValueOnce(new Error('Processing failed'));

      // Execute & Assert
      await expect(
        orderProcessingService.processBatch(orders, {
          continueOnError: false,
        })
      ).rejects.toThrow('Processing failed');
    });

    it('should call progress callback with correct data', async () => {
      // Setup mocks
      const progressCallback = jest.fn();
      jest.spyOn(orderProcessingService, 'processOrder').mockResolvedValue({
        success: true,
      });

      // Execute
      await orderProcessingService.processBatch(orders, {
        batchSize: 2,
        progressCallback,
      });

      // Assertions
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          processed: expect.any(Number),
          total: 3,
          currentBatch: expect.any(Number),
          totalBatches: expect.any(Number),
        })
      );
    });
  });

  describe('getProcessingStatistics', () => {
    it('should return processing statistics for all shops', async () => {
      // Setup mocks
      mockPrisma.processedWebhook.count.mockResolvedValue(100);
      mockPrisma.unprocessable.count.mockResolvedValue(5);

      // Execute
      const stats = await orderProcessingService.getProcessingStatistics();

      // Assertions
      expect(stats.totalProcessed).toBe(100);
      expect(stats.totalFailed).toBe(5);
      expect(stats.successRate).toBeCloseTo(95.24, 2); // 100/(100+5) * 100
    });

    it('should return processing statistics for specific shop', async () => {
      // Setup mocks
      mockPrisma.processedWebhook.count.mockResolvedValue(50);
      mockPrisma.unprocessable.count.mockResolvedValue(2);

      // Execute
      const stats = await orderProcessingService.getProcessingStatistics('test-shop.myshopify.com');

      // Assertions
      expect(stats.totalProcessed).toBe(50);
      expect(stats.totalFailed).toBe(2);
      expect(stats.successRate).toBeCloseTo(96.15, 2); // 50/(50+2) * 100
      expect(mockPrisma.processedWebhook.count).toHaveBeenCalledWith({
        where: expect.objectContaining({
          shop: 'test-shop.myshopify.com',
        }),
      });
    });

    it('should handle date range filtering', async () => {
      // Setup mocks
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-31');
      mockPrisma.processedWebhook.count.mockResolvedValue(25);
      mockPrisma.unprocessable.count.mockResolvedValue(1);

      // Execute
      const stats = await orderProcessingService.getProcessingStatistics(null, {
        startDate,
        endDate,
      });

      // Assertions
      expect(stats.totalProcessed).toBe(25);
      expect(stats.totalFailed).toBe(1);
      expect(mockPrisma.processedWebhook.count).toHaveBeenCalledWith({
        where: expect.objectContaining({
          processedAt: {
            gte: startDate,
            lte: endDate,
          },
        }),
      });
    });
  });
});
