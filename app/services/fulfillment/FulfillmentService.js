/**
 * Fulfillment Service
 *
 * This service handles fulfillment service operations including
 * caching, batch processing, and API interactions for variant fulfillment services.
 */

import { container } from '../../lib/container/ServiceContainer.server.js';
import { BusinessLogicError, ValidationError } from '../../lib/errors/AppError.js';

/**
 * Service for fulfillment operations
 */
export class FulfillmentService {
  constructor(dependencies = {}) {
    this.prisma = dependencies.prisma || container.resolve('database');
    this.batchProcessingService = dependencies.batchProcessingService || container.resolve('batchProcessingService');

    // Cache configuration
    this.CACHE_DURATION_HOURS = 24;
    this.DEFAULT_BATCH_SIZE = 50;
    this.DEFAULT_CONCURRENCY = 2;
    this.DEFAULT_DELAY_BETWEEN_BATCHES = 1000;
  }

  /**
   * Check if a variant has manual fulfillment service
   * @param {object} admin - Shopify admin client
   * @param {string} variantId - Variant ID
   * @param {object} session - Shopify session
   * @returns {Promise<boolean>} - True if manual fulfillment
   */
  async hasManualFulfillmentService(admin, variantId, session) {
    try {
      // Check cache first
      const cachedValue = await this.getVariantFulfillmentService(variantId);

      if (cachedValue && this.isRecentEnough(cachedValue.lastUpdated)) {
        return cachedValue.fulfillmentService === "manual";
      }

      // Fetch from API
      const fulfillmentService = await this.fetchFulfillmentServiceFromAPI(admin, variantId, session);

      // Update cache
      await this.upsertVariantFulfillmentService(variantId, fulfillmentService);

      return fulfillmentService === "manual";
    } catch (error) {
      console.error(`Error checking fulfillment service for variant ${variantId}: ${error.message}`);
      // Default to true (manual) in case of error to avoid skipping items unnecessarily
      return true;
    }
  }

  /**
   * Batch check manual fulfillment services for multiple variants
   * @param {object} admin - Shopify admin client
   * @param {Array} variantIds - Array of variant IDs
   * @param {object} session - Shopify session
   * @param {object} options - Processing options
   * @returns {Promise<object>} - Map of variant IDs to fulfillment info
   */
  async batchCheckManualFulfillmentServices(admin, variantIds, session, options = {}) {
    try {
      const {
        batchSize = this.DEFAULT_BATCH_SIZE,
        concurrency = this.DEFAULT_CONCURRENCY,
        delayBetweenBatches = this.DEFAULT_DELAY_BETWEEN_BATCHES
      } = options;

      // Get cached values for all variants
      const cachedValues = await this.getBulkVariantFulfillmentServices(variantIds);

      // Create a map of variant IDs to results
      const results = {};
      const uncachedVariantIds = [];

      // Process cached values first
      for (const variantId of variantIds) {
        const cachedValue = cachedValues.find(cv => cv.variantId === variantId);

        if (cachedValue && this.isRecentEnough(cachedValue.lastUpdated)) {
          // Use cached value
          results[variantId] = {
            isManual: cachedValue.fulfillmentService === "manual",
            serviceName: cachedValue.fulfillmentService,
            fromCache: true
          };
        } else {
          // Need to fetch from API
          uncachedVariantIds.push(variantId);
        }
      }

      // Process uncached variants in batches if any exist
      if (uncachedVariantIds.length > 0) {
        const batchResults = await this.batchProcessingService.processBatches(
          uncachedVariantIds,
          async (variantId) => {
            try {
              const fulfillmentService = await this.fetchFulfillmentServiceFromAPI(admin, variantId, session);

              // Update the cache
              await this.upsertVariantFulfillmentService(variantId, fulfillmentService);

              return {
                variantId,
                isManual: fulfillmentService === "manual",
                serviceName: fulfillmentService,
                fromCache: false
              };
            } catch (error) {
              console.error(`Error checking fulfillment service for variant ${variantId}: ${error.message}`);
              return {
                variantId,
                isManual: true, // Default to manual in case of error
                serviceName: "manual",
                error: error.message,
                fromCache: false
              };
            }
          },
          {
            batchSize,
            concurrency,
            delayBetweenBatches
          }
        );

        // Add batch results to the main results
        for (const batchResult of batchResults) {
          if (batchResult.success) {
            const result = batchResult.result;
            results[result.variantId] = result;
          }
        }
      }

      return results;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to batch check fulfillment services: ${error.message}`,
        { variantCount: variantIds.length }
      );
    }
  }

  /**
   * Fetch fulfillment service from Shopify API
   * @param {object} admin - Shopify admin client
   * @param {string} variantId - Variant ID
   * @param {object} session - Shopify session
   * @returns {Promise<string>} - Fulfillment service name
   */
  async fetchFulfillmentServiceFromAPI(admin, variantId, session) {
    try {
      const response = await admin.graphql(
        `
        query GetVariantFulfillmentService($variantId: ID!) {
          productVariant(id: $variantId) {
            id
            inventoryItem {
              id
              tracked
              requiresShipping
            }
            product {
              id
              title
            }
          }
        }
        `,
        {
          variables: {
            variantId: variantId,
          },
        }
      );

      const responseData = await response.json();

      if (responseData.errors) {
        throw new Error(`GraphQL errors: ${JSON.stringify(responseData.errors)}`);
      }

      const variant = responseData.data?.productVariant;
      if (!variant) {
        throw new Error(`Variant ${variantId} not found`);
      }

      // For now, we'll assume manual fulfillment unless we can determine otherwise
      // This is a simplified implementation - you may need to enhance this based on your specific requirements
      const inventoryItem = variant.inventoryItem;

      if (inventoryItem && inventoryItem.requiresShipping) {
        return "manual"; // Requires shipping, likely manual fulfillment
      } else {
        return "manual"; // Default to manual
      }

    } catch (error) {
      console.error(`Error fetching fulfillment service from API for variant ${variantId}:`, error);
      return "manual"; // Default to manual on error
    }
  }

  /**
   * Get variant fulfillment service from cache
   * @param {string} variantId - Variant ID
   * @returns {Promise<object|null>} - Cached fulfillment service or null
   */
  async getVariantFulfillmentService(variantId) {
    try {
      const cached = await this.prisma.variantFulfillmentService.findUnique({
        where: { variantId: variantId },
      });

      return cached;
    } catch (error) {
      console.error(`Error getting cached fulfillment service for variant ${variantId}:`, error);
      return null;
    }
  }

  /**
   * Upsert variant fulfillment service in cache
   * @param {string} variantId - Variant ID
   * @param {string} fulfillmentService - Fulfillment service name
   * @returns {Promise<object>} - Updated record
   */
  async upsertVariantFulfillmentService(variantId, fulfillmentService) {
    try {
      const record = await this.prisma.variantFulfillmentService.upsert({
        where: { variantId: variantId },
        update: {
          fulfillmentService: fulfillmentService,
          lastUpdated: new Date(),
        },
        create: {
          variantId: variantId,
          fulfillmentService: fulfillmentService,
          lastUpdated: new Date(),
        },
      });

      return record;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to upsert variant fulfillment service: ${error.message}`,
        { variantId, fulfillmentService }
      );
    }
  }

  /**
   * Get bulk variant fulfillment services
   * @param {Array} variantIds - Array of variant IDs
   * @returns {Promise<Array>} - Array of cached fulfillment services
   */
  async getBulkVariantFulfillmentServices(variantIds) {
    try {
      const cached = await this.prisma.variantFulfillmentService.findMany({
        where: {
          variantId: {
            in: variantIds,
          },
        },
      });

      return cached;
    } catch (error) {
      console.error(`Error getting bulk fulfillment services:`, error);
      return [];
    }
  }

  /**
   * Check if cached value is recent enough
   * @param {Date} lastUpdated - Last updated timestamp
   * @returns {boolean} - True if recent enough
   */
  isRecentEnough(lastUpdated) {
    if (!lastUpdated) return false;

    const now = new Date();
    const cacheExpiry = new Date(lastUpdated.getTime() + (this.CACHE_DURATION_HOURS * 60 * 60 * 1000));

    return now < cacheExpiry;
  }

  /**
   * Get stale variant fulfillment services
   * @param {number} olderThanHours - Hours threshold for staleness
   * @returns {Promise<Array>} - Array of stale records
   */
  async getStaleVariantFulfillmentServices(olderThanHours = this.CACHE_DURATION_HOURS) {
    try {
      const cutoffDate = new Date(Date.now() - (olderThanHours * 60 * 60 * 1000));

      const staleRecords = await this.prisma.variantFulfillmentService.findMany({
        where: {
          lastUpdated: {
            lt: cutoffDate,
          },
        },
        orderBy: {
          lastUpdated: 'asc',
        },
      });

      return staleRecords;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get stale fulfillment services: ${error.message}`,
        { olderThanHours }
      );
    }
  }

  /**
   * Clean up old fulfillment service cache entries
   * @param {number} olderThanDays - Days threshold for cleanup
   * @returns {Promise<number>} - Number of deleted records
   */
  async cleanupOldFulfillmentServices(olderThanDays = 30) {
    try {
      const cutoffDate = new Date(Date.now() - (olderThanDays * 24 * 60 * 60 * 1000));

      const deleteResult = await this.prisma.variantFulfillmentService.deleteMany({
        where: {
          lastUpdated: {
            lt: cutoffDate,
          },
        },
      });

      console.log(`Cleaned up ${deleteResult.count} old fulfillment service cache entries`);
      return deleteResult.count;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to cleanup old fulfillment services: ${error.message}`,
        { olderThanDays }
      );
    }
  }
}
