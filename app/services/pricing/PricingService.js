/**
 * Pricing Service
 *
 * This service manages category-based pricing for invoice balance calculations.
 * The Price model only supports shop, category, and cost fields.
 */

import { container } from '../../lib/container/ServiceContainer.js';
import { BusinessLogicError, ValidationError } from '../../lib/errors/AppError.js';

/**
 * Service for managing category-based pricing
 */
export class PricingService {
  constructor(dependencies = {}) {
    this.dependencies = dependencies;
    this.prisma = null;
    this.initialized = false;
  }

  async initialize() {
    if (!this.initialized) {
      this.prisma = this.dependencies.prisma || await container.resolve('database');
      this.initialized = true;
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * Get price for a category
   * @param {string} category - Product category
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<object|null>} - Price object or null
   */
  async getPriceByCategory(category, shopDomain) {
    try {
      await this.ensureInitialized();
      const price = await this.prisma.price.findFirst({
        where: {
          shop: shopDomain,
          category: category,
        },
      });

      return price;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get price for category ${category}: ${error.message}`,
        { category, shopDomain }
      );
    }
  }

  /**
   * Set price for a category
   * @param {object} priceData - Price data
   * @returns {Promise<object>} - Created/updated price
   */
  async setPrice(priceData) {
    try {
      await this.ensureInitialized();
      const { shop, category, cost } = priceData;

      if (!shop || !category || cost === undefined) {
        throw new ValidationError('Shop, category, and cost are required');
      }

      const result = await this.prisma.price.upsert({
        where: {
          shop_category: {
            shop: shop,
            category: category,
          },
        },
        update: {
          cost: parseFloat(cost),
        },
        create: {
          shop: shop,
          category: category,
          cost: parseFloat(cost),
        },
      });

      return result;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to set price: ${error.message}`,
        { priceData }
      );
    }
  }

  /**
   * Get all prices for a shop
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<Array>} - Array of price objects
   */
  async getPricesForShop(shopDomain) {
    try {
      await this.ensureInitialized();
      const prices = await this.prisma.price.findMany({
        where: {
          shop: shopDomain,
        },
        orderBy: {
          category: 'asc',
        },
      });

      return prices;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get prices for shop ${shopDomain}: ${error.message}`,
        { shopDomain }
      );
    }
  }
}
