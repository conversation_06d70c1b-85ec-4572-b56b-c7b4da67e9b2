/**
 * Reconciliation Orchestrator
 *
 * This service orchestrates different reconciliation strategies and manages
 * the overall reconciliation process across multiple shops and data sources.
 */

import { container } from '../../lib/container/ServiceContainer.server.js';
import { TransactionManager } from '../../lib/database/TransactionManager.js';
import { ScheduledReconciliation } from './strategies/ScheduledReconciliation.js';
import { MissingOrderReconciliation } from './strategies/MissingOrderReconciliation.js';
import { ShipStationReconciliation } from './strategies/ShipStationReconciliation.js';
import { ReconciliationReporter } from './reporters/ReconciliationReporter.js';
import { BusinessLogicError, ValidationError } from '../../lib/errors/AppError.js';
import { generateIdHash } from '../../utils/id-normalization.server.js';

/**
 * Main reconciliation orchestration service
 */
export class ReconciliationOrchestrator {
  constructor(dependencies = {}) {
    this.prisma = dependencies.prisma;
    this.transactionManager = dependencies.transactionManager;
    this.reporter = dependencies.reporter || new ReconciliationReporter();
    this.initialized = false;

    // Initialize strategies
    this.strategies = new Map();
    this.registerDefaultStrategies();

    if (!this.prisma) {
      this.initializeAsync();
    } else {
      this.transactionManager = this.transactionManager || new TransactionManager(this.prisma);
      this.initialized = true;
    }
  }

  async initializeAsync() {
    if (!this.initialized) {
      this.prisma = await container.resolve('database');
      this.transactionManager = this.transactionManager || new TransactionManager(this.prisma);
      this.initialized = true;
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      await this.initializeAsync();
    }
  }

  /**
   * Register default reconciliation strategies
   */
  registerDefaultStrategies() {
    this.strategies.set('scheduled', ScheduledReconciliation);
    this.strategies.set('missing_orders', MissingOrderReconciliation);
    this.strategies.set('shipstation_sync', ShipStationReconciliation);
  }

  /**
   * Register a custom reconciliation strategy
   * @param {string} type - Strategy type
   * @param {class} StrategyClass - Strategy class
   */
  registerStrategy(type, StrategyClass) {
    this.strategies.set(type, StrategyClass);
  }

  /**
   * Start a reconciliation job
   * @param {string} type - Reconciliation type
   * @param {object} options - Reconciliation options
   * @returns {Promise<object>} - Job result
   */
  async startReconciliation(type, options = {}) {
    const {
      shopDomain = null,
      startDate = null,
      endDate = null,
      forceReprocess = false,
      dryRun = false,
      progressCallback = null,
    } = options;

    try {
      // Validate reconciliation type
      if (!this.strategies.has(type)) {
        throw new ValidationError(`Unknown reconciliation type: ${type}`);
      }

      // Check for overlapping reconciliation jobs
      await this.checkForOverlappingJobs(type, shopDomain, startDate, endDate);

      // Create reconciliation job record
      const jobId = await this.createReconciliationJob(type, options);

      // Initialize strategy
      const StrategyClass = this.strategies.get(type);
      const strategy = new StrategyClass({
        prisma: this.prisma,
        transactionManager: this.transactionManager,
        reporter: this.reporter,
      });

      // Start reconciliation
      const result = await this.executeReconciliation(strategy, jobId, options);

      // Update job status
      await this.updateJobStatus(jobId, 'completed', result);

      return {
        success: true,
        jobId,
        type,
        result,
      };

    } catch (error) {
      console.error(`Reconciliation failed for type ${type}:`, error);

      // Update job status if job was created
      if (error.jobId) {
        await this.updateJobStatus(error.jobId, 'failed', { error: error.message });
      }

      throw new BusinessLogicError(
        `Reconciliation failed: ${error.message}`,
        { type, options, originalError: error.message }
      );
    }
  }

  /**
   * Execute reconciliation with a strategy
   * @param {object} strategy - Reconciliation strategy instance
   * @param {string} jobId - Job ID
   * @param {object} options - Reconciliation options
   * @returns {Promise<object>} - Reconciliation result
   */
  async executeReconciliation(strategy, jobId, options) {
    const startTime = Date.now();

    try {
      // Update job status to running
      await this.updateJobStatus(jobId, 'running', { startedAt: new Date() });

      // Execute strategy
      const result = await strategy.execute(options);

      // Calculate execution time
      const executionTime = Date.now() - startTime;
      result.executionTime = executionTime;
      result.jobId = jobId;

      return result;

    } catch (error) {
      const executionTime = Date.now() - startTime;

      throw new BusinessLogicError(
        `Strategy execution failed: ${error.message}`,
        { jobId, executionTime, originalError: error.message }
      );
    }
  }

  /**
   * Check for overlapping reconciliation jobs
   * @param {string} type - Reconciliation type
   * @param {string} shopDomain - Shop domain
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Promise<void>}
   */
  async checkForOverlappingJobs(type, shopDomain, startDate, endDate) {
    try {
      const overlappingJobs = await this.prisma.reconciliationJob.findMany({
        where: {
          type,
          status: 'running',
          ...(shopDomain && { shop: shopDomain }),
          ...(startDate && endDate && {
            OR: [
              {
                startDate: {
                  lte: endDate,
                },
                endDate: {
                  gte: startDate,
                },
              },
            ],
          }),
        },
      });

      if (overlappingJobs.length > 0) {
        throw new BusinessLogicError(
          `Overlapping reconciliation job found: ${overlappingJobs[0].id}`,
          { overlappingJobId: overlappingJobs[0].id }
        );
      }
    } catch (error) {
      if (error instanceof BusinessLogicError) {
        throw error;
      }

      console.warn('Failed to check for overlapping jobs:', error);
      // Continue execution - don't fail reconciliation for this
    }
  }

  /**
   * Create reconciliation job record
   * @param {string} type - Reconciliation type
   * @param {object} options - Reconciliation options
   * @returns {Promise<string>} - Job ID
   */
  async createReconciliationJob(type, options) {
    const {
      shopDomain,
      startDate,
      endDate,
      forceReprocess = false,
      dryRun = false,
    } = options;

    const jobData = {
      type,
      status: 'pending',
      shop: shopDomain,
      startDate: startDate ? new Date(startDate) : null,
      endDate: endDate ? new Date(endDate) : null,
      options: JSON.stringify({
        forceReprocess,
        dryRun,
        ...options,
      }),
      createdAt: new Date(),
    };

    const job = await this.prisma.reconciliationJob.create({
      data: jobData,
    });

    return job.id;
  }

  /**
   * Update job status
   * @param {string} jobId - Job ID
   * @param {string} status - New status
   * @param {object} result - Job result data
   * @returns {Promise<void>}
   */
  async updateJobStatus(jobId, status, result = {}) {
    try {
      await this.prisma.reconciliationJob.update({
        where: { id: jobId },
        data: {
          status,
          result: JSON.stringify(result),
          updatedAt: new Date(),
          ...(status === 'completed' && { completedAt: new Date() }),
          ...(status === 'failed' && { failedAt: new Date() }),
        },
      });
    } catch (error) {
      console.error(`Failed to update job status for ${jobId}:`, error);
    }
  }

  /**
   * Get reconciliation job status
   * @param {string} jobId - Job ID
   * @returns {Promise<object>} - Job status
   */
  async getJobStatus(jobId) {
    try {
      const job = await this.prisma.reconciliationJob.findUnique({
        where: { id: jobId },
      });

      if (!job) {
        throw new ValidationError(`Job not found: ${jobId}`);
      }

      return {
        id: job.id,
        type: job.type,
        status: job.status,
        shop: job.shop,
        startDate: job.startDate,
        endDate: job.endDate,
        createdAt: job.createdAt,
        updatedAt: job.updatedAt,
        completedAt: job.completedAt,
        failedAt: job.failedAt,
        options: job.options ? JSON.parse(job.options) : {},
        result: job.result ? JSON.parse(job.result) : {},
      };
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get job status: ${error.message}`,
        { jobId }
      );
    }
  }

  /**
   * List reconciliation jobs
   * @param {object} filters - Filter options
   * @returns {Promise<Array>} - List of jobs
   */
  async listJobs(filters = {}) {
    const {
      type = null,
      status = null,
      shopDomain = null,
      limit = 50,
      offset = 0,
    } = filters;

    try {
      const jobs = await this.prisma.reconciliationJob.findMany({
        where: {
          ...(type && { type }),
          ...(status && { status }),
          ...(shopDomain && { shop: shopDomain }),
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
        skip: offset,
      });

      return jobs.map(job => ({
        id: job.id,
        type: job.type,
        status: job.status,
        shop: job.shop,
        startDate: job.startDate,
        endDate: job.endDate,
        createdAt: job.createdAt,
        updatedAt: job.updatedAt,
        completedAt: job.completedAt,
        failedAt: job.failedAt,
      }));
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to list jobs: ${error.message}`,
        { filters }
      );
    }
  }

  /**
   * Cancel a running reconciliation job
   * @param {string} jobId - Job ID
   * @returns {Promise<void>}
   */
  async cancelJob(jobId) {
    try {
      const job = await this.prisma.reconciliationJob.findUnique({
        where: { id: jobId },
      });

      if (!job) {
        throw new ValidationError(`Job not found: ${jobId}`);
      }

      if (job.status !== 'running') {
        throw new ValidationError(`Job is not running: ${job.status}`);
      }

      await this.updateJobStatus(jobId, 'cancelled', {
        cancelledAt: new Date(),
        reason: 'User requested cancellation',
      });

    } catch (error) {
      throw new BusinessLogicError(
        `Failed to cancel job: ${error.message}`,
        { jobId }
      );
    }
  }

  /**
   * Get reconciliation statistics
   * @param {object} filters - Filter options
   * @returns {Promise<object>} - Statistics
   */
  async getStatistics(filters = {}) {
    const { shopDomain = null, startDate = null, endDate = null } = filters;

    try {
      const whereClause = {
        ...(shopDomain && { shop: shopDomain }),
        ...(startDate && endDate && {
          createdAt: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        }),
      };

      const [totalJobs, completedJobs, failedJobs, runningJobs] = await Promise.all([
        this.prisma.reconciliationJob.count({ where: whereClause }),
        this.prisma.reconciliationJob.count({ where: { ...whereClause, status: 'completed' } }),
        this.prisma.reconciliationJob.count({ where: { ...whereClause, status: 'failed' } }),
        this.prisma.reconciliationJob.count({ where: { ...whereClause, status: 'running' } }),
      ]);

      const successRate = totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 0;

      return {
        totalJobs,
        completedJobs,
        failedJobs,
        runningJobs,
        pendingJobs: totalJobs - completedJobs - failedJobs - runningJobs,
        successRate: Math.round(successRate * 100) / 100,
        availableStrategies: Array.from(this.strategies.keys()),
      };
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get statistics: ${error.message}`,
        { filters }
      );
    }
  }
}
