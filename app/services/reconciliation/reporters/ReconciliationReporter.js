/**
 * Reconciliation Reporter
 * 
 * This service generates reports and tracks progress for reconciliation
 * operations across different strategies and data sources.
 */

import { container } from '../../../lib/container/ServiceContainer.js';
import { formatCurrency, formatDuration, formatPercentage } from '../../../utils/core/formatUtils.js';
import { BusinessLogicError } from '../../../lib/errors/AppError.js';

/**
 * Reporter for reconciliation operations
 */
export class ReconciliationReporter {
  constructor(dependencies = {}) {
    this.prisma = dependencies.prisma || container.resolve('database');
    this.progressCallbacks = new Map();
  }

  /**
   * Register a progress callback
   * @param {string} jobId - Job ID
   * @param {Function} callback - Progress callback function
   */
  registerProgressCallback(jobId, callback) {
    this.progressCallbacks.set(jobId, callback);
  }

  /**
   * Unregister a progress callback
   * @param {string} jobId - Job ID
   */
  unregisterProgressCallback(jobId) {
    this.progressCallbacks.delete(jobId);
  }

  /**
   * Report progress for a reconciliation operation
   * @param {string} strategyType - Strategy type
   * @param {string} phase - Current phase
   * @param {object} progress - Progress data
   */
  reportProgress(strategyType, phase, progress) {
    const progressReport = {
      strategyType,
      phase,
      timestamp: new Date(),
      ...progress,
    };

    // Log progress
    console.log(`[${strategyType.toUpperCase()}] ${phase}:`, progress);

    // Call registered callbacks
    for (const [jobId, callback] of this.progressCallbacks) {
      try {
        callback(progressReport);
      } catch (error) {
        console.error(`Progress callback failed for job ${jobId}:`, error);
      }
    }
  }

  /**
   * Generate reconciliation summary report
   * @param {object} results - Reconciliation results
   * @returns {object} - Summary report
   */
  generateSummaryReport(results) {
    const report = {
      title: 'Reconciliation Summary Report',
      generatedAt: new Date(),
      strategyType: results.strategyType,
      executionTime: results.executionTime ? formatDuration(results.executionTime) : 'N/A',
      overview: this.generateOverview(results),
      details: this.generateDetailedResults(results),
      recommendations: this.generateRecommendations(results),
    };

    return report;
  }

  /**
   * Generate overview section
   * @param {object} results - Reconciliation results
   * @returns {object} - Overview data
   */
  generateOverview(results) {
    const summary = results.summary || {};
    const details = results.details || {};

    const overview = {
      totalProcessed: summary.totalProcessed || 0,
      successful: summary.successful || 0,
      failed: summary.failed || 0,
      skipped: summary.skipped || 0,
      successRate: 0,
      errorRate: 0,
    };

    if (overview.totalProcessed > 0) {
      overview.successRate = (overview.successful / overview.totalProcessed) * 100;
      overview.errorRate = (overview.failed / overview.totalProcessed) * 100;
    }

    // Add strategy-specific metrics
    if (results.strategyType === 'scheduled') {
      overview.shopsProcessed = details.processedShops || 0;
      overview.totalShops = details.totalShops || 0;
      overview.ordersProcessed = details.processedOrders || 0;
    } else if (results.strategyType === 'missing_orders') {
      overview.shopsChecked = details.processedShops || 0;
      overview.missingOrdersFound = details.totalMissingOrders || 0;
    } else if (results.strategyType === 'shipstation_sync') {
      overview.storesProcessed = details.processedStores || 0;
      overview.shipmentsProcessed = details.processedShipments || 0;
      overview.totalShippingCost = formatCurrency(details.totalShippingCost || 0);
    }

    return overview;
  }

  /**
   * Generate detailed results section
   * @param {object} results - Reconciliation results
   * @returns {object} - Detailed results
   */
  generateDetailedResults(results) {
    const details = results.details || {};
    const detailedResults = {
      dateRange: details.dateRange,
      errors: results.summary?.errors || [],
      warnings: [],
    };

    // Add strategy-specific details
    if (results.strategyType === 'scheduled') {
      detailedResults.shopResults = this.formatShopResults(details.shopResults || []);
    } else if (results.strategyType === 'missing_orders') {
      detailedResults.missingOrdersByShop = this.formatMissingOrderResults(details.shopResults || []);
    } else if (results.strategyType === 'shipstation_sync') {
      detailedResults.storeResults = this.formatStoreResults(details.storeResults || []);
    }

    return detailedResults;
  }

  /**
   * Format shop results for scheduled reconciliation
   * @param {Array} shopResults - Shop results array
   * @returns {Array} - Formatted shop results
   */
  formatShopResults(shopResults) {
    return shopResults.map(result => ({
      shop: result.shop,
      ordersProcessed: result.processedOrders || 0,
      successful: result.successfulOrders || 0,
      failed: result.failedOrders || 0,
      duration: result.duration ? formatDuration(result.duration) : 'N/A',
      successRate: result.processedOrders > 0 
        ? formatPercentage((result.successfulOrders || 0) / result.processedOrders)
        : '0%',
      errors: result.errors || [],
    }));
  }

  /**
   * Format missing order results
   * @param {Array} shopResults - Shop results array
   * @returns {Array} - Formatted missing order results
   */
  formatMissingOrderResults(shopResults) {
    return shopResults.map(result => ({
      shop: result.shop,
      missingOrdersFound: result.missingOrders?.length || 0,
      ordersProcessed: result.processedOrders || 0,
      successful: result.successfulOrders || 0,
      failed: result.failedOrders || 0,
      duration: result.duration ? formatDuration(result.duration) : 'N/A',
      missingOrders: result.missingOrders?.slice(0, 10) || [], // Show first 10
      errors: result.errors || [],
    }));
  }

  /**
   * Format store results for ShipStation reconciliation
   * @param {Array} storeResults - Store results array
   * @returns {Array} - Formatted store results
   */
  formatStoreResults(storeResults) {
    return storeResults.map(result => ({
      storeId: result.storeId,
      storeName: result.storeName,
      shopDomain: result.shopDomain,
      shipmentsProcessed: result.processedShipments || 0,
      successful: result.successfulShipments || 0,
      failed: result.failedShipments || 0,
      totalShippingCost: formatCurrency(result.totalShippingCost || 0),
      duration: result.duration ? formatDuration(result.duration) : 'N/A',
      successRate: result.processedShipments > 0 
        ? formatPercentage((result.successfulShipments || 0) / result.processedShipments)
        : '0%',
      errors: result.errors || [],
    }));
  }

  /**
   * Generate recommendations based on results
   * @param {object} results - Reconciliation results
   * @returns {Array} - Array of recommendations
   */
  generateRecommendations(results) {
    const recommendations = [];
    const summary = results.summary || {};
    const details = results.details || {};

    // General recommendations
    if (summary.failed > 0) {
      recommendations.push({
        type: 'error',
        message: `${summary.failed} items failed processing. Review error logs and consider reprocessing.`,
        priority: 'high',
      });
    }

    if (summary.successful > 0 && summary.failed === 0) {
      recommendations.push({
        type: 'success',
        message: 'All items processed successfully. No action required.',
        priority: 'low',
      });
    }

    // Strategy-specific recommendations
    if (results.strategyType === 'missing_orders' && details.totalMissingOrders > 0) {
      recommendations.push({
        type: 'warning',
        message: `Found ${details.totalMissingOrders} missing orders. Consider investigating why these orders were not processed initially.`,
        priority: 'medium',
      });
    }

    if (results.strategyType === 'shipstation_sync' && details.totalShippingCost > 10000) {
      recommendations.push({
        type: 'info',
        message: `High shipping costs detected (${formatCurrency(details.totalShippingCost)}). Review for accuracy.`,
        priority: 'medium',
      });
    }

    // Performance recommendations
    if (results.executionTime > 300000) { // 5 minutes
      recommendations.push({
        type: 'performance',
        message: 'Reconciliation took longer than expected. Consider reducing date range or batch size.',
        priority: 'low',
      });
    }

    return recommendations;
  }

  /**
   * Generate detailed error report
   * @param {Array} errors - Array of errors
   * @returns {object} - Error report
   */
  generateErrorReport(errors) {
    const errorReport = {
      title: 'Reconciliation Error Report',
      generatedAt: new Date(),
      totalErrors: errors.length,
      errorsByType: {},
      errorsByShop: {},
      detailedErrors: [],
    };

    // Categorize errors
    errors.forEach(error => {
      // By type
      const errorType = this.categorizeError(error);
      errorReport.errorsByType[errorType] = (errorReport.errorsByType[errorType] || 0) + 1;

      // By shop
      if (error.shop) {
        errorReport.errorsByShop[error.shop] = (errorReport.errorsByShop[error.shop] || 0) + 1;
      }

      // Add to detailed errors
      errorReport.detailedErrors.push({
        type: errorType,
        shop: error.shop || 'N/A',
        item: error.item || error.orderId || error.storeId || 'N/A',
        message: error.error || error.message,
        timestamp: error.timestamp || new Date(),
      });
    });

    return errorReport;
  }

  /**
   * Categorize error by type
   * @param {object} error - Error object
   * @returns {string} - Error category
   */
  categorizeError(error) {
    const message = (error.error || error.message || '').toLowerCase();

    if (message.includes('rate limit') || message.includes('429')) {
      return 'rate_limit';
    } else if (message.includes('authentication') || message.includes('401')) {
      return 'authentication';
    } else if (message.includes('not found') || message.includes('404')) {
      return 'not_found';
    } else if (message.includes('validation') || message.includes('invalid')) {
      return 'validation';
    } else if (message.includes('network') || message.includes('timeout')) {
      return 'network';
    } else {
      return 'unknown';
    }
  }

  /**
   * Save report to database
   * @param {string} jobId - Job ID
   * @param {object} report - Report data
   * @returns {Promise<void>}
   */
  async saveReport(jobId, report) {
    try {
      await this.prisma.reconciliationReport.create({
        data: {
          jobId,
          reportType: 'summary',
          reportData: JSON.stringify(report),
          generatedAt: new Date(),
        },
      });
    } catch (error) {
      console.error(`Failed to save report for job ${jobId}:`, error);
    }
  }

  /**
   * Get saved reports
   * @param {object} filters - Filter options
   * @returns {Promise<Array>} - Array of reports
   */
  async getReports(filters = {}) {
    const { jobId, reportType, limit = 50, offset = 0 } = filters;

    try {
      const reports = await this.prisma.reconciliationReport.findMany({
        where: {
          ...(jobId && { jobId }),
          ...(reportType && { reportType }),
        },
        orderBy: {
          generatedAt: 'desc',
        },
        take: limit,
        skip: offset,
      });

      return reports.map(report => ({
        id: report.id,
        jobId: report.jobId,
        reportType: report.reportType,
        generatedAt: report.generatedAt,
        reportData: JSON.parse(report.reportData),
      }));
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get reports: ${error.message}`,
        { filters }
      );
    }
  }
}
