/**
 * Missing Order Reconciliation Strategy
 *
 * This strategy identifies and processes orders that exist in Shopify
 * but are missing from the local database or haven't been processed.
 */

import { BaseReconciliationStrategy } from './BaseReconciliationStrategy.js';
import { OrderProcessingService } from '../../order/OrderProcessingService.js';
import { container } from '../../../lib/container/ServiceContainer.server.js';
import { BusinessLogicError } from '../../../lib/errors/AppError.js';

/**
 * Missing order reconciliation strategy implementation
 */
export class MissingOrderReconciliation extends BaseReconciliationStrategy {
  constructor(dependencies = {}) {
    super(dependencies);
    this.strategyType = 'missing_orders';
    this.orderProcessingService = dependencies.orderProcessingService || new OrderProcessingService();
    this.shopifyClient = dependencies.shopifyClient || container.resolve('shopifyClient');
  }

  /**
   * Execute missing order reconciliation
   * @param {object} options - Reconciliation options
   * @returns {Promise<object>} - Reconciliation result
   */
  async execute(options = {}) {
    try {
      this.logInfo('Starting missing order reconciliation', options);

      // Validate options
      this.validateOptions(options);

      // Get date range
      const dateRange = this.getDateRange(options);
      this.logInfo('Using date range', dateRange);

      // Get shops to reconcile
      const shops = await this.getShopsToReconcile(options.shopDomain);
      this.logInfo(`Checking ${shops.length} shops for missing orders`);

      // Initialize results
      const results = {
        totalShops: shops.length,
        processedShops: 0,
        totalMissingOrders: 0,
        processedOrders: 0,
        successfulOrders: 0,
        failedOrders: 0,
        shopResults: [],
        errors: [],
        dateRange,
      };

      // Process each shop
      for (const shop of shops) {
        try {
          this.reportProgress('shop_analysis', {
            currentShop: shop,
            processedShops: results.processedShops,
            totalShops: results.totalShops,
          });

          const shopResult = await this.findAndProcessMissingOrders(shop, dateRange, options);
          results.shopResults.push(shopResult);

          results.totalMissingOrders += shopResult.missingOrders.length;
          results.processedOrders += shopResult.processedOrders;
          results.successfulOrders += shopResult.successfulOrders;
          results.failedOrders += shopResult.failedOrders;
          results.processedShops++;

        } catch (error) {
          this.logError(`Failed to check missing orders for shop ${shop}`, { error: error.message });
          results.errors.push({
            shop,
            error: error.message,
          });
          results.processedShops++;
        }
      }

      // Generate final summary
      const summary = this.createSummary({
        totalProcessed: results.processedOrders,
        successful: results.successfulOrders,
        failed: results.failedOrders,
        errors: results.errors,
        details: results,
      });

      this.logInfo('Missing order reconciliation completed', summary);
      return summary;

    } catch (error) {
      this.logError('Missing order reconciliation failed', { error: error.message });
      return this.handleError(error, { options });
    }
  }

  /**
   * Get shops to reconcile
   * @param {string} specificShop - Specific shop domain (optional)
   * @returns {Promise<Array<string>>} - Array of shop domains
   */
  async getShopsToReconcile(specificShop = null) {
    if (specificShop) {
      const isSupported = await this.isShopSupported(specificShop);
      if (!isSupported) {
        throw new BusinessLogicError(`Shop not supported: ${specificShop}`);
      }
      return [specificShop];
    }

    return this.getSupportedShops();
  }

  /**
   * Find and process missing orders for a shop
   * @param {string} shopDomain - Shop domain
   * @param {object} dateRange - Date range for reconciliation
   * @param {object} options - Reconciliation options
   * @returns {Promise<object>} - Shop reconciliation result
   */
  async findAndProcessMissingOrders(shopDomain, dateRange, options) {
    this.logInfo(`Checking for missing orders in shop: ${shopDomain}`);

    const shopResult = {
      shop: shopDomain,
      missingOrders: [],
      processedOrders: 0,
      successfulOrders: 0,
      failedOrders: 0,
      errors: [],
      startTime: new Date(),
    };

    try {
      // Set shop context for Shopify API
      this.shopifyClient.setStoreContext(shopDomain);

      // Get orders from Shopify
      const shopifyOrders = await this.getShopifyOrders(shopDomain, dateRange);
      this.logInfo(`Found ${shopifyOrders.length} orders in Shopify for ${shopDomain}`);

      // Get processed orders from database
      const processedOrderIds = await this.getProcessedOrderIds(shopDomain, dateRange);
      this.logInfo(`Found ${processedOrderIds.size} processed orders in database for ${shopDomain}`);

      // Find missing orders
      const missingOrders = shopifyOrders.filter(order =>
        !processedOrderIds.has(order.id.toString())
      );

      shopResult.missingOrders = missingOrders.map(order => ({
        orderId: order.id,
        name: order.name,
        createdAt: order.created_at,
        totalPrice: order.total_price,
        financialStatus: order.financial_status,
      }));

      this.logInfo(`Found ${missingOrders.length} missing orders for ${shopDomain}`);

      if (missingOrders.length === 0) {
        shopResult.endTime = new Date();
        return shopResult;
      }

      // Process missing orders if not dry run
      if (!options.dryRun) {
        const batchResults = await this.orderProcessingService.processBatch(
          missingOrders.map(order => ({ orderId: order.id, shopDomain })),
          {
            batchSize: options.batchSize || 5, // Smaller batch size for missing orders
            continueOnError: true,
            forceReprocess: true, // Force reprocess since these are missing
            progressCallback: (progress) => {
              this.reportProgress('missing_order_processing', {
                shop: shopDomain,
                ...progress,
              });
            },
          }
        );

        shopResult.processedOrders = batchResults.totalProcessed;
        shopResult.successfulOrders = batchResults.successful;
        shopResult.failedOrders = batchResults.failed;
        shopResult.errors = batchResults.errors;
      }

      shopResult.endTime = new Date();
      shopResult.duration = shopResult.endTime - shopResult.startTime;

      this.logInfo(`Completed missing order check for ${shopDomain}`, {
        missing: missingOrders.length,
        processed: shopResult.processedOrders,
        successful: shopResult.successfulOrders,
        failed: shopResult.failedOrders,
      });

      return shopResult;

    } catch (error) {
      shopResult.endTime = new Date();
      shopResult.error = error.message;

      this.logError(`Missing order check failed for ${shopDomain}`, { error: error.message });
      throw error;
    }
  }

  /**
   * Get orders from Shopify for the date range
   * @param {string} shopDomain - Shop domain
   * @param {object} dateRange - Date range
   * @returns {Promise<Array>} - Array of Shopify orders
   */
  async getShopifyOrders(shopDomain, dateRange) {
    try {
      const orders = [];
      let hasNextPage = true;
      let pageInfo = null;

      while (hasNextPage) {
        const params = {
          status: 'any',
          created_at_min: dateRange.startDate.toISOString(),
          created_at_max: dateRange.endDate.toISOString(),
          limit: 250, // Maximum allowed by Shopify
          fields: 'id,name,created_at,updated_at,total_price,financial_status,fulfillment_status',
          ...(pageInfo && { page_info: pageInfo }),
        };

        const response = await this.shopifyClient.getOrders(params);

        if (response.orders && response.orders.length > 0) {
          orders.push(...response.orders);

          // Check for pagination
          const linkHeader = response.headers?.link;
          if (linkHeader && linkHeader.includes('rel="next"')) {
            // Extract page info from link header
            const nextMatch = linkHeader.match(/<[^>]*[?&]page_info=([^&>]+)[^>]*>;\s*rel="next"/);
            pageInfo = nextMatch ? nextMatch[1] : null;
            hasNextPage = !!pageInfo;
          } else {
            hasNextPage = false;
          }
        } else {
          hasNextPage = false;
        }

        // Add delay to respect rate limits
        await this.sleep(500);
      }

      return orders;

    } catch (error) {
      this.logError(`Failed to get Shopify orders for ${shopDomain}`, { error: error.message });
      throw new BusinessLogicError(`Failed to fetch Shopify orders: ${error.message}`);
    }
  }

  /**
   * Get processed order IDs from database
   * @param {string} shopDomain - Shop domain
   * @param {object} dateRange - Date range
   * @returns {Promise<Set>} - Set of processed order IDs
   */
  async getProcessedOrderIds(shopDomain, dateRange) {
    try {
      const processedWebhooks = await this.prisma.processedWebhook.findMany({
        where: {
          shop: shopDomain,
          type: 'order_processing',
          processedAt: {
            gte: dateRange.startDate,
            lte: dateRange.endDate,
          },
        },
        select: {
          orderId: true,
        },
      });

      return new Set(processedWebhooks.map(pw => pw.orderId));

    } catch (error) {
      this.logWarning(`Failed to get processed order IDs for ${shopDomain}`, { error: error.message });
      return new Set();
    }
  }

  /**
   * Validate missing order reconciliation options
   * @param {object} options - Options to validate
   * @returns {void}
   */
  validateOptions(options) {
    super.validateOptions(options);

    // Additional validation for missing order reconciliation
    if (options.batchSize && (options.batchSize < 1 || options.batchSize > 50)) {
      throw new BusinessLogicError('Batch size for missing orders must be between 1 and 50');
    }
  }

  /**
   * Get strategy metadata
   * @returns {object} - Strategy metadata
   */
  getMetadata() {
    return {
      ...super.getMetadata(),
      type: this.strategyType,
      description: 'Find and process orders missing from local database',
      capabilities: [
        ...super.getMetadata().capabilities,
        'Shopify API integration',
        'Missing order detection',
        'Pagination handling',
        'Rate limit compliance',
      ],
      options: {
        shopDomain: 'Specific shop to check (optional)',
        startDate: 'Start date for order search (optional)',
        endDate: 'End date for order search (optional)',
        batchSize: 'Number of orders to process in each batch (1-50)',
        dryRun: 'Find missing orders without processing them',
      },
      limitations: [
        'Requires Shopify API access',
        'Subject to Shopify rate limits',
        'Large date ranges may take significant time',
      ],
    };
  }
}
