/**
 * Scheduled Reconciliation Strategy
 * 
 * This strategy handles regular scheduled reconciliation of orders
 * and invoice balances across all supported shops.
 */

import { BaseReconciliationStrategy } from './BaseReconciliationStrategy.js';
import { OrderProcessingService } from '../../order/OrderProcessingService.js';
import { BusinessLogicError } from '../../../lib/errors/AppError.js';
import { getFirstDayOfPreviousMonth, getLastDayOfPreviousMonth } from '../../../utils/core/dateUtils.js';

/**
 * Scheduled reconciliation strategy implementation
 */
export class ScheduledReconciliation extends BaseReconciliationStrategy {
  constructor(dependencies = {}) {
    super(dependencies);
    this.strategyType = 'scheduled';
    this.orderProcessingService = dependencies.orderProcessingService || new OrderProcessingService();
  }

  /**
   * Execute scheduled reconciliation
   * @param {object} options - Reconciliation options
   * @returns {Promise<object>} - Reconciliation result
   */
  async execute(options = {}) {
    try {
      this.logInfo('Starting scheduled reconciliation', options);
      
      // Validate options
      this.validateOptions(options);

      // Get date range
      const dateRange = this.getDateRange(options);
      this.logInfo('Using date range', dateRange);

      // Get shops to reconcile
      const shops = await this.getShopsToReconcile(options.shopDomain);
      this.logInfo(`Reconciling ${shops.length} shops`);

      // Initialize results
      const results = {
        totalShops: shops.length,
        processedShops: 0,
        totalOrders: 0,
        processedOrders: 0,
        successfulOrders: 0,
        failedOrders: 0,
        shopResults: [],
        errors: [],
        dateRange,
      };

      // Process each shop
      for (const shop of shops) {
        try {
          this.reportProgress('shop_processing', {
            currentShop: shop,
            processedShops: results.processedShops,
            totalShops: results.totalShops,
          });

          const shopResult = await this.reconcileShop(shop, dateRange, options);
          results.shopResults.push(shopResult);
          
          results.totalOrders += shopResult.totalOrders;
          results.processedOrders += shopResult.processedOrders;
          results.successfulOrders += shopResult.successfulOrders;
          results.failedOrders += shopResult.failedOrders;
          results.processedShops++;

        } catch (error) {
          this.logError(`Failed to reconcile shop ${shop}`, { error: error.message });
          results.errors.push({
            shop,
            error: error.message,
          });
          results.processedShops++;
        }
      }

      // Generate final summary
      const summary = this.createSummary({
        totalProcessed: results.processedOrders,
        successful: results.successfulOrders,
        failed: results.failedOrders,
        errors: results.errors,
        details: results,
      });

      this.logInfo('Scheduled reconciliation completed', summary);
      return summary;

    } catch (error) {
      this.logError('Scheduled reconciliation failed', { error: error.message });
      return this.handleError(error, { options });
    }
  }

  /**
   * Get shops to reconcile
   * @param {string} specificShop - Specific shop domain (optional)
   * @returns {Promise<Array<string>>} - Array of shop domains
   */
  async getShopsToReconcile(specificShop = null) {
    if (specificShop) {
      const isSupported = await this.isShopSupported(specificShop);
      if (!isSupported) {
        throw new BusinessLogicError(`Shop not supported: ${specificShop}`);
      }
      return [specificShop];
    }

    return this.getSupportedShops();
  }

  /**
   * Reconcile a single shop
   * @param {string} shopDomain - Shop domain
   * @param {object} dateRange - Date range for reconciliation
   * @param {object} options - Reconciliation options
   * @returns {Promise<object>} - Shop reconciliation result
   */
  async reconcileShop(shopDomain, dateRange, options) {
    this.logInfo(`Starting reconciliation for shop: ${shopDomain}`);

    const shopResult = {
      shop: shopDomain,
      totalOrders: 0,
      processedOrders: 0,
      successfulOrders: 0,
      failedOrders: 0,
      skippedOrders: 0,
      errors: [],
      startTime: new Date(),
    };

    try {
      // Get orders for the date range
      const orders = await this.getOrdersForReconciliation(shopDomain, dateRange);
      shopResult.totalOrders = orders.length;

      this.logInfo(`Found ${orders.length} orders for ${shopDomain}`);

      if (orders.length === 0) {
        shopResult.endTime = new Date();
        return shopResult;
      }

      // Process orders in batches
      const batchResults = await this.orderProcessingService.processBatch(
        orders.map(order => ({ orderId: order.orderId, shopDomain })),
        {
          batchSize: options.batchSize || 10,
          continueOnError: true,
          forceReprocess: options.forceReprocess || false,
          dryRun: options.dryRun || false,
          progressCallback: (progress) => {
            this.reportProgress('order_processing', {
              shop: shopDomain,
              ...progress,
            });
          },
        }
      );

      // Aggregate results
      shopResult.processedOrders = batchResults.totalProcessed;
      shopResult.successfulOrders = batchResults.successful;
      shopResult.failedOrders = batchResults.failed;
      shopResult.errors = batchResults.errors;

      shopResult.endTime = new Date();
      shopResult.duration = shopResult.endTime - shopResult.startTime;

      this.logInfo(`Completed reconciliation for ${shopDomain}`, {
        processed: shopResult.processedOrders,
        successful: shopResult.successfulOrders,
        failed: shopResult.failedOrders,
      });

      return shopResult;

    } catch (error) {
      shopResult.endTime = new Date();
      shopResult.error = error.message;
      
      this.logError(`Shop reconciliation failed for ${shopDomain}`, { error: error.message });
      throw error;
    }
  }

  /**
   * Get orders for reconciliation
   * @param {string} shopDomain - Shop domain
   * @param {object} dateRange - Date range
   * @returns {Promise<Array>} - Array of orders
   */
  async getOrdersForReconciliation(shopDomain, dateRange) {
    try {
      // Get orders from database that haven't been processed or need reprocessing
      const orders = await this.prisma.order.findMany({
        where: {
          shop: shopDomain,
          createdAt: {
            gte: dateRange.startDate,
            lte: dateRange.endDate,
          },
          // Add conditions for orders that need processing
          OR: [
            {
              // Orders not yet processed
              processedWebhooks: {
                none: {
                  type: 'order_processing',
                },
              },
            },
            {
              // Orders that failed processing
              unprocessable: {
                some: {
                  shop: shopDomain,
                },
              },
            },
          ],
        },
        select: {
          orderId: true,
          shop: true,
          createdAt: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      return orders;

    } catch (error) {
      this.logWarning(`Failed to get orders for ${shopDomain}`, { error: error.message });
      
      // Fallback: return empty array to continue with other shops
      return [];
    }
  }

  /**
   * Validate scheduled reconciliation options
   * @param {object} options - Options to validate
   * @returns {void}
   */
  validateOptions(options) {
    super.validateOptions(options);

    // Additional validation for scheduled reconciliation
    if (options.batchSize && (options.batchSize < 1 || options.batchSize > 100)) {
      throw new BusinessLogicError('Batch size must be between 1 and 100');
    }
  }

  /**
   * Get default date range for scheduled reconciliation
   * @param {object} options - Options containing date range
   * @returns {object} - Date range
   */
  getDateRange(options) {
    if (options.startDate && options.endDate) {
      return super.getDateRange(options);
    }

    // Default to previous month for scheduled reconciliation
    return {
      startDate: getFirstDayOfPreviousMonth(),
      endDate: getLastDayOfPreviousMonth(),
    };
  }

  /**
   * Get strategy metadata
   * @returns {object} - Strategy metadata
   */
  getMetadata() {
    return {
      ...super.getMetadata(),
      type: this.strategyType,
      description: 'Scheduled reconciliation of orders and invoice balances',
      defaultDateRange: 'Previous month',
      capabilities: [
        ...super.getMetadata().capabilities,
        'Multi-shop processing',
        'Order batch processing',
        'Failed order reprocessing',
        'Progress tracking per shop',
      ],
      options: {
        shopDomain: 'Specific shop to reconcile (optional)',
        startDate: 'Start date for reconciliation (optional)',
        endDate: 'End date for reconciliation (optional)',
        batchSize: 'Number of orders to process in each batch (1-100)',
        forceReprocess: 'Reprocess already processed orders',
        dryRun: 'Run without making changes',
      },
    };
  }
}
