/**
 * Base Reconciliation Strategy
 *
 * This abstract base class defines the interface and common functionality
 * for all reconciliation strategies in the system.
 */

import { container } from '../../../lib/container/ServiceContainer.server.js';
import { BusinessLogicError } from '../../../lib/errors/AppError.js';

/**
 * Abstract base class for reconciliation strategies
 */
export class BaseReconciliationStrategy {
  constructor(dependencies = {}) {
    this.prisma = dependencies.prisma || container.resolve('database');
    this.transactionManager = dependencies.transactionManager;
    this.reporter = dependencies.reporter;
    this.strategyType = 'base';
  }

  /**
   * Execute the reconciliation strategy (abstract method)
   * @param {object} options - Strategy options
   * @returns {Promise<object>} - Reconciliation result
   */
  async execute(options = {}) {
    throw new Error('execute method must be implemented by subclasses');
  }

  /**
   * Validate strategy options
   * @param {object} options - Options to validate
   * @returns {void}
   * @throws {BusinessLogicError} - If validation fails
   */
  validateOptions(options) {
    // Base validation - can be overridden by subclasses
    if (options.startDate && options.endDate) {
      const start = new Date(options.startDate);
      const end = new Date(options.endDate);

      if (start >= end) {
        throw new BusinessLogicError('Start date must be before end date');
      }

      // Check for reasonable date range (not more than 1 year)
      const daysDiff = (end - start) / (1000 * 60 * 60 * 24);
      if (daysDiff > 365) {
        throw new BusinessLogicError('Date range cannot exceed 365 days');
      }
    }
  }

  /**
   * Report progress to the reporter
   * @param {string} phase - Current phase
   * @param {object} progress - Progress data
   */
  reportProgress(phase, progress) {
    if (this.reporter) {
      this.reporter.reportProgress(this.strategyType, phase, progress);
    }
  }

  /**
   * Log strategy information
   * @param {string} message - Log message
   * @param {object} context - Additional context
   */
  logInfo(message, context = {}) {
    console.log(`[${this.strategyType.toUpperCase()}] ${message}`, context);
  }

  /**
   * Log strategy warning
   * @param {string} message - Warning message
   * @param {object} context - Additional context
   */
  logWarning(message, context = {}) {
    console.warn(`[${this.strategyType.toUpperCase()}] ${message}`, context);
  }

  /**
   * Log strategy error
   * @param {string} message - Error message
   * @param {object} context - Additional context
   */
  logError(message, context = {}) {
    console.error(`[${this.strategyType.toUpperCase()}] ${message}`, context);
  }

  /**
   * Get supported shops for reconciliation
   * @returns {Promise<Array<string>>} - Array of shop domains
   */
  async getSupportedShops() {
    try {
      const shops = await this.prisma.shop.findMany({
        where: {
          isActive: true,
        },
        select: {
          shop: true,
        },
      });

      return shops.map(s => s.shop);
    } catch (error) {
      this.logWarning('Failed to get supported shops', { error: error.message });
      return [];
    }
  }

  /**
   * Check if shop is supported
   * @param {string} shopDomain - Shop domain to check
   * @returns {Promise<boolean>} - True if shop is supported
   */
  async isShopSupported(shopDomain) {
    try {
      const shop = await this.prisma.shop.findFirst({
        where: {
          shop: shopDomain,
          isActive: true,
        },
      });

      return !!shop;
    } catch (error) {
      this.logWarning('Failed to check shop support', { shopDomain, error: error.message });
      return false;
    }
  }

  /**
   * Get date range for reconciliation
   * @param {object} options - Options containing date range
   * @returns {object} - Normalized date range
   */
  getDateRange(options) {
    let { startDate, endDate } = options;

    // Default to previous month if no dates provided
    if (!startDate || !endDate) {
      const now = new Date();
      const firstDayOfCurrentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const firstDayOfPreviousMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const lastDayOfPreviousMonth = new Date(firstDayOfCurrentMonth - 1);

      startDate = firstDayOfPreviousMonth;
      endDate = lastDayOfPreviousMonth;
    } else {
      startDate = new Date(startDate);
      endDate = new Date(endDate);
    }

    return { startDate, endDate };
  }

  /**
   * Create reconciliation summary
   * @param {object} results - Reconciliation results
   * @returns {object} - Summary object
   */
  createSummary(results) {
    return {
      strategyType: this.strategyType,
      executedAt: new Date(),
      summary: {
        totalProcessed: results.totalProcessed || 0,
        successful: results.successful || 0,
        failed: results.failed || 0,
        skipped: results.skipped || 0,
        errors: results.errors || [],
      },
      details: results.details || {},
    };
  }

  /**
   * Handle strategy errors
   * @param {Error} error - Error that occurred
   * @param {object} context - Error context
   * @returns {object} - Error result
   */
  handleError(error, context = {}) {
    this.logError('Strategy execution failed', {
      error: error.message,
      stack: error.stack,
      context,
    });

    return {
      success: false,
      error: error.message,
      context,
      executedAt: new Date(),
    };
  }

  /**
   * Batch process items with progress reporting
   * @param {Array} items - Items to process
   * @param {Function} processor - Processing function
   * @param {object} options - Processing options
   * @returns {Promise<object>} - Processing results
   */
  async batchProcess(items, processor, options = {}) {
    const {
      batchSize = 10,
      continueOnError = true,
      progressPhase = 'processing',
    } = options;

    const results = {
      totalProcessed: 0,
      successful: 0,
      failed: 0,
      errors: [],
    };

    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);

      // Report progress
      this.reportProgress(progressPhase, {
        processed: i,
        total: items.length,
        currentBatch: Math.floor(i / batchSize) + 1,
        totalBatches: Math.ceil(items.length / batchSize),
      });

      // Process batch
      for (const item of batch) {
        try {
          await processor(item);
          results.successful++;
        } catch (error) {
          results.failed++;
          results.errors.push({
            item: item.id || item,
            error: error.message,
          });

          if (!continueOnError) {
            throw error;
          }
        }
        results.totalProcessed++;
      }
    }

    // Report final progress
    this.reportProgress(progressPhase, {
      processed: items.length,
      total: items.length,
      completed: true,
    });

    return results;
  }

  /**
   * Sleep for specified duration
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get strategy metadata
   * @returns {object} - Strategy metadata
   */
  getMetadata() {
    return {
      type: this.strategyType,
      description: 'Base reconciliation strategy',
      version: '1.0.0',
      capabilities: [
        'Progress reporting',
        'Error handling',
        'Batch processing',
        'Date range validation',
      ],
    };
  }
}
