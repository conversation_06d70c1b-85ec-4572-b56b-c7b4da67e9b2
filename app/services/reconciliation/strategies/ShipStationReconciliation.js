/**
 * ShipStation Reconciliation Strategy
 *
 * This strategy reconciles shipping costs and fulfillment data
 * between ShipStation and the local database.
 */

import { BaseReconciliationStrategy } from './BaseReconciliationStrategy.js';
import { container } from '../../../lib/container/ServiceContainer.server.js';
import { BusinessLogicError } from '../../../lib/errors/AppError.js';
import { normalizeCurrencyToCents } from '../../../utils/id-normalization.server.js';

/**
 * ShipStation reconciliation strategy implementation
 */
export class ShipStationReconciliation extends BaseReconciliationStrategy {
  constructor(dependencies = {}) {
    super(dependencies);
    this.strategyType = 'shipstation_sync';
    this.shipstationClient = dependencies.shipstationClient || container.resolve('shipstationClient');
  }

  /**
   * Execute ShipStation reconciliation
   * @param {object} options - Reconciliation options
   * @returns {Promise<object>} - Reconciliation result
   */
  async execute(options = {}) {
    try {
      this.logInfo('Starting ShipStation reconciliation', options);

      // Validate options
      this.validateOptions(options);

      // Get date range
      const dateRange = this.getDateRange(options);
      this.logInfo('Using date range', dateRange);

      // Get ShipStation stores to reconcile
      const stores = await this.getShipStationStores(options.storeId);
      this.logInfo(`Reconciling ${stores.length} ShipStation stores`);

      // Initialize results
      const results = {
        totalStores: stores.length,
        processedStores: 0,
        totalShipments: 0,
        processedShipments: 0,
        successfulShipments: 0,
        failedShipments: 0,
        totalShippingCost: 0,
        storeResults: [],
        errors: [],
        dateRange,
      };

      // Process each store
      for (const store of stores) {
        try {
          this.reportProgress('store_processing', {
            currentStore: store.storeId,
            processedStores: results.processedStores,
            totalStores: results.totalStores,
          });

          const storeResult = await this.reconcileShipStationStore(store, dateRange, options);
          results.storeResults.push(storeResult);

          results.totalShipments += storeResult.totalShipments;
          results.processedShipments += storeResult.processedShipments;
          results.successfulShipments += storeResult.successfulShipments;
          results.failedShipments += storeResult.failedShipments;
          results.totalShippingCost += storeResult.totalShippingCost;
          results.processedStores++;

        } catch (error) {
          this.logError(`Failed to reconcile ShipStation store ${store.storeId}`, { error: error.message });
          results.errors.push({
            storeId: store.storeId,
            storeName: store.storeName,
            error: error.message,
          });
          results.processedStores++;
        }
      }

      // Generate final summary
      const summary = this.createSummary({
        totalProcessed: results.processedShipments,
        successful: results.successfulShipments,
        failed: results.failedShipments,
        errors: results.errors,
        details: results,
      });

      this.logInfo('ShipStation reconciliation completed', summary);
      return summary;

    } catch (error) {
      this.logError('ShipStation reconciliation failed', { error: error.message });
      return this.handleError(error, { options });
    }
  }

  /**
   * Get ShipStation stores to reconcile
   * @param {string} specificStoreId - Specific store ID (optional)
   * @returns {Promise<Array>} - Array of store objects
   */
  async getShipStationStores(specificStoreId = null) {
    try {
      if (specificStoreId) {
        // Get specific store from database mapping
        const storeMapping = await this.prisma.shipstationStoreMapping.findFirst({
          where: {
            shipstationStoreId: specificStoreId,
            isActive: true,
          },
        });

        if (!storeMapping) {
          throw new BusinessLogicError(`ShipStation store mapping not found: ${specificStoreId}`);
        }

        return [{
          storeId: storeMapping.shipstationStoreId,
          storeName: storeMapping.storeName,
          shopDomain: storeMapping.shopDomain,
        }];
      }

      // Get all active store mappings
      const storeMappings = await this.prisma.shipstationStoreMapping.findMany({
        where: {
          isActive: true,
        },
      });

      return storeMappings.map(mapping => ({
        storeId: mapping.shipstationStoreId,
        storeName: mapping.storeName,
        shopDomain: mapping.shopDomain,
      }));

    } catch (error) {
      this.logError('Failed to get ShipStation stores', { error: error.message });
      throw new BusinessLogicError(`Failed to get ShipStation stores: ${error.message}`);
    }
  }

  /**
   * Reconcile a single ShipStation store
   * @param {object} store - Store object
   * @param {object} dateRange - Date range for reconciliation
   * @param {object} options - Reconciliation options
   * @returns {Promise<object>} - Store reconciliation result
   */
  async reconcileShipStationStore(store, dateRange, options) {
    this.logInfo(`Starting reconciliation for ShipStation store: ${store.storeId}`);

    const storeResult = {
      storeId: store.storeId,
      storeName: store.storeName,
      shopDomain: store.shopDomain,
      totalShipments: 0,
      processedShipments: 0,
      successfulShipments: 0,
      failedShipments: 0,
      totalShippingCost: 0,
      errors: [],
      startTime: new Date(),
    };

    try {
      // Get shipments/labels from ShipStation
      const shipments = await this.getShipStationShipments(store.storeId, dateRange);
      storeResult.totalShipments = shipments.length;

      this.logInfo(`Found ${shipments.length} shipments for store ${store.storeId}`);

      if (shipments.length === 0) {
        storeResult.endTime = new Date();
        return storeResult;
      }

      // Process shipments in batches
      const batchResults = await this.batchProcess(
        shipments,
        async (shipment) => {
          await this.processShipment(shipment, store, options);
          storeResult.totalShippingCost += parseFloat(shipment.shipment_cost || 0);
        },
        {
          batchSize: options.batchSize || 50,
          continueOnError: true,
          progressPhase: 'shipment_processing',
        }
      );

      storeResult.processedShipments = batchResults.totalProcessed;
      storeResult.successfulShipments = batchResults.successful;
      storeResult.failedShipments = batchResults.failed;
      storeResult.errors = batchResults.errors;

      storeResult.endTime = new Date();
      storeResult.duration = storeResult.endTime - storeResult.startTime;

      this.logInfo(`Completed reconciliation for store ${store.storeId}`, {
        processed: storeResult.processedShipments,
        successful: storeResult.successfulShipments,
        failed: storeResult.failedShipments,
        totalCost: storeResult.totalShippingCost,
      });

      return storeResult;

    } catch (error) {
      storeResult.endTime = new Date();
      storeResult.error = error.message;

      this.logError(`Store reconciliation failed for ${store.storeId}`, { error: error.message });
      throw error;
    }
  }

  /**
   * Get shipments from ShipStation
   * @param {string} storeId - ShipStation store ID
   * @param {object} dateRange - Date range
   * @returns {Promise<Array>} - Array of shipments
   */
  async getShipStationShipments(storeId, dateRange) {
    try {
      // Use labels endpoint for better shipping cost data
      const labels = await this.shipstationClient.getAllLabels({
        store_id: storeId,
        created_at_start: dateRange.startDate.toISOString(),
        created_at_end: dateRange.endDate.toISOString(),
      }, (progress) => {
        this.reportProgress('shipstation_fetch', {
          storeId,
          ...progress,
        });
      });

      // Filter out voided shipments
      return labels.filter(label => label.status !== 'voided');

    } catch (error) {
      this.logError(`Failed to get ShipStation shipments for store ${storeId}`, { error: error.message });
      throw new BusinessLogicError(`Failed to fetch ShipStation shipments: ${error.message}`);
    }
  }

  /**
   * Process a single shipment
   * @param {object} shipment - ShipStation shipment/label data
   * @param {object} store - Store object
   * @param {object} options - Processing options
   * @returns {Promise<void>}
   */
  async processShipment(shipment, store, options) {
    try {
      if (options.dryRun) {
        return; // Skip actual processing in dry run mode
      }

      // Extract shipping cost data
      const shippingCost = parseFloat(shipment.shipment_cost || 0);
      const createdAt = new Date(shipment.created_at);

      // Save or update shipping cost record
      await this.prisma.shippingCost.upsert({
        where: {
          shipmentId_storeId: {
            shipmentId: shipment.shipment_id.toString(),
            storeId: store.storeId,
          },
        },
        update: {
          cost: shippingCost,
          shop: store.shopDomain,
          date: createdAt,
          updatedAt: new Date(),
        },
        create: {
          shipmentId: shipment.shipment_id.toString(),
          storeId: store.storeId,
          shop: store.shopDomain,
          cost: shippingCost,
          date: createdAt,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

    } catch (error) {
      this.logError(`Failed to process shipment ${shipment.shipment_id}`, { error: error.message });
      throw error;
    }
  }

  /**
   * Validate ShipStation reconciliation options
   * @param {object} options - Options to validate
   * @returns {void}
   */
  validateOptions(options) {
    super.validateOptions(options);

    // Additional validation for ShipStation reconciliation
    if (options.batchSize && (options.batchSize < 1 || options.batchSize > 100)) {
      throw new BusinessLogicError('Batch size must be between 1 and 100');
    }

    if (options.storeId && typeof options.storeId !== 'string') {
      throw new BusinessLogicError('Store ID must be a string');
    }
  }

  /**
   * Get strategy metadata
   * @returns {object} - Strategy metadata
   */
  getMetadata() {
    return {
      ...super.getMetadata(),
      type: this.strategyType,
      description: 'Reconcile shipping costs and fulfillment data from ShipStation',
      capabilities: [
        ...super.getMetadata().capabilities,
        'ShipStation API integration',
        'Shipping cost reconciliation',
        'Store mapping support',
        'Voided shipment filtering',
      ],
      options: {
        storeId: 'Specific ShipStation store ID to reconcile (optional)',
        startDate: 'Start date for shipment search (optional)',
        endDate: 'End date for shipment search (optional)',
        batchSize: 'Number of shipments to process in each batch (1-100)',
        dryRun: 'Fetch shipments without saving to database',
      },
      requirements: [
        'ShipStation API credentials configured',
        'Store mappings configured in database',
        'Active ShipStation stores',
      ],
    };
  }
}
