/**
 * Authentication Middleware
 * 
 * This module provides authentication middleware for various types of
 * requests including Shopify OAuth, scheduled jobs, and webhooks.
 */

import crypto from 'crypto';
import { config } from '../lib/config/index.js';
import { AuthenticationError, ValidationError } from '../lib/errors/AppError.js';

/**
 * Verify scheduled job token
 * @param {string} providedToken - Token provided in request
 * @returns {boolean} - True if token is valid
 */
export function verifyScheduledJobToken(providedToken) {
  if (!providedToken) {
    return false;
  }

  const expectedToken = config.security.scheduledJobToken;
  
  // Use constant-time comparison to prevent timing attacks
  return crypto.timingSafeEqual(
    Buffer.from(providedToken),
    Buffer.from(expectedToken)
  );
}

/**
 * Verify webhook signature
 * @param {string} payload - Raw request payload
 * @param {string} signature - Provided signature
 * @param {string} secret - Webhook secret
 * @returns {boolean} - True if signature is valid
 */
export function verifyWebhookSignature(payload, signature, secret) {
  if (!payload || !signature || !secret) {
    return false;
  }

  // Calculate expected signature
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload, 'utf8')
    .digest('hex');

  // Remove any prefix from the signature (e.g., "sha256=")
  const cleanSignature = signature.replace(/^sha256=/, '');

  // Use constant-time comparison
  return crypto.timingSafeEqual(
    Buffer.from(cleanSignature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  );
}

/**
 * Verify Shopify webhook signature
 * @param {string} payload - Raw request payload
 * @param {string} signature - X-Shopify-Hmac-Sha256 header
 * @returns {boolean} - True if signature is valid
 */
export function verifyShopifyWebhook(payload, signature) {
  const secret = config.shopify.apiSecret;
  return verifyWebhookSignature(payload, signature, secret);
}

/**
 * Verify ShipStation webhook signature
 * @param {string} payload - Raw request payload
 * @param {string} signature - Provided signature
 * @returns {boolean} - True if signature is valid
 */
export function verifyShipStationWebhook(payload, signature) {
  if (!config.shipstation.webhookKey) {
    // If no webhook key is configured, skip verification
    console.warn('ShipStation webhook key not configured, skipping signature verification');
    return true;
  }

  return verifyWebhookSignature(payload, signature, config.shipstation.webhookKey);
}

/**
 * Extract and validate shop parameter
 * @param {Request} request - The request object
 * @returns {string|null} - Shop domain or null if invalid
 */
export function extractShopFromRequest(request) {
  const url = new URL(request.url);
  const shop = url.searchParams.get('shop');

  if (!shop) {
    return null;
  }

  // Validate shop domain format
  const shopRegex = /^[a-zA-Z0-9-]+\.myshopify\.com$/;
  if (!shopRegex.test(shop)) {
    throw new ValidationError('Invalid shop domain format');
  }

  return shop;
}

/**
 * Scheduled job authentication middleware
 * @param {Request} request - The request object
 * @returns {boolean} - True if authenticated
 */
export function authenticateScheduledJob(request) {
  const authHeader = request.headers.get('authorization');
  
  if (!authHeader) {
    throw new AuthenticationError('Missing authorization header');
  }

  // Extract token from "Bearer <token>" format
  const tokenMatch = authHeader.match(/^Bearer\s+(.+)$/);
  if (!tokenMatch) {
    throw new AuthenticationError('Invalid authorization header format');
  }

  const token = tokenMatch[1];
  
  if (!verifyScheduledJobToken(token)) {
    throw new AuthenticationError('Invalid scheduled job token');
  }

  return true;
}

/**
 * Webhook authentication middleware
 * @param {Request} request - The request object
 * @param {string} payload - Raw request payload
 * @param {string} webhookType - Type of webhook ('shopify' or 'shipstation')
 * @returns {boolean} - True if authenticated
 */
export function authenticateWebhook(request, payload, webhookType) {
  switch (webhookType) {
    case 'shopify': {
      const signature = request.headers.get('x-shopify-hmac-sha256');
      if (!signature) {
        throw new AuthenticationError('Missing Shopify webhook signature');
      }
      
      if (!verifyShopifyWebhook(payload, signature)) {
        throw new AuthenticationError('Invalid Shopify webhook signature');
      }
      
      return true;
    }

    case 'shipstation': {
      const signature = request.headers.get('x-shipstation-signature');
      
      // ShipStation webhook signature is optional
      if (signature && !verifyShipStationWebhook(payload, signature)) {
        throw new AuthenticationError('Invalid ShipStation webhook signature');
      }
      
      return true;
    }

    default:
      throw new ValidationError(`Unknown webhook type: ${webhookType}`);
  }
}

/**
 * API key authentication middleware
 * @param {Request} request - The request object
 * @param {string} expectedApiKey - Expected API key
 * @returns {boolean} - True if authenticated
 */
export function authenticateApiKey(request, expectedApiKey) {
  const apiKey = request.headers.get('x-api-key') || 
                 request.headers.get('api-key') ||
                 request.headers.get('authorization')?.replace('Bearer ', '');

  if (!apiKey) {
    throw new AuthenticationError('Missing API key');
  }

  if (apiKey !== expectedApiKey) {
    throw new AuthenticationError('Invalid API key');
  }

  return true;
}

/**
 * Create authentication middleware factory
 * @param {string} type - Authentication type
 * @param {object} options - Authentication options
 * @returns {Function} - Middleware function
 */
export function createAuthMiddleware(type, options = {}) {
  return async function authMiddleware(request, context) {
    try {
      switch (type) {
        case 'scheduled-job':
          authenticateScheduledJob(request);
          break;

        case 'webhook': {
          const { webhookType = 'shopify' } = options;
          const payload = await request.text();
          authenticateWebhook(request, payload, webhookType);
          
          // Re-create request with consumed body
          const newRequest = new Request(request.url, {
            method: request.method,
            headers: request.headers,
            body: payload,
          });
          
          return context.next(newRequest);
        }

        case 'api-key': {
          const { apiKey } = options;
          if (!apiKey) {
            throw new ValidationError('API key not provided in options');
          }
          authenticateApiKey(request, apiKey);
          break;
        }

        case 'shopify-oauth': {
          const shop = extractShopFromRequest(request);
          if (!shop) {
            throw new AuthenticationError('Missing or invalid shop parameter');
          }
          
          // Add shop to request context
          request.shop = shop;
          break;
        }

        default:
          throw new ValidationError(`Unknown authentication type: ${type}`);
      }

      return context.next(request);
    } catch (error) {
      if (error instanceof AuthenticationError || error instanceof ValidationError) {
        return Response.json(
          { error: error.message, code: error.code },
          { status: error.statusCode }
        );
      }

      console.error('Authentication middleware error:', error);
      return Response.json(
        { error: 'Authentication failed' },
        { status: 500 }
      );
    }
  };
}

/**
 * Multi-factor authentication helper
 * @param {Request} request - The request object
 * @param {string[]} authTypes - Array of authentication types to check
 * @param {object} options - Authentication options
 * @returns {boolean} - True if any authentication method succeeds
 */
export function authenticateMultiple(request, authTypes, options = {}) {
  const errors = [];

  for (const authType of authTypes) {
    try {
      switch (authType) {
        case 'scheduled-job':
          authenticateScheduledJob(request);
          return true;

        case 'api-key':
          if (options.apiKey) {
            authenticateApiKey(request, options.apiKey);
            return true;
          }
          break;

        default:
          errors.push(`Unknown auth type: ${authType}`);
      }
    } catch (error) {
      errors.push(`${authType}: ${error.message}`);
    }
  }

  throw new AuthenticationError(
    `All authentication methods failed: ${errors.join(', ')}`
  );
}

/**
 * Request timing validation
 * @param {Request} request - The request object
 * @param {number} maxAge - Maximum age in seconds (default: 300 = 5 minutes)
 * @returns {boolean} - True if request is within time window
 */
export function validateRequestTiming(request, maxAge = 300) {
  const timestamp = request.headers.get('x-timestamp');
  
  if (!timestamp) {
    throw new AuthenticationError('Missing timestamp header');
  }

  const requestTime = parseInt(timestamp, 10);
  const currentTime = Math.floor(Date.now() / 1000);
  const age = currentTime - requestTime;

  if (age > maxAge) {
    throw new AuthenticationError('Request timestamp too old');
  }

  if (age < -60) { // Allow 1 minute clock skew
    throw new AuthenticationError('Request timestamp too far in the future');
  }

  return true;
}
