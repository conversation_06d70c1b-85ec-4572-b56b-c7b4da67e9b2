/**
 * Security Middleware
 * 
 * This module provides security middleware for Content Security Policy,
 * request sanitization, and other security measures.
 */

import { config } from '../lib/config/index.js';

/**
 * Content Security Policy configuration
 */
const CSP_DIRECTIVES = {
  'default-src': ["'self'"],
  'script-src': [
    "'self'",
    "'unsafe-inline'", // Required for Shopify App Bridge
    "'unsafe-eval'", // Required for some Shopify functionality
    'https://cdn.shopify.com',
    'https://shopify.dev',
    'https://js.stripe.com',
  ],
  'style-src': [
    "'self'",
    "'unsafe-inline'", // Required for Polaris styles
    'https://cdn.shopify.com',
    'https://fonts.googleapis.com',
  ],
  'font-src': [
    "'self'",
    'https://fonts.gstatic.com',
    'https://cdn.shopify.com',
  ],
  'img-src': [
    "'self'",
    'data:',
    'https:',
    'https://cdn.shopify.com',
  ],
  'connect-src': [
    "'self'",
    'https://api.shopify.com',
    'https://ssapi.shipstation.com',
    'https://api.shipstation.com',
    config.app.appUrl,
  ],
  'frame-src': [
    "'self'",
    'https://admin.shopify.com',
    'https://shopify.dev',
  ],
  'frame-ancestors': [
    'https://admin.shopify.com',
    'https://*.myshopify.com',
  ],
  'object-src': ["'none'"],
  'base-uri': ["'self'"],
  'form-action': ["'self'"],
};

/**
 * Generate CSP header value
 * @returns {string} - CSP header value
 */
function generateCSPHeader() {
  return Object.entries(CSP_DIRECTIVES)
    .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
    .join('; ');
}

/**
 * Security headers middleware
 * @param {Request} request - The request object
 * @param {Response} response - The response object
 * @returns {Response} - Response with security headers
 */
export function addSecurityHeaders(request, response) {
  const headers = new Headers(response.headers);

  // Content Security Policy
  headers.set('Content-Security-Policy', generateCSPHeader());

  // X-Frame-Options (backup for older browsers)
  headers.set('X-Frame-Options', 'SAMEORIGIN');

  // X-Content-Type-Options
  headers.set('X-Content-Type-Options', 'nosniff');

  // X-XSS-Protection
  headers.set('X-XSS-Protection', '1; mode=block');

  // Referrer Policy
  headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Permissions Policy
  headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  // HSTS (only in production with HTTPS)
  if (config.app.environment === 'production' && request.url.startsWith('https://')) {
    headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  }

  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers,
  });
}

/**
 * Request sanitization middleware
 * @param {Request} request - The request object
 * @returns {Request} - Sanitized request object
 */
export function sanitizeRequest(request) {
  // Clone the request to avoid modifying the original
  const url = new URL(request.url);
  
  // Remove potentially dangerous query parameters
  const dangerousParams = ['__proto__', 'constructor', 'prototype'];
  dangerousParams.forEach(param => {
    url.searchParams.delete(param);
  });

  // Limit query string length
  const maxQueryLength = 2048;
  if (url.search.length > maxQueryLength) {
    console.warn(`Query string too long: ${url.search.length} characters`);
    // Truncate query string
    url.search = url.search.substring(0, maxQueryLength);
  }

  return new Request(url.toString(), {
    method: request.method,
    headers: request.headers,
    body: request.body,
  });
}

/**
 * Input validation middleware
 * @param {object} data - Data to validate
 * @param {object} options - Validation options
 * @returns {object} - Sanitized data
 */
export function sanitizeInput(data, options = {}) {
  const { maxStringLength = 1000, allowHtml = false } = options;

  if (typeof data !== 'object' || data === null) {
    return data;
  }

  const sanitized = {};

  for (const [key, value] of Object.entries(data)) {
    // Skip dangerous property names
    if (['__proto__', 'constructor', 'prototype'].includes(key)) {
      continue;
    }

    if (typeof value === 'string') {
      // Limit string length
      let sanitizedValue = value.length > maxStringLength 
        ? value.substring(0, maxStringLength) 
        : value;

      // Remove HTML if not allowed
      if (!allowHtml) {
        sanitizedValue = sanitizedValue.replace(/<[^>]*>/g, '');
      }

      // Remove null bytes
      sanitizedValue = sanitizedValue.replace(/\0/g, '');

      sanitized[key] = sanitizedValue;
    } else if (typeof value === 'object' && value !== null) {
      // Recursively sanitize nested objects
      sanitized[key] = sanitizeInput(value, options);
    } else {
      sanitized[key] = value;
    }
  }

  return sanitized;
}

/**
 * Rate limiting state (in-memory for simplicity)
 */
const rateLimitStore = new Map();

/**
 * Simple rate limiting middleware
 * @param {string} identifier - Unique identifier for rate limiting
 * @param {object} options - Rate limiting options
 * @returns {boolean} - True if request is allowed
 */
export function checkRateLimit(identifier, options = {}) {
  const {
    windowMs = 60000, // 1 minute
    maxRequests = 100,
    skipSuccessfulRequests = false,
  } = options;

  const now = Date.now();
  const windowStart = now - windowMs;

  // Get or create rate limit data for this identifier
  let rateLimitData = rateLimitStore.get(identifier);
  if (!rateLimitData) {
    rateLimitData = { requests: [], blocked: 0 };
    rateLimitStore.set(identifier, rateLimitData);
  }

  // Remove old requests outside the window
  rateLimitData.requests = rateLimitData.requests.filter(
    timestamp => timestamp > windowStart
  );

  // Check if limit exceeded
  if (rateLimitData.requests.length >= maxRequests) {
    rateLimitData.blocked++;
    return false;
  }

  // Add current request
  rateLimitData.requests.push(now);
  return true;
}

/**
 * Clean up old rate limit data
 */
export function cleanupRateLimitStore() {
  const now = Date.now();
  const maxAge = 3600000; // 1 hour

  for (const [identifier, data] of rateLimitStore.entries()) {
    const hasRecentRequests = data.requests.some(
      timestamp => now - timestamp < maxAge
    );

    if (!hasRecentRequests) {
      rateLimitStore.delete(identifier);
    }
  }
}

// Clean up rate limit store every 10 minutes
setInterval(cleanupRateLimitStore, 600000);

/**
 * Security middleware factory
 * @param {object} options - Security options
 * @returns {Function} - Middleware function
 */
export function createSecurityMiddleware(options = {}) {
  const {
    enableCSP = true,
    enableRateLimit = true,
    enableSanitization = true,
    rateLimitOptions = {},
  } = options;

  return async function securityMiddleware(request, context) {
    try {
      let processedRequest = request;

      // Sanitize request if enabled
      if (enableSanitization) {
        processedRequest = sanitizeRequest(processedRequest);
      }

      // Check rate limit if enabled
      if (enableRateLimit) {
        const identifier = getClientIdentifier(processedRequest);
        const isAllowed = checkRateLimit(identifier, rateLimitOptions);
        
        if (!isAllowed) {
          return new Response('Rate limit exceeded', {
            status: 429,
            headers: {
              'Retry-After': '60',
              'Content-Type': 'text/plain',
            },
          });
        }
      }

      // Continue with the request
      const response = await context.next(processedRequest);

      // Add security headers if enabled
      if (enableCSP && response) {
        return addSecurityHeaders(processedRequest, response);
      }

      return response;
    } catch (error) {
      console.error('Security middleware error:', error);
      return new Response('Internal Server Error', { status: 500 });
    }
  };
}

/**
 * Get client identifier for rate limiting
 * @param {Request} request - The request object
 * @returns {string} - Client identifier
 */
function getClientIdentifier(request) {
  // Try to get IP from various headers
  const forwarded = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const cfConnectingIp = request.headers.get('cf-connecting-ip');
  
  const ip = forwarded?.split(',')[0] || realIp || cfConnectingIp || 'unknown';
  
  // Include user agent for more specific identification
  const userAgent = request.headers.get('user-agent') || 'unknown';
  
  return `${ip}:${userAgent.substring(0, 50)}`;
}
