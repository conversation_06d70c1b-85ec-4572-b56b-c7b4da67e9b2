/**
 * Centralized Error Handling Middleware
 * 
 * This module provides centralized error handling for the application,
 * including error logging, response formatting, and error recovery.
 */

import { AppError, ErrorLogger } from '../lib/errors/AppError.js';
import { config } from '../lib/config/index.js';

/**
 * Global error handler middleware
 * @param {Error} error - The error that occurred
 * @param {Request} request - The request object
 * @param {object} context - Additional context
 * @returns {Response} - Error response
 */
export function handleError(error, request, context = {}) {
  // Log the error with context
  ErrorLogger.logSafe(error, {
    url: request?.url,
    method: request?.method,
    userAgent: request?.headers?.get('user-agent'),
    ...context,
  });

  // Handle known application errors
  if (error instanceof AppError) {
    return createErrorResponse(error, request);
  }

  // Handle specific error types
  if (error.name === 'ValidationError' || error.name === 'ZodError') {
    const appError = new AppError(
      'Validation failed',
      400,
      'VALIDATION_ERROR',
      { validationErrors: error.errors || error.message }
    );
    return createErrorResponse(appError, request);
  }

  if (error.name === 'PrismaClientKnownRequestError') {
    return handlePrismaError(error, request);
  }

  if (error.name === 'AxiosError') {
    return handleAxiosError(error, request);
  }

  // Handle unexpected errors
  const appError = new AppError(
    config.app.environment === 'production' 
      ? 'An unexpected error occurred' 
      : error.message,
    500,
    'UNEXPECTED_ERROR'
  );

  return createErrorResponse(appError, request);
}

/**
 * Handle Prisma database errors
 * @param {Error} error - Prisma error
 * @param {Request} request - The request object
 * @returns {Response} - Error response
 */
function handlePrismaError(error, request) {
  let appError;

  switch (error.code) {
    case 'P2002':
      appError = new AppError(
        'A record with this information already exists',
        409,
        'DUPLICATE_RECORD',
        { field: error.meta?.target }
      );
      break;

    case 'P2025':
      appError = new AppError(
        'Record not found',
        404,
        'RECORD_NOT_FOUND'
      );
      break;

    case 'P2003':
      appError = new AppError(
        'Foreign key constraint failed',
        400,
        'FOREIGN_KEY_ERROR',
        { field: error.meta?.field_name }
      );
      break;

    case 'P2014':
      appError = new AppError(
        'Invalid data provided',
        400,
        'INVALID_DATA',
        { relation: error.meta?.relation_name }
      );
      break;

    default:
      appError = new AppError(
        config.app.environment === 'production'
          ? 'Database operation failed'
          : error.message,
        500,
        'DATABASE_ERROR',
        { prismaCode: error.code }
      );
  }

  return createErrorResponse(appError, request);
}

/**
 * Handle Axios HTTP errors
 * @param {Error} error - Axios error
 * @param {Request} request - The request object
 * @returns {Response} - Error response
 */
function handleAxiosError(error, request) {
  let appError;

  if (error.response) {
    // Server responded with error status
    const status = error.response.status;
    const service = getServiceFromUrl(error.config?.url);

    if (status === 429) {
      appError = new AppError(
        `${service} rate limit exceeded`,
        429,
        'RATE_LIMIT_ERROR',
        { service, retryAfter: error.response.headers['retry-after'] }
      );
    } else if (status >= 500) {
      appError = new AppError(
        `${service} service unavailable`,
        502,
        'SERVICE_UNAVAILABLE',
        { service, status }
      );
    } else {
      appError = new AppError(
        `${service} request failed`,
        status,
        'EXTERNAL_API_ERROR',
        { service, status }
      );
    }
  } else if (error.request) {
    // Network error
    const service = getServiceFromUrl(error.config?.url);
    appError = new AppError(
      `Unable to connect to ${service}`,
      502,
      'NETWORK_ERROR',
      { service }
    );
  } else {
    // Request setup error
    appError = new AppError(
      'Request configuration error',
      500,
      'REQUEST_ERROR'
    );
  }

  return createErrorResponse(appError, request);
}

/**
 * Extract service name from URL
 * @param {string} url - Request URL
 * @returns {string} - Service name
 */
function getServiceFromUrl(url) {
  if (!url) return 'External service';
  
  if (url.includes('shopify.com')) return 'Shopify';
  if (url.includes('shipstation.com')) return 'ShipStation';
  
  return 'External service';
}

/**
 * Create standardized error response
 * @param {AppError} error - Application error
 * @param {Request} request - The request object
 * @returns {Response} - Error response
 */
function createErrorResponse(error, request) {
  const response = {
    success: false,
    error: {
      message: error.getClientMessage(),
      code: error.code,
      timestamp: error.timestamp,
    },
  };

  // Add details in development
  if (config.app.environment === 'development' && error.details) {
    response.error.details = error.details;
  }

  // Add request ID if available
  const requestId = request?.headers?.get('x-request-id');
  if (requestId) {
    response.error.requestId = requestId;
  }

  return Response.json(response, {
    status: error.statusCode,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * Async error handler wrapper for route handlers
 * @param {Function} handler - Route handler function
 * @returns {Function} - Wrapped handler with error handling
 */
export function withErrorHandler(handler) {
  return async function wrappedHandler(request, ...args) {
    try {
      return await handler(request, ...args);
    } catch (error) {
      return handleError(error, request, {
        handler: handler.name,
      });
    }
  };
}

/**
 * Error boundary for React components (server-side)
 * @param {Function} component - React component
 * @returns {Function} - Wrapped component with error boundary
 */
export function withErrorBoundary(component) {
  return function ErrorBoundaryWrapper(props) {
    try {
      return component(props);
    } catch (error) {
      ErrorLogger.logSafe(error, {
        component: component.name,
        props: Object.keys(props),
      });

      // Return error fallback UI
      return {
        error: true,
        message: config.app.environment === 'production'
          ? 'Something went wrong'
          : error.message,
      };
    }
  };
}

/**
 * Global unhandled error handlers
 */
if (typeof process !== 'undefined') {
  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    ErrorLogger.log(new Error(`Unhandled Promise Rejection: ${reason}`), {
      type: 'unhandledRejection',
      promise: promise.toString(),
    });
  });

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    ErrorLogger.log(error, {
      type: 'uncaughtException',
    });

    // Exit gracefully in production
    if (config.app.environment === 'production') {
      process.exit(1);
    }
  });
}

/**
 * Health check error handler
 * @param {Error} error - Health check error
 * @returns {object} - Health check response
 */
export function handleHealthCheckError(error) {
  ErrorLogger.logSafe(error, {
    type: 'healthCheck',
  });

  return {
    status: 'unhealthy',
    error: error.message,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Webhook error handler
 * @param {Error} error - Webhook processing error
 * @param {string} webhookType - Type of webhook
 * @param {string} webhookId - Webhook ID
 * @returns {Response} - Webhook error response
 */
export function handleWebhookError(error, webhookType, webhookId) {
  ErrorLogger.logSafe(error, {
    type: 'webhook',
    webhookType,
    webhookId,
  });

  // For webhooks, we often want to return 200 to prevent retries
  // unless it's a validation error
  const shouldRetry = !(error instanceof AppError && error.statusCode < 500);
  
  return Response.json(
    {
      success: false,
      error: error.message,
      shouldRetry,
    },
    { 
      status: shouldRetry ? 500 : 200,
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
}
