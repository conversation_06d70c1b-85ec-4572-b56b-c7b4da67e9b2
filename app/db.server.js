/**
 * db.server.js
 *
 * This file initializes and exports the Prisma client for database operations.
 * It ensures that only one instance of PrismaClient is created in development
 * to prevent connection pool exhaustion and provides optimized configuration
 * for PostgreSQL.
 */

import { PrismaClient } from "@prisma/client";
import { config } from "./lib/config/index.js";

/**
 * Create Prisma client with optimized configuration
 */
function createPrismaClient() {
  const clientConfig = {
    datasources: {
      db: {
        url: config.database.url,
      },
    },
  };

  // Production configuration
  if (config.app.environment === "production") {
    clientConfig.log = ['error'];
    clientConfig.errorFormat = 'minimal';
  } else {
    // Development configuration - removed 'query' to reduce verbosity
    clientConfig.log = ['info', 'warn', 'error'];
    clientConfig.errorFormat = 'pretty';
  }

  return new PrismaClient(clientConfig);
}

// Global instance management for development
if (config.app.environment !== "production") {
  if (!global.prismaGlobal) {
    global.prismaGlobal = createPrismaClient();
  }
}

const prisma = global.prismaGlobal ?? createPrismaClient();

// Graceful shutdown handling
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});

process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

export default prisma;
