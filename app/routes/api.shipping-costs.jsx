import { authenticate } from "../shopify.server";
import { getShippingCostsByMonth } from "../models/ShippingCost.server";

/**
 * API endpoint to fetch shipping costs for a specific month and year
 * This endpoint is used by the invoice page to fetch shipping costs when the user changes the month
 */
export async function loader({ request }) {
  try {
    // Authenticate the request
    const { session } = await authenticate.admin(request);
    
    // Get the month and year from the query parameters
    const url = new URL(request.url);
    const month = url.searchParams.get("month");
    const year = url.searchParams.get("year");
    
    // Validate the parameters
    if (!month || !year) {
      return Response.json({ error: "Month and year are required" }, { status: 400 });
    }
    
    // Convert to numbers
    const monthNum = Number(month);
    const yearNum = Number(year);
    
    // Validate the numbers
    if (isNaN(monthNum) || isNaN(yearNum) || monthNum < 0 || monthNum > 11 || yearNum < 2000) {
      return Response.json({ error: "Invalid month or year" }, { status: 400 });
    }
    
    console.log(`Fetching shipping costs for ${session.shop}, month: ${monthNum}, year: ${yearNum}`);
    
    // Get the shipping costs for the specified month and year
    const shippingCosts = await getShippingCostsByMonth(session.shop, monthNum, yearNum);
    
    console.log(`Found ${shippingCosts.length} shipping costs for month ${monthNum}, year ${yearNum}`);
    
    // Return the shipping costs
    return Response.json({ shippingCosts });
  } catch (error) {
    console.error(`Error fetching shipping costs: ${error.message}`);
    return Response.json({ error: error.message }, { status: 500 });
  }
}
