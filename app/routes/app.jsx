import { Link, Outlet, useLoaderD<PERSON>, useRouteError, useLocation } from "@remix-run/react";
import { boundary } from "@shopify/shopify-app-remix/server";
import { AppProvider } from "@shopify/shopify-app-remix/react";
import { NavMenu } from "@shopify/app-bridge-react";
import polarisStyles from "@shopify/polaris/build/esm/styles.css?url";

export const links = () => [{ rel: "stylesheet", href: polarisStyles }];

export const loader = async ({ request }) => {
  // Import server-only functions inside the loader
  const { authenticateMultistore, getStoreConfig } = await import("../shopify.multistore.server");

  console.log(`[App Loader] Processing request: ${request.url}`);

  // Use multistore authentication - no fallback to single store
  const result = await authenticateMultistore(request);

  // If result is a Response (redirect), throw it to let <PERSON> handle it
  if (result instanceof Response) {
    console.log(`[App Loader] Got redirect response, throwing it`);
    throw result;
  }

  const { session } = result;
  console.log(`[App Loader] Successfully authenticated for shop: ${session.shop}`);

  let isAdmin = false;
  if (session.shop === process.env.ADMIN_SHOP) {
    isAdmin = true;
    console.log(`[App Loader] Shop is admin: ${session.shop}`);
  }

  // Get the appropriate API key for this store from the store configuration
  const storeConfig = getStoreConfig(session.shop);
  console.log(`[App Loader] Store config for ${session.shop}:`, storeConfig ? 'found' : 'not found');

  const apiKey = storeConfig?.apiKey || process.env.SHOPIFY_API_KEY || "";

  console.log(`[App Loader] Using API key for ${session.shop}: ${apiKey ? apiKey.substring(0, 8) + '...' : 'none'}`);
  console.log(`[App Loader] Expected API key should start with: ${session.shop === '75th-rra-app-development-store.myshopify.com' ? '6a5f4bd1' : session.shop === 'pld-app-development-store.myshopify.com' ? '8c37f275' : '4f62408a'}`);

  return {
    apiKey,
    isAdmin,
    shop: session.shop
  };
};

export default function App() {
  const { apiKey, isAdmin, shop } = useLoaderData();
  const location = useLocation();

  console.log(`[App Component] Rendering with location: ${location.pathname}${location.search}`);
  console.log(`[App Component] Shop: ${shop}, isAdmin: ${isAdmin}`);

  return (
    <AppProvider isEmbeddedApp apiKey={apiKey}>
      <NavMenu>
        <Link to="/app" rel="home" reloadDocument>
          Home
        </Link>
        {!isAdmin && <Link to="/app/invoice" reloadDocument>Cost Estimates</Link>}
        { isAdmin && <Link to="/app/all-balances" reloadDocument>Client Balances by Month</Link>}
        { isAdmin && <Link to="/app/shipstation" reloadDocument>Setup ShipStation Integrations</Link>}
      </NavMenu>
      <Outlet />
    </AppProvider>
  );
}

// Shopify needs Remix to catch some thrown responses, so that their headers are included in the response.
export function ErrorBoundary() {
  return boundary.error(useRouteError());
}

export const headers = (headersArgs) => {
  return boundary.headers(headersArgs);
};
