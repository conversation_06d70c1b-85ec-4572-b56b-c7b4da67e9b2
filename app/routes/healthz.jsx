/**
 * Health check endpoint for Remix application
 * Route: GET /healthz
 *
 * This endpoint provides health status information for the Remix server
 * and is used by Docker healthchecks and deployment validation.
 */

import { json } from "@remix-run/node";

/**
 * Health check loader
 * @param {object} request - Request object
 * @returns {Response} Health check response
 */
export const loader = async ({ request }) => {
  try {
    // Get basic system information
    const memoryUsage = process.memoryUsage();
    const uptime = process.uptime();

    // Format memory usage
    const formatBytes = (bytes) => {
      const mb = bytes / 1024 / 1024;
      return `${mb.toFixed(2)} MB`;
    };

    // Lazy import monitoring service to avoid bundling issues
    const { monitoring } = await import("../utils/monitoring.server.js");

    // Run health checks through monitoring service
    const healthChecks = await monitoring.runHealthChecks();

    // Determine overall health status
    const isHealthy = healthChecks.status === 'healthy';
    const statusCode = isHealthy ? 200 : 503;

    // Build response data
    const responseData = {
      service: 'remix-server',
      status: isHealthy ? 'healthy' : 'unhealthy',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      timestamp: new Date().toISOString(),
      uptime: {
        seconds: Math.floor(uptime),
        formatted: `${Math.floor(uptime / 60)}m ${Math.floor(uptime % 60)}s`,
      },
      memory: {
        rss: formatBytes(memoryUsage.rss),
        heapTotal: formatBytes(memoryUsage.heapTotal),
        heapUsed: formatBytes(memoryUsage.heapUsed),
        external: formatBytes(memoryUsage.external),
        arrayBuffers: formatBytes(memoryUsage.arrayBuffers),
      },
      healthChecks: healthChecks.checks,
    };

    // Add request information for debugging
    const url = new URL(request.url);
    responseData.request = {
      method: request.method,
      url: url.pathname,
      userAgent: request.headers.get('user-agent'),
      timestamp: new Date().toISOString(),
    };

    return json(responseData, {
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('Health check error:', error);

    // Return unhealthy status with error information
    return json({
      service: 'remix-server',
      status: 'unhealthy',
      error: {
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      },
      timestamp: new Date().toISOString(),
    }, {
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });
  }
};

/**
 * Handle GET requests to /healthz
 * This is the same as the loader function for this route
 */
export { loader as action };
