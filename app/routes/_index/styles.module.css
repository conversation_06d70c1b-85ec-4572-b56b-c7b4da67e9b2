.index {
  align-items: center;
  display: flex;
  justify-content: center;
  height: 100%;
  width: 100%;
  text-align: center;
  padding: 1rem;
}

.heading,
.text {
  padding: 0;
  margin: 0;
}

.text {
  font-size: 1.2rem;
  padding-bottom: 2rem;
}

.content {
  display: grid;
  gap: 2rem;
}

.form {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin: 0 auto;
  gap: 1rem;
}

.label {
  display: grid;
  gap: 0.2rem;
  max-width: 20rem;
  text-align: left;
  font-size: 1rem;
}

.input {
  padding: 0.4rem;
}

.button {
  padding: 0.4rem;
}

.list {
  list-style: none;
  padding: 0;
  padding-top: 3rem;
  margin: 0;
  display: flex;
  gap: 2rem;
}

.list > li {
  max-width: 20rem;
  text-align: left;
}

@media only screen and (max-width: 50rem) {
  .list {
    display: block;
  }

  .list > li {
    padding-bottom: 1rem;
  }
}

