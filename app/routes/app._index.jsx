import {
  Page,
  Layout,
  Text,
  Card,
  BlockStack,
  List,
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { getSetupFlag } from "../models/IsSetup.server";

export const loader = async ({ request }) => {
  console.log(`[Home Loader] Starting loader execution`);

  // Import server-only functions inside the loader
  const { authenticateMultistore } = await import("../shopify.multistore.server");

  // Use multistore authentication
  const result = await authenticateMultistore(request);

  // If result is a Response (redirect), throw it to let Remix handle it
  if (result instanceof Response) {
    console.log(`[Home Loader] Got redirect response, throwing it`);
    throw result;
  }

  const { session } = result;
  console.log(`[Home Loader] Authenticated for shop: ${session.shop}`);

  try {
    // Check if the shop is already set up
    let isSetup = await getSetupFlag(session.shop);
    console.log(`[Home Loader] Setup flag status:`, isSetup);

    // If the shop is not set up, initialize it
    if (isSetup === null) {
      console.log(`[Home Loader] No setup flag found, initializing store...`);
      try {
        // Get setup service from server container (since setupService is only available there)
        const { container } = await import("../lib/container/ServiceContainer.server.js");
        const setupService = await container.resolve('setupService');
        let response = await setupService.initializeShop(session.shop);
        console.log(`[Home Loader] Store initialization response:`, response);
      } catch (setupError) {
        console.error(`[Home Loader] Setup service error (non-fatal):`, setupError);
        // Continue execution - this is not critical for app startup
      }
    }
  } catch (error) {
    console.error(`[Home Loader] Error (non-fatal):`, error);
    // Don't throw - let the app continue to load
  }

  // Always return null to render the index page
  return null;
};

export default function Index() {
  console.log(`[Index Component] Rendering index page`);

  return (
    <Page>
      <TitleBar title="AUI Production Tools" />
      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="500">
                <img src="AUI_Logo_2020.webp" alt="Americans United Inc logo" width="100"></img>
                <Text as="h2" variant="headingMd">Print-On-Demand tools for your Shopify Admin</Text>
                <Text as="p">Thanks for installing our app! This app is still in beta, so our tools are only available to current clients.</Text>
                <Text as="p">Stay tuned for updates, and please provide any feedback on app <NAME_EMAIL></Text>
              </BlockStack>
              <br />
              <BlockStack gap="500">
                <Text as="h3" variant="headingMd">Current Features</Text>
                <List>
                  <List.Item><Text as="span" variant="headingSm">Invoice Previews</Text> - <Text as="span">View a preview of your monthly invoice balances, based off real-time order data.</Text></List.Item>
                </List>
                <Text as="h3" variant="headingMd">Planned Upcoming Features</Text>
                <List>
                  <List.Item><Text as="span" variant="headingSm">Product Creation</Text> - <Text as="span">Pick one of our blank offerings, add your art, and start selling</Text></List.Item>
                  <List.Item><Text as="span" variant="headingSm">Order Statuses and Metrics</Text> - <Text as="span">View live fulfillment metrics, view statuses of specific orders, and more</Text></List.Item>
                </List>
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}
