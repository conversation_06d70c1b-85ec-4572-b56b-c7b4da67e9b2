export const loader = async ({ request }) => {
  // Import server-only functions inside the loader
  const { authenticateMultistore } = await import("../shopify.multistore.server");

  // Use multistore authentication
  const result = await authenticateMultistore(request);

  // If result is a Response (redirect), throw it to let Remix handle it
  if (result instanceof Response) {
    throw result;
  }

  return null;
};
