/**
 * api.shipstation-sync.jsx
 *
 * This file handles the synchronization of ShipStation shipments to extract carrier fees.
 * It processes shipments from ShipStation and updates shipping costs in the database.
 */

import { updateShippingCost, updateFulfillmentCost } from "../models/ShippingCost.server";
import { getStoreMappingByStoreId } from "../models/ShipStationStoreMapping.server";
import { container } from "../lib/container/ServiceContainer.server.js";

export async function action({ request }) {
  if (request.method !== "POST") {
    return Response.json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Check for API key for authentication
    const apiKey = request.headers.get("X-API-Key");
    if (!apiKey || apiKey !== process.env.RECONCILIATION_API_KEY) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { storeId, startDate, endDate } = body;

    // Store these for later use in the response
    let formattedStartDate = startDate;
    let formattedEndDate = endDate;

    if (!storeId) {
      return Response.json({ error: "Store ID is required" }, { status: 400 });
    }

    // Get the store mapping from the database
    const storeMapping = await getStoreMappingByStoreId(storeId);

    if (!storeMapping) {
      return Response.json({ error: `No mapping found for store ID: ${storeId}. Please configure this store ID first.` }, { status: 400 });
    }

    const { shop } = storeMapping;

    // Fetch shipments from ShipStation - ignore the provided dates and use dynamic date range
    // Calculate the first day of the previous month
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();

    // Calculate previous month and year
    let previousMonth = currentMonth - 1;
    let previousYear = currentYear;
    if (previousMonth < 0) {
      previousMonth = 11; // December
      previousYear = currentYear - 1;
    }

    // First day of previous month
    const firstDayOfPreviousMonth = new Date(previousYear, previousMonth, 1);
    const firstDayOfPreviousMonthISO = firstDayOfPreviousMonth.toISOString();
    const nowISO = now.toISOString();

    console.log(`Syncing historical shipping data for store ${storeId} from ${previousMonth + 1}/1/${previousYear} (first day of previous month) to current`);

    console.log(`Using simplified request params: ${JSON.stringify({
      storeId: storeId,
      shipDateStart: firstDayOfPreviousMonthISO,
      shipDateEnd: nowISO,
      pageSize: 500,
      page: 1
    })}`);

    // Get services from container
    const shipstationOrderService = await container.resolve('shipstationOrderService');

    const shipments = await shipstationOrderService.fetchShipments(storeId, firstDayOfPreviousMonthISO, nowISO);
    const fetchedStartDate = firstDayOfPreviousMonthISO;
    const fetchedEndDate = nowISO;

    // Update the formatted dates with the actual values used in the fetch
    formattedStartDate = fetchedStartDate;
    formattedEndDate = fetchedEndDate;
    console.log(`Successfully fetched ${shipments.length} shipments`);

    if (shipments.length === 0) {
      console.log('No shipments found. This could be due to:');
      console.log('1. No shipments exist for the specified date range');
      console.log('2. The store ID is incorrect');
      console.log('3. The API key does not have access to this store');
      console.log('4. The date format is incorrect');

      // Return early with a more helpful message
      return Response.json({
        success: false,
        error: 'No shipments found for the specified store ID and date range',
        storeId,
        dateRange: { startDate, endDate }
      });
    }

    // Process shipments and calculate costs
    let totalShippingCost = 0;
    let processedShipments = 0;
    let skippedOldShipments = 0;

    // We already have currentMonth, previousMonth, and previousYear from above
    // No need to recalculate them here

    console.log(`Current month/year for filtering: ${currentMonth}/${currentYear}`);
    console.log(`Previous month/year for filtering: ${previousMonth}/${previousYear}`);

    for (const shipment of shipments) {
      // Check if the shipment has a valid created_at date

      // Use created_at as the only source for shipment date
      let shipDate = null;
      if (shipment.created_at) shipDate = new Date(shipment.created_at);
      else if (shipment.createDate) shipDate = new Date(shipment.createDate); // Fallback to createDate (same field, different naming convention)

      if (shipDate) {
        const shipMonth = shipDate.getMonth();
        const shipYear = shipDate.getFullYear();

        console.log(`Shipment date parsed: ${shipDate.toISOString()}, month: ${shipMonth}, year: ${shipYear}`);

        // Since we're already filtering by date range in the API request,
        // we don't need to filter again here. All shipments returned should be valid.
        // Just log the date for debugging purposes
        console.log(`PROCESSING shipment from ${shipMonth}/${shipYear}`);
      } else {
        console.log(`WARNING: No date found for shipment ${shipment.shipment_id || shipment.id || 'unknown'}, skipping`);
        skippedOldShipments++;
        continue;
      }
      // Extract shipping cost from the shipment
      let shippingCost = 0;
      const shipmentId = shipment.shipment_id || shipment.id || 'unknown';
      console.log(`Processing shipment ID: ${shipmentId}`);

      // Log only essential information to reduce memory usage
      console.log(`Processing shipment ID: ${shipmentId} with fields: ${Object.keys(shipment).join(', ')}`);

      // First, check if the shipment has a label ID
      const labelId = shipment.label_id || shipment.labelId;

      if (labelId) {
        console.log(`Found label ID: ${labelId} for shipment ${shipmentId}`);

        try {
          // Fetch the label data to get the shipping cost
          const labelData = await shipstationOrderService.fetchShipmentLabel(labelId);

          if (labelData && labelData.shipping_cost) {
            // Use the shipping_cost from the label
            const labelShippingCost = parseFloat(labelData.shipping_cost);
            if (!isNaN(labelShippingCost)) {
              shippingCost = labelShippingCost;
              console.log(`Using shipping_cost from label: $${shippingCost}`);
            }
          } else {
            console.log(`No shipping_cost found in label data for label ID: ${labelId}`);
          }
        } catch (labelError) {
          console.error(`Error fetching label data for label ID ${labelId}: ${labelError.message}`);
        }
      } else {
        console.log(`No label ID found for shipment ${shipmentId}, falling back to carrier_fee`);
      }

      // If we couldn't get the shipping cost from the label, fall back to carrier_fee
      if (shippingCost === 0) {
        // Log carrier_fee and shipping_paid fields if they exist
        if (shipment.carrier_fee) {
          console.log(`Carrier fee: ${JSON.stringify(shipment.carrier_fee)}`);
        }
        if (shipment.shipping_paid) {
          console.log(`Shipping paid: ${JSON.stringify(shipment.shipping_paid)}`);
        }

        // Use carrier_fee as a fallback source for shipping costs
        if (shipment.carrier_fee && shipment.carrier_fee.amount) {
          const carrierFeeCost = parseFloat(shipment.carrier_fee.amount || 0);
          shippingCost = carrierFeeCost;
          console.log(`Falling back to carrier_fee amount: $${carrierFeeCost}`);
        }
      }

      // If we still don't have a shipping cost, log a warning
      if (shippingCost === 0) {
        console.warn(`WARNING: No shipping cost found for shipment ${shipmentId}. This indicates a potential issue with the ShipStation API integration.`);
      }

      // Log the final shipping cost for this shipment
      console.log(`Processing shipment with final cost: $${shippingCost}`);

      if (shippingCost > 0) {
        // Update the shipping costs record with the shipment date
        await updateShippingCost(
          shop,
          storeId,
          shippingCost,
          10, // Default markup percentage
          shipDate // Pass the shipment date
        );

        // Also update fulfillment costs with the same shipment date
        await updateFulfillmentCost(
          shop,
          1.50,
          shipDate // Pass the shipment date
        );

        totalShippingCost += shippingCost;
        processedShipments++;
      }
    }

    return Response.json({
      success: true,
      processedShipments,
      skippedOldShipments,
      totalShippingCost,
      dateRange: {
        startDate: formattedStartDate,
        endDate: formattedEndDate
      }
    });
  } catch (error) {
    console.error(`Error syncing ShipStation data: ${error.message}`);

    // Log more detailed error information
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error(`Response status: ${error.response.status}`);
      console.error(`Response headers: ${JSON.stringify(error.response.headers)}`);
      console.error(`Response data: ${JSON.stringify(error.response.data)}`);
    } else if (error.request) {
      // The request was made but no response was received
      console.error(`No response received: ${error.request}`);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error(`Error setting up request: ${error.message}`);
    }

    return Response.json({
      success: false,
      error: error.message,
      details: error.response ? error.response.data : null
    }, { status: 500 });
  }
}

// Loader to check status
export async function loader({ request }) {
  try {
    // Check for API key
    const apiKey = request.headers.get("X-API-Key");
    if (!apiKey || apiKey !== process.env.RECONCILIATION_API_KEY) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    return Response.json({
      status: "ShipStation sync API is available",
      usage: "Send a POST request to sync shipping costs from ShipStation"
    });
  } catch (error) {
    return Response.json({ error: error.message }, { status: 500 });
  }
}
