/**
 * api.shipstation-stores.jsx
 *
 * This endpoint handles fetching and storing ShipStation stores.
 * It can be used to refresh the list of available ShipStation stores.
 */

import { authenticate } from "../shopify.server";
import { container } from "../lib/container/ServiceContainer.server.js";

export async function loader({ request }) {
  try {
    // Authenticate admin
    const { session } = await authenticate.admin(request);

    if (!session) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const refresh = url.searchParams.get('refresh') === 'true';
    const filter = url.searchParams.get('filter') || 'all'; // all, mapped, unmapped

    // Get services from container
    const shipstationApiService = await container.resolve('shipstationApiService');

    // If refresh is true, fetch stores from ShipStation API and update database
    if (refresh) {
      try {
        console.log('Fetching stores from ShipStation API');
        const apiStores = await shipstationApiService.fetchStores();

        // Format stores for database
        const formattedStores = apiStores.map(store => ({
          storeId: String(store.storeId),
          storeName: store.storeName
        }));

        // Bulk create or update stores
        await shipstationApiService.bulkCreateOrUpdateStores(formattedStores);
        console.log(`Updated ${formattedStores.length} ShipStation stores in database`);
      } catch (error) {
        console.error(`Error refreshing ShipStation stores: ${error.message}`);
        return Response.json({
          error: `Error refreshing ShipStation stores: ${error.message}`
        }, { status: 500 });
      }
    }

    // Get stores from database based on filter
    let stores = [];
    const allMappings = await shipstationApiService.getAllStoreMappings();

    switch (filter) {
      case 'mapped':
        stores = allMappings.filter(mapping => mapping.shop && mapping.shop.trim() !== '');
        break;
      case 'unmapped':
        stores = allMappings.filter(mapping => !mapping.shop || mapping.shop.trim() === '');
        break;
      case 'all':
      default:
        stores = allMappings;
        break;
    }

    return Response.json({
      stores,
      count: stores.length,
      filter
    });
  } catch (error) {
    console.error(`Error in ShipStation stores API: ${error.message}`);
    return Response.json({ error: error.message }, { status: 500 });
  }
}

export async function action({ request }) {
  if (request.method !== "POST") {
    return Response.json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Authenticate admin
    const { session } = await authenticate.admin(request);

    if (!session) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get services from container
    const shipstationApiService = await container.resolve('shipstationApiService');

    // Fetch stores from ShipStation API
    const apiStores = await shipstationApiService.fetchStores();

    // Format stores for database
    const formattedStores = apiStores.map(store => ({
      storeId: String(store.storeId),
      storeName: store.storeName
    }));

    // Bulk create or update stores
    const results = await shipstationApiService.bulkCreateOrUpdateStores(formattedStores);

    return Response.json({
      success: true,
      message: `Successfully updated ${results.created + results.updated} ShipStation stores`,
      results
    });
  } catch (error) {
    console.error(`Error updating ShipStation stores: ${error.message}`);
    return Response.json({ error: error.message }, { status: 500 });
  }
}
