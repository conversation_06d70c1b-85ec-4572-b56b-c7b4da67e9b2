// Import multistore authentication instead of standard authentication
// import { authenticate } from "../shopify.server";
import { createOrUpdateStoreMapping, getStoreMappingsByShop, getStoreMappingByStoreId } from "../models/ShipStationStoreMapping.server";
import { updateShippingCost, updateFulfillmentCost } from "../models/ShippingCost.server";
import { container } from "../lib/container/ServiceContainer.server.js";
import { processLargeDataset } from "../../shared/utils/processLargeDataset.js";
import { isMemoryUsageHigh, getMemoryUsage } from "../../shared/utils/index.js";
import { v4 as uuidv4 } from 'uuid';
import { updateShipStationProgress, completeShipStationJob, failShipStationJob } from "./api.shipstation-progress";
import { config, getStoreConfig } from "../lib/config/index.js";
import jwt from 'jsonwebtoken';

/**
 * api.shipstation-mapping.jsx
 *
 * This file handles the mapping of ShipStation stores to Shopify shops.
 * It creates the mapping and syncs historical shipping data.
 */

export async function action({ request }) {
  console.log('=== SHIPSTATION MAPPING ACTION START ===');
  console.log('Request method:', request.method);
  console.log('Request URL:', request.url);

  if (request.method !== "POST") {
    console.log('Method not allowed:', request.method);
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      status: 405,
      headers: { "Content-Type": "application/json" }
    });
  }

  try {
    console.log('Starting multistore authentication...');
    console.log('Request headers:', Object.fromEntries(request.headers.entries()));
    console.log('Request URL:', request.url);
    console.log('Request method:', request.method);

    // Use multistore authentication instead of standard authentication
    const { authenticateMultistore } = await import("../shopify.multistore.server");

    let session;
    try {
      const authResult = await authenticateMultistore(request);
      session = authResult.session;
      console.log('Multistore authentication successful');
      console.log('Authentication session:', session ? 'SUCCESS' : 'FAILED');
      console.log('Authenticated shop:', session?.shop);
      console.log('Session details:', {
        id: session?.id,
        shop: session?.shop,
        state: session?.state,
        isOnline: session?.isOnline
      });
    } catch (authError) {
      console.log('Multistore authentication error:', authError);
      console.log('Authentication error message:', authError.message);
      console.log('Authentication error stack:', authError.stack);
      console.log('Authentication error status:', authError.status);
      console.log('Authentication error response:', authError.response);
      throw authError;
    }

    if (!session) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Parse the request body
    console.log('Parsing request body...');
    let body;
    const contentType = request.headers.get("Content-Type") || "";
    console.log('Content-Type:', contentType);

    if (contentType.includes("application/json")) {
      console.log('Parsing as JSON...');
      body = await request.json();
      console.log('JSON body parsed:', body);
    } else {
      console.log('Parsing as form data...');
      // Handle form data
      const formData = await request.formData();
      console.log('Form data parsed successfully');
      body = {
        shop: formData.get("shop"),
        storeId: formData.get("storeId"),
        jobId: formData.get("jobId"),
        syncHistoricalData: formData.get("syncHistoricalData") === "true"
      };
      console.log('Form body constructed:', body);
    }

    const { shop, storeId, jobId: existingJobId, syncHistoricalData = false } = body;

    // Debug logging
    console.log('Received mapping request body:', body);
    console.log('Extracted values:', { shop, storeId, existingJobId, syncHistoricalData });

    // Validate required fields
    if (!shop) {
      return new Response(JSON.stringify({ error: "Shop is required" }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    if (!storeId) {
      return new Response(JSON.stringify({ error: "Store ID is required" }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Generate a unique job ID for tracking progress or use the provided one
    const jobId = existingJobId || uuidv4();
    console.log('Using job ID:', jobId, existingJobId ? '(provided)' : '(generated)');

    // Initialize progress tracking
    console.log('Initializing progress tracking for job:', jobId);
    updateShipStationProgress(jobId, {
      status: 'initializing',
      message: 'Starting ShipStation mapping process...',
      progress: 5
    });
    console.log('Progress tracking initialized. Current global state:', Object.keys(global.shipstationProgress));

    // Check if this is a new mapping or an update
    const existingMapping = await getStoreMappingByStoreId(storeId);
    const isNewMapping = !existingMapping;

    updateShipStationProgress(jobId, {
      status: 'creating_mapping',
      message: isNewMapping ? 'Creating new store mapping...' : 'Updating existing store mapping...',
      progress: 15
    });

    // Create or update the store mapping
    // Use the client shop from the request, not the admin shop
    console.log(`Creating/updating store mapping for shop: ${shop} (admin shop: ${session.shop})`);
    const mapping = await createOrUpdateStoreMapping(
      storeId,
      shop
    );

    updateShipStationProgress(jobId, {
      status: 'mapping_created',
      message: 'Store mapping saved successfully',
      progress: 20
    });

    // If this is a new mapping or syncHistoricalData is explicitly requested,
    // we'll need to fetch historical shipping data
    // But we'll do it asynchronously and return a response immediately
    console.log(`isNewMapping: ${isNewMapping}, syncHistoricalData: ${syncHistoricalData}`);
    if (isNewMapping || syncHistoricalData) {
      // Calculate the first day of the previous month
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth();

      // Calculate previous month and year
      let previousMonth = currentMonth - 1;
      let previousYear = currentYear;
      if (previousMonth < 0) {
        previousMonth = 11; // December
        previousYear = currentYear - 1;
      }

      // First day of previous month
      const startDate = new Date(previousYear, previousMonth, 1);
      const endDate = new Date(); // Today

      updateShipStationProgress(jobId, {
        status: 'preparing',
        message: `Preparing to sync historical shipping data from ${previousMonth + 1}/1/${previousYear} to current...`,
        progress: 25
      });

      console.log(`Starting historical data sync for job ${jobId}`);

      // Start the sync process in the background without awaiting it
      // This allows us to return a response immediately
      // Use the client shop from the request, not the admin shop
      processHistoricalDataAsync(storeId, shop, startDate, endDate, jobId);
    } else {
      // No sync needed, job is complete
      completeShipStationJob(jobId, {
        message: 'Store mapping updated successfully, no historical data sync needed'
      });
    }

    // Return a response immediately with the job ID
    return new Response(JSON.stringify({
      success: true,
      mapping,
      jobId,
      message: isNewMapping ?
        "Store mapping created. Historical data sync started. Check progress with the provided job ID." :
        "Store mapping updated successfully."
    }), {
      headers: { "Content-Type": "application/json" }
    });
  } catch (error) {
    console.error(`Error saving store mapping: ${error.message}`);
    // If we have a jobId in the error, use it, otherwise we can't update progress
    if (error.jobId) {
      failShipStationJob(error.jobId, error.message);
    }
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
}

export async function loader({ request }) {
  try {
    const { session } = await authenticate.admin(request);

    if (!session) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }

    // Get all store mappings for this shop
    const mappings = await getStoreMappingsByShop(session.shop);

    return new Response(JSON.stringify({
      mappings
    }), {
      headers: { "Content-Type": "application/json" }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
}

/**
 * Process historical data asynchronously without blocking the response
 * @param {string} storeId - The ShipStation store ID
 * @param {string} shop - The shop identifier
 * @param {Date} startDate - Start date for fetching shipments
 * @param {Date} endDate - End date for fetching shipments
 * @param {string} jobId - The job ID for progress tracking
 */
async function processHistoricalDataAsync(storeId, shop, startDate, endDate, jobId) {
  try {
    console.log(`Processing historical data asynchronously for job ${jobId}`);
    console.log(`Shop: ${shop}, StoreId: ${storeId}, Date Range: ${startDate.toISOString()} to ${endDate.toISOString()}`);
    console.log(`API Key available: ${!!process.env.SHIPSTATION_API_KEY}`);

    // Validate inputs
    if (!storeId) {
      throw new Error("Store ID is required for historical data sync");
    }
    if (!shop) {
      throw new Error("Shop is required for historical data sync");
    }
    if (!jobId) {
      throw new Error("Job ID is required for historical data sync");
    }

    // Initial progress update
    updateShipStationProgress(jobId, {
      status: 'starting',
      message: 'Starting historical data processing...',
      progress: 10
    });

    // Small delay to ensure the client receives the initial progress update
    await new Promise(resolve => setTimeout(resolve, 500));

    // Update progress before starting the sync - force progress to 10%
    updateShipStationProgress(jobId, {
      status: 'preparing',
      message: 'Preparing to fetch data from ShipStation...',
      progress: 10
    });

    console.log(`Updated progress to 10% for job ${jobId}`);
    console.log(`Current progress data:`, global.shipstationProgress[jobId]);

    // Small delay to ensure the client receives the progress update
    await new Promise(resolve => setTimeout(resolve, 1000));

    const syncResults = await syncHistoricalShippingData(storeId, shop, startDate, endDate, jobId);

    // Update progress with the final results
    completeShipStationJob(jobId, {
      message: `Completed processing ${syncResults.processedLabels} labels with total cost of $${syncResults.totalShippingCost.toFixed(2)}`,
      processedItems: syncResults.processedLabels,
      totalItems: syncResults.processedLabels,
      totalCost: syncResults.totalShippingCost
    });

    console.log(`Async processing completed for job ${jobId}`);
  } catch (error) {
    console.error(`Error in async processing for job ${jobId}: ${error.message}`);
    failShipStationJob(jobId, `Error syncing historical data: ${error.message}`);
  }
}

/**
 * Syncs historical shipping data from ShipStation
 * @param {string} storeId - The ShipStation store ID
 * @param {string} shop - The shop identifier
 * @param {Date} startDate - Start date for fetching shipments
 * @param {Date} endDate - End date for fetching shipments
 * @param {string} jobId - The job ID for progress tracking
 * @returns {Promise<object>} - Results of the sync
 */
async function syncHistoricalShippingData(storeId, shop, startDate, endDate, jobId) {
  const API_KEY = process.env.SHIPSTATION_API_KEY;

  console.log(`Starting syncHistoricalShippingData for store ${storeId}, shop ${shop}`);
  console.log(`Date range: ${startDate.toISOString()} to ${endDate.toISOString()}`);
  console.log(`Job ID: ${jobId}`);
  console.log(`API Key available: ${!!API_KEY}`);

  if (!API_KEY) {
    throw new Error("ShipStation API key is not configured");
  }

  // Format dates for ShipStation API
  const formattedStartDate = startDate.toISOString();
  const formattedEndDate = endDate.toISOString();

  try {
    console.log(`Syncing historical shipping data for store ${storeId} from ${formattedStartDate} to ${formattedEndDate}`);

    // Update progress
    if (jobId) {
      updateShipStationProgress(jobId, {
        status: 'fetching_labels',
        message: 'Fetching labels from ShipStation...',
        progress: 30
      });
    }

    // Get services from container
    const shipstationApiService = await container.resolve('shipstationApiService');

    // Fetch labels directly from the labels endpoint with progress tracking
    const { labels } = await shipstationApiService.fetchLabels(
      storeId,
      formattedStartDate,
      formattedEndDate,
      API_KEY,
      {
        jobId,
        updateProgress: updateShipStationProgress
      }
    );

    console.log(`Successfully fetched ${labels.length} labels`);

    if (jobId) {
      updateShipStationProgress(jobId, {
        status: 'labels_fetched',
        message: `Successfully fetched ${labels.length} labels`,
        progress: 40,
        totalItems: labels.length,
        processedItems: 0
      });
    }

    if (labels.length === 0) {
      console.log('No labels found. This could be due to:');
      console.log('1. No labels exist for the specified date range');
      console.log('2. The store ID is incorrect');
      console.log('3. The API key does not have access to this store');
      console.log('4. The date format is incorrect');

      if (jobId) {
        updateShipStationProgress(jobId, {
          status: 'no_labels',
          message: 'No labels found for the specified date range',
          progress: 100,
          totalItems: 0,
          processedItems: 0
        });
      }

      return {
        processedLabels: 0,
        totalShippingCost: 0
      };
    }

    // Process labels and calculate costs
    let totalShippingCost = 0;
    let processedLabels = 0;

    // Process labels in smaller batches to provide more frequent progress updates
    const batchSize = 50; // Smaller batch size for more frequent progress updates
    const totalLabels = labels.length;
    const totalBatches = Math.ceil(totalLabels / batchSize);

    console.log(`Processing ${totalLabels} labels in ${totalBatches} batches of ${batchSize}`);

    if (jobId) {
      updateShipStationProgress(jobId, {
        status: 'processing',
        message: `Processing ${totalLabels} labels in ${totalBatches} batches`,
        progress: 45,
        totalItems: totalLabels,
        processedItems: 0
      });
    }

    for (let i = 0; i < totalLabels; i += batchSize) {
      const batchNumber = Math.floor(i / batchSize) + 1;
      const batchStartTime = Date.now();
      const batchEnd = Math.min(i + batchSize, totalLabels);

      console.log(`Starting batch ${batchNumber}/${totalBatches} (labels ${i + 1}-${batchEnd})`);

      if (jobId) {
        // Calculate progress percentage - ensure it's between 45% and 95%
        const progressPercent = Math.min(95, Math.max(45, 45 + Math.floor((i / totalLabels) * 50)));
        console.log(`Updating progress for batch ${batchNumber}/${totalBatches}: ${progressPercent}%`);

        updateShipStationProgress(jobId, {
          status: 'processing_batch',
          message: `Processing batch ${batchNumber}/${totalBatches} (labels ${i + 1}-${batchEnd})`,
          progress: progressPercent,
          totalItems: totalLabels,
          processedItems: i
        });
      }

      const batch = labels.slice(i, batchEnd);

      // Update progress at the start of each label in the batch
      let labelIndex = 0;
      for (const label of batch) {
        // Update progress more frequently (every 2 labels)
        if (jobId && labelIndex % 2 === 0) {
          const overallProgress = Math.min(95, Math.max(45, 45 + Math.floor(((i + labelIndex) / totalLabels) * 50)));

          // Get label ID for more detailed progress message
          const labelId = label.label_id || label.id || 'unknown';

          // Get ship date if available for more context
          let shipDateStr = '';
          if (label.ship_date) {
            try {
              const shipDate = new Date(label.ship_date);
              shipDateStr = ` (shipped ${shipDate.toLocaleDateString()})`;
            } catch (e) {
              // Ignore date parsing errors
            }
          }

          updateShipStationProgress(jobId, {
            status: 'processing_label',
            message: `Processing label ${i + labelIndex + 1}/${totalLabels}: ID ${labelId}${shipDateStr}`,
            progress: overallProgress,
            totalItems: totalLabels,
            processedItems: i + labelIndex
          });
        }
        labelIndex++;
        // Check if the label is voided
        if (label.status && label.status.toLowerCase() === "voided") {
          console.log(`Skipping voided label: ${label.label_id || label.id || 'unknown'}`);
          continue;
        }

        // Extract shipping cost from the label
        let shippingCost = 0;
        const labelId = label.label_id || label.id || 'unknown';

        try {
          // Log only essential information to reduce memory usage
          console.log(`Processing label ID: ${labelId}`);

          // Get the shipping cost directly from the label
          if (label.shipment_cost && label.shipment_cost.amount) {
            const labelShippingCost = parseFloat(label.shipment_cost.amount);

            if (!isNaN(labelShippingCost)) {
              shippingCost = labelShippingCost;
              console.log(`Using shipment_cost.amount from label: $${shippingCost}`);
            }
          } else if (label.shipment_cost) {
            // Try to use the shipment_cost directly if it's a number or string
            if (typeof label.shipment_cost === 'number' || typeof label.shipment_cost === 'string') {
              const directCost = parseFloat(label.shipment_cost);
              if (!isNaN(directCost)) {
                shippingCost = directCost;
                console.log(`Using direct shipment_cost value: $${shippingCost}`);
              }
            }
          } else {
            // Check for other possible field names
            const possibleFields = ['shipmentCost', 'shipping_cost', 'shippingCost', 'cost', 'amount', 'price', 'rate', 'charge'];
            for (const field of possibleFields) {
              if (label[field]) {
                const altCost = parseFloat(label[field]);
                if (!isNaN(altCost)) {
                  shippingCost = altCost;
                  console.log(`Using alternative field ${field} for cost: $${shippingCost}`);
                  break;
                }
              }
            }
          }
        } catch (error) {
          console.error(`Error processing label ${labelId}: ${error.message}`);
        }

        let labelDate = null;
        const dateField = label.created_at || label.createDate;

        if (dateField) {
          labelDate = new Date(dateField);

          // Validate the label date
          if (isNaN(labelDate) || !isFinite(labelDate)) {
            labelDate = null;
          }
        }

        if (shippingCost > 0) {
          // Update the shipping costs record with the label date
          console.log(`Updating shipping cost for shop: ${shop}, storeId: ${storeId}, cost: ${shippingCost}, date: ${labelDate?.toISOString() || 'null'}`);
          await updateShippingCost(
            shop,
            storeId,
            shippingCost,
            10, // Default markup percentage
            labelDate // Pass the label date
          );

          // Also update fulfillment costs with the same label date
          console.log(`Updating fulfillment cost for shop: ${shop}, cost: 1.50, date: ${labelDate?.toISOString() || 'null'}`);
          await updateFulfillmentCost(
            shop,
            1.50,
            labelDate // Pass the label date
          );

          totalShippingCost += shippingCost;
          processedLabels++;
        }
      }

      const batchDuration = Date.now() - batchStartTime;
      console.log(`Batch ${batchNumber}/${totalBatches} completed in ${batchDuration}ms. Processed ${batch.length} labels (total processed: ${processedLabels})`);

      if (jobId) {
        // Calculate progress based on completed batches and processed labels
        // Force progress to increase with each batch
        const batchProgress = Math.min(95, Math.max(10, 10 + Math.floor((batchNumber / totalBatches) * 85)));
        console.log(`Batch ${batchNumber}/${totalBatches} complete. Updating progress: ${batchProgress}%`);

        // Add a small delay to ensure progress updates are sent separately
        await new Promise(resolve => setTimeout(resolve, 100));

        // Calculate total cost so far for more detailed progress message
        const totalCostSoFar = totalShippingCost.toFixed(2);

        updateShipStationProgress(jobId, {
          status: 'batch_complete',
          message: `Completed batch ${batchNumber}/${totalBatches}. Processed ${processedLabels}/${totalLabels} labels ($${totalCostSoFar} total).`,
          progress: batchProgress,
          totalItems: totalLabels,
          processedItems: processedLabels
        });

        // Log the current progress data to verify it's being updated
        console.log(`Current progress data for job ${jobId}:`, global.shipstationProgress[jobId]);
      }
    }

    // Final progress update before completion
    if (jobId) {
      updateShipStationProgress(jobId, {
        status: 'finalizing',
        message: `Processed all ${processedLabels} labels with total cost of $${totalShippingCost.toFixed(2)}`,
        progress: 95,
        totalItems: totalLabels,
        processedItems: processedLabels
      });
    }

    // Log a few sample labels to help with debugging
    if (labels.length > 0) {
      console.log('Sample label structure:');
      const sampleLabel = labels[0];
      console.log(`Keys: ${Object.keys(sampleLabel).join(', ')}`);
      console.log(`shipment_id: ${sampleLabel.shipment_id}`);
      console.log(`created_at: ${sampleLabel.created_at || sampleLabel.createDate}`);
      console.log(`ship_date: ${sampleLabel.ship_date}`);
      console.log(`shipment_cost: ${JSON.stringify(sampleLabel.shipment_cost)}`);
    }

    return {
      processedLabels,
      processedShipments: processedLabels, // For backward compatibility
      totalShippingCost
    };
  } catch (error) {
    console.error(`Error fetching historical ShipStation shipments: ${error.message}`);

    // Log more detailed error information
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error(`Response status: ${error.response.status}`);
      console.error(`Response headers: ${JSON.stringify(error.response.headers)}`);
      console.error(`Response data: ${JSON.stringify(error.response.data)}`);
    } else if (error.request) {
      // The request was made but no response was received
      console.error(`No response received: ${error.request}`);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error(`Error setting up request: ${error.message}`);
    }

    throw error;
  }
}
