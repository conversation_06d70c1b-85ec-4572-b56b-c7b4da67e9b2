/**
 * app.shipstation.jsx
 *
 * This page allows admin users to manage ShipStation store mappings for client shops.
 * It displays a list of client shops and allows mapping them to ShipStation stores.
 */

import {
  Card,
  Layout,
  Page,
  Select,
  Text,
  BlockStack,
  Button,
  Banner,
  Modal,
  DataTable,
  EmptyState
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { useState, useCallback, useEffect } from 'react';

import { useLoaderData, useActionData, useNavigate } from "@remix-run/react";
import { v4 as uuidv4 } from 'uuid';

// No custom hook needed - we'll use polling only

/**
 * Loader function to fetch all shops and ShipStation stores
 */
export async function loader({ request }) {
  console.log("[ShipStation Loader] Starting loader execution");

  // Import server-only functions inside the loader
  const { authenticateMultistore } = await import("../shopify.multistore.server");
  const { getShops } = await import("../models/Shop.server");
  const {
    getAllStoreMappings,
    getStoreMappingsByShop,
  } = await import("../models/ShipStationStoreMapping.server");
  const { container } = await import("../lib/container/ServiceContainer.server.js");

  // Use multistore authentication
  const result = await authenticateMultistore(request);

  // If result is a Response (redirect), throw it to let Remix handle it
  if (result instanceof Response) {
    throw result;
  }

  const { session } = result;
  console.log(`[ShipStation Loader] Authenticated for shop: ${session.shop}`);

  // Get admin shop from environment variable
  const adminShop = process.env.ADMIN_SHOP;
  console.log(`[ShipStation Loader] Admin shop: ${adminShop}`);

  // Get all shops
  const shops = await getShops();
  console.log(`[ShipStation Loader] Found ${shops.length} shops`);

  // Filter out the admin shop
  const clientShops = shops.filter(shop => shop.shop !== adminShop);
  console.log(`[ShipStation Loader] Found ${clientShops.length} client shops after filtering out admin shop`);

  // Get all ShipStation store mappings
  let shipStationStores = await getAllStoreMappings();
  console.log(`[ShipStation Loader] Found ${shipStationStores.length} ShipStation store mappings`);

  // Always fetch the latest stores from the API to ensure we have all available stores
  try {
    console.log('[ShipStation Loader] Fetching stores from ShipStation API');

    // Get services from container
    const shipstationApiService = await container.resolve('shipstationApiService');

    const apiStores = await shipstationApiService.fetchStores();
    console.log(`[ShipStation Loader] Fetched ${apiStores.length} stores from ShipStation API`);

    // Create or update stores in the database
    const formattedStores = apiStores.map(store => ({
      storeId: String(store.storeId),
      storeName: store.storeName
    }));

    await shipstationApiService.bulkCreateOrUpdateStores(formattedStores);
    console.log(`[ShipStation Loader] Updated ${formattedStores.length} stores in database`);

    // Get all store mappings again after the update
    shipStationStores = await getAllStoreMappings();
    console.log(`[ShipStation Loader] Retrieved ${shipStationStores.length} store mappings after update`);
  } catch (error) {
    console.error(`[ShipStation Loader] Error fetching/updating ShipStation stores: ${error.message}`);
    // Continue with existing stores from database
  }

  // Get mappings for each client shop
  const shopMappings = {};
  for (const shop of clientShops) {
    const mappings = await getStoreMappingsByShop(shop.shop);
    shopMappings[shop.shop] = mappings;
  }

  return {
    shops: clientShops, // Return only client shops, not the admin shop
    shipStationStores,
    shopMappings,
    adminShop
  };
}

/**
 * Action function to handle mapping submissions
 */
export async function action({ request }) {
  if (request.method !== "POST") {
    return { error: "Method not allowed" };
  }

  try {
    // Import server-only functions inside the action
    const { authenticateMultistore } = await import("../shopify.multistore.server");

    // Use multistore authentication
    const result = await authenticateMultistore(request);

    // If result is a Response (redirect), throw it to let Remix handle it
    if (result instanceof Response) {
      throw result;
    }

    const formData = await request.formData();
    const shop = formData.get('shop');
    const storeId = formData.get('storeId');

    if (!shop || !storeId) {
      return { error: "Shop and store ID are required" };
    }

    // Generate a unique job ID for tracking progress
    const jobId = uuidv4();

    // Return the job ID immediately so the client can start tracking progress
    return {
      success: true,
      message: "Mapping process started",
      jobId,
      shop,
      storeId
    };
  } catch (error) {
    console.error(`Error in ShipStation mapping action: ${error.message}`);
    return { error: error.message };
  }
}

/**
 * Main component for the ShipStation management page
 */
export default function ShipStationPage() {
  const { shops, shipStationStores, shopMappings, adminShop } = useLoaderData();
  const actionData = useActionData();
  const navigate = useNavigate();

  // State for managing the UI
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedMappings, setSelectedMappings] = useState({});
  const [showWarningModal, setShowWarningModal] = useState(false);
  const [currentShop, setCurrentShop] = useState(null);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Progress tracking state
  const [jobId, setJobId] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [progressMessage, setProgressMessage] = useState("");

  // Initialize selected mappings from existing mappings
  useEffect(() => {
    console.log(`ShipStation Page: Admin shop is ${adminShop}, excluded from mapping options`);

    const initialMappings = {};
    shops.forEach(shop => {
      const shopDomain = shop.shop;
      const mapping = shopMappings[shopDomain]?.[0];
      if (mapping) {
        initialMappings[shopDomain] = mapping.storeId;
      } else {
        initialMappings[shopDomain] = "";
      }
    });
    setSelectedMappings(initialMappings);
  }, [shops, shopMappings, adminShop]);

  // Handle action data updates
  useEffect(() => {
    if (actionData) {
      if (actionData.error) {
        setError(actionData.error);
        setIsProcessing(false);
      } else if (actionData.success) {
        setSuccess(actionData.message);
        if (actionData.jobId) {
          setJobId(actionData.jobId);
          setIsProcessing(true);
        }
      }
    }
  }, [actionData]);

  // Use polling for progress tracking
  useEffect(() => {
    if (!jobId || !isProcessing) return;

    // Function to poll for progress updates
    const pollForProgress = async () => {
      try {
        console.log(`Polling for progress updates for job ${jobId}`);
        const response = await fetch(`/api/shipstation-progress?jobId=${jobId}&poll=true`, {
          credentials: 'include'
        });
        const data = await response.json();

        console.log('Received polling data:', data);

        // Update progress state
        setProgress(data.progress || 0);
        setProgressMessage(data.message || "Processing...");

        // Check if the process is complete
        if (data.complete) {
          setIsProcessing(false);

          if (data.status === 'complete') {
            setSuccess(`Successfully mapped ShipStation store. ${data.message || ''}`);

            // Add a small delay before refreshing the page
            setTimeout(() => {
              console.log('Refreshing page after successful mapping completion');
              // Use navigate instead of window.location.reload to preserve authentication
              navigate('.', { replace: true });
            }, 1500);
          } else if (data.status === 'failed') {
            setError(`Failed to map ShipStation store: ${data.error || 'Unknown error'}`);
          }

          return true; // Polling should stop
        }

        return false; // Polling should continue
      } catch (error) {
        console.error('Error polling for progress:', error);
        return false; // Polling should continue despite error
      }
    };

    // Poll immediately when the component mounts or jobId/isProcessing changes
    pollForProgress();

    // Set up polling interval (every 1 second)
    const pollInterval = setInterval(async () => {
      const shouldStop = await pollForProgress();
      if (shouldStop) {
        clearInterval(pollInterval);
      }
    }, 1000); // Poll every 1 second

    // Clean up interval on unmount
    return () => clearInterval(pollInterval);
  }, [jobId, isProcessing, navigate]);

  // Handle refreshing ShipStation stores
  const handleRefreshStores = useCallback(async () => {
    setIsRefreshing(true);
    setError(null);

    try {
      const response = await fetch('/api/shipstation-stores?refresh=true', {
        credentials: 'include'
      });
      const data = await response.json();

      if (data.error) {
        setError(data.error);
      } else {
        setSuccess(`Successfully refreshed ShipStation stores. Found ${data.count} stores.`);
        // Use navigate instead of window.location.reload to preserve authentication
        navigate('.', { replace: true });
      }
    } catch (error) {
      setError(`Error refreshing ShipStation stores: ${error.message}`);
    } finally {
      setIsRefreshing(false);
    }
  }, [navigate]);

  // Handle mapping selection change
  const handleMappingChange = useCallback((shopDomain, storeId) => {
    // Ignore the special "mapped-to-others-note" value
    if (storeId === 'mapped-to-others-note') {
      return;
    }

    setSelectedMappings(prev => ({
      ...prev,
      [shopDomain]: storeId
    }));
  }, []);

  // Handle mapping submission
  const handleSubmitMapping = useCallback((shopDomain) => {
    const storeId = selectedMappings[shopDomain];
    if (!storeId) {
      setError("Please select a ShipStation store to map");
      return;
    }

    // Check if this shop already has a mapping
    const existingMapping = shopMappings[shopDomain]?.[0];
    if (existingMapping) {
      setError("This shop already has a ShipStation mapping. Mappings cannot be changed once created.");
      return;
    }

    // Show warning modal
    setCurrentShop(shopDomain);
    setShowWarningModal(true);
  }, [selectedMappings, shopMappings]);

  // Handle confirming the mapping
  const handleConfirmMapping = useCallback(() => {
    if (!currentShop) return;

    const storeId = selectedMappings[currentShop];
    if (!storeId) return;

    // Close the modal
    setShowWarningModal(false);

    // Generate a job ID for tracking progress
    const newJobId = uuidv4();
    setJobId(newJobId);

    // Submit the form
    const formData = new FormData();
    formData.append('shop', currentShop);
    formData.append('storeId', storeId);
    formData.append('jobId', newJobId);
    formData.append('syncHistoricalData', 'true'); // Explicitly request historical data sync

    // Submit to the ShipStation mapping API endpoint
    fetch('/api/shipstation-mapping', {
      method: 'POST',
      body: formData,
      credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
      if (data.error) {
        setError(data.error);
        setIsProcessing(false);
      } else {
        setSuccess(data.message);
        setIsProcessing(true);
      }
    })
    .catch(error => {
      setError(`Error submitting mapping: ${error.message}`);
      setIsProcessing(false);
    });

    // Set processing state
    setIsProcessing(true);
  }, [currentShop, selectedMappings]);

  // Format shop name for display
  const formatShopName = useCallback((shopDomain) => {
    return shopDomain
      .replace('.myshopify.com', '')
      .replace(/-/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }, []);

  // Helper function to format store name
  const formatStoreName = useCallback((store) => {
    return store.storeName || `Store ${store.storeId}`;
  }, []);

  // Render progress bar
  const renderProgressBar = () => {
    return (
      <div style={{ marginTop: '20px' }}>
        <BlockStack gap="400">
          <Text variant="bodyMd">{progressMessage}</Text>
          <div style={{
            width: '100%',
            height: '8px',
            backgroundColor: '#e6e6e6',
            borderRadius: '4px',
            overflow: 'hidden'
          }}>
            <div style={{
              width: `${progress}%`,
              height: '100%',
              backgroundColor: '#008060',
              borderRadius: '4px',
              transition: 'width 0.3s ease-in-out'
            }} />
          </div>
          <Text variant="bodySm">{Math.round(progress)}% complete</Text>
        </BlockStack>
      </div>
    );
  };

  return (
    <Page
      title="ShipStation Integration"
      primaryAction={{
        content: 'Refresh ShipStation Stores',
        onAction: handleRefreshStores,
        loading: isRefreshing,
        disabled: isRefreshing || isProcessing
      }}
    >
      <TitleBar title="ShipStation Integration" />

      <Layout>
        <Layout.Section>
          {error && (
            <Banner status="critical" onDismiss={() => setError(null)}>
              <p>{error}</p>
            </Banner>
          )}

          {success && (
            <Banner status="success" onDismiss={() => setSuccess(null)}>
              <p>{success}</p>
            </Banner>
          )}

          <Card>
            <BlockStack gap="500">
              <Text variant="headingMd">Map Client Shops to ShipStation Stores</Text>

              <BlockStack gap="200">
                <Text variant="bodyMd">
                  This page allows you to map client shops to their corresponding ShipStation stores.
                  Once a mapping is created, it cannot be changed, so please ensure you select the correct store.
                </Text>
                <Text variant="bodySm" color="subdued">
                  Note: ShipStation stores are automatically refreshed each time you visit this page.
                  If you don't see a store, you can also use the "Refresh ShipStation Stores" button above.
                </Text>
              </BlockStack>

              {shipStationStores.length === 0 && (
                <EmptyState
                  heading="No ShipStation stores found"
                  action={{
                    content: 'Refresh ShipStation Stores',
                    onAction: handleRefreshStores,
                    loading: isRefreshing
                  }}
                  image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
                >
                  <p>
                    No ShipStation stores were found in the database. Click the button to fetch stores from the ShipStation API.
                  </p>
                </EmptyState>
              )}

              {shops.length === 0 ? (
                <EmptyState
                  heading="No client shops found"
                  image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
                >
                  <p>
                    No client shops have installed the app yet. Once shops install the app, they will appear here.
                  </p>
                </EmptyState>
              ) : (
                <DataTable
                  columnContentTypes={['text', 'text', 'text']}
                  headings={['Shop', 'ShipStation Store', 'Actions']}
                  rows={shops.map(shop => {
                    const shopDomain = shop.shop;
                    const existingMapping = shopMappings[shopDomain]?.[0];
                    const isProcessingThisShop = isProcessing && currentShop === shopDomain;

                    return [
                      <Text key={`shop-${shopDomain}`} variant="bodyMd" fontWeight="semibold">
                        {formatShopName(shopDomain)}
                      </Text>,
                      existingMapping ? (
                        <Text key={`store-${shopDomain}`} variant="bodyMd">
                          {formatStoreName(shipStationStores.find(s => s.storeId === existingMapping.storeId) || { storeId: existingMapping.storeId })}
                        </Text>
                      ) : isProcessingThisShop ? (
                        renderProgressBar()
                      ) : (
                        <Select
                          key={`select-${shopDomain}`}
                          options={(() => {
                            // Create shop-specific options for each shop
                            const options = [{ label: 'Select a ShipStation store', value: '' }];

                            // Count how many stores are mapped to other shops
                            let mappedToOtherShopsCount = 0;

                            shipStationStores.forEach(store => {
                              // Only include unmapped stores or stores mapped to this specific shop
                              if (!store.shop || store.shop === shopDomain) {
                                options.push({
                                  label: formatStoreName(store),
                                  value: store.storeId
                                });
                              } else {
                                // Count stores mapped to other shops
                                mappedToOtherShopsCount++;
                              }
                            });

                            // If there are stores mapped to other shops, add a note at the end
                            if (mappedToOtherShopsCount > 0) {
                              options.push({
                                label: `${mappedToOtherShopsCount} store(s) already mapped to other shops (not shown)`,
                                value: 'mapped-to-others-note',
                                disabled: true
                              });
                            }

                            return options;
                          })()}
                          onChange={(value) => handleMappingChange(shopDomain, value)}
                          value={selectedMappings[shopDomain] || ''}
                          disabled={false}
                        />
                      ),
                      existingMapping ? (
                        <Text key={`status-${shopDomain}`} variant="bodyMd" color="success">
                          Mapped
                        </Text>
                      ) : isProcessingThisShop ? (
                        <Text key={`status-${shopDomain}`} variant="bodyMd">
                          Processing...
                        </Text>
                      ) : (
                        <Button
                          key={`button-${shopDomain}`}
                          onClick={() => handleSubmitMapping(shopDomain)}
                          disabled={!selectedMappings[shopDomain] || isProcessing}
                        >
                          Map Store
                        </Button>
                      )
                    ];
                  })}
                />
              )}
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>

      {/* Warning Modal */}
      <Modal
        open={showWarningModal}
        onClose={() => setShowWarningModal(false)}
        title="Confirm ShipStation Mapping"
        primaryAction={{
          content: 'Confirm Mapping',
          onAction: handleConfirmMapping
        }}
        secondaryActions={[
          {
            content: 'Cancel',
            onAction: () => setShowWarningModal(false)
          }
        ]}
      >
        <Modal.Section>
          <BlockStack gap="400">
            <Text variant="bodyMd">
              <strong>Warning:</strong> Once a ShipStation mapping is created, it cannot be changed.
              Please ensure you have selected the correct store.
            </Text>

            {currentShop && (
              <Text variant="bodyMd">
                You are about to map <strong>{formatShopName(currentShop)}</strong> to ShipStation store{' '}
                <strong>{shipStationStores.find(s => s.storeId === selectedMappings[currentShop])?.storeName || selectedMappings[currentShop]}</strong>.
              </Text>
            )}

            <BlockStack gap="200">
              <Text variant="bodyMd">
                This will immediately begin syncing historical shipping data from ShipStation,
                which may take several minutes to complete.
              </Text>
              <Text variant="bodySm" color="subdued">
                The page will automatically refresh after the mapping is created. You will remain logged in.
              </Text>
            </BlockStack>
          </BlockStack>
        </Modal.Section>
      </Modal>
    </Page>
  );
}
