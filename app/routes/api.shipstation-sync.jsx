import { container } from "../lib/container/ServiceContainer.server.js";
import { processLargeDataset } from "../../shared/utils/processLargeDataset.js";
import { isMemoryUsageHigh, getMemoryUsage } from "../../shared/utils/index.js";

/**
 * api.shipstation-sync.jsx
 *
 * This file handles the synchronization of ShipStation shipments to extract carrier fees.
 * It processes shipments from ShipStation and updates shipping costs in the database.
 */

export async function action({ request }) {
  if (request.method !== "POST") {
    return Response.json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Check for API key for authentication
    const apiKey = request.headers.get("X-API-Key");
    if (!apiKey || apiKey !== process.env.RECONCILIATION_API_KEY) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { storeId, startDate, endDate } = body;

    // Store these for later use in the response
    let formattedStartDate = startDate;
    let formattedEndDate = endDate;

    if (!storeId) {
      return Response.json({ error: "Store ID is required" }, { status: 400 });
    }

    // Get services from container
    const shipstationApiService = await container.resolve('shipstationApiService');
    const shipstationOrderService = await container.resolve('shipstationOrderService');

    // Get the store mapping from the database
    const storeMappings = await shipstationApiService.getAllStoreMappings();
    const storeMapping = storeMappings.find(mapping => mapping.storeId === storeId);

    if (!storeMapping) {
      return Response.json({ error: `No mapping found for store ID: ${storeId}. Please configure this store ID first.` }, { status: 400 });
    }

    const { shop } = storeMapping;

    // Fetch shipments from ShipStation - use first day of previous month as start date
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();

    // Calculate previous month and year
    let previousMonth = currentMonth - 1;
    let previousYear = currentYear;
    if (previousMonth < 0) {
      previousMonth = 11; // December
      previousYear = currentYear - 1;
    }

    // First day of previous month
    const firstDayOfPreviousMonth = new Date(previousYear, previousMonth, 1);
    const firstDayOfPreviousMonthISO = firstDayOfPreviousMonth.toISOString();
    const nowISO = now.toISOString();

    console.log(`Syncing historical shipping data for store ${storeId} from ${previousMonth + 1}/1/${previousYear} (first day of previous month) to current`);

    console.log(`Using simplified request params: ${JSON.stringify({
      store_id: storeId,
      created_at_start: firstDayOfPreviousMonthISO,
      created_at_end: nowISO,
      page_size: 100,
      page: 1
    })}`);

    // Fetch labels directly from the labels endpoint
    const { labels, formattedStartDate: fetchedStartDate, formattedEndDate: fetchedEndDate } =
      await shipstationApiService.fetchLabels(storeId, firstDayOfPreviousMonthISO, nowISO, process.env.SHIPSTATION_API_KEY);

    // Update the formatted dates with the actual values used in the fetch
    formattedStartDate = fetchedStartDate;
    formattedEndDate = fetchedEndDate;
    console.log(`Successfully fetched ${labels.length} labels`);

    if (labels.length === 0) {
      console.log('No labels found. This could be due to:');
      console.log('1. No labels exist for the specified date range');
      console.log('2. The store ID is incorrect');
      console.log('3. The API key does not have access to this store');
      console.log('4. The date format is incorrect');

      // Return early with a more helpful message
      return Response.json({
        success: false,
        error: 'No labels found for the specified store ID and date range',
        storeId,
        dateRange: { startDate, endDate }
      });
    }

    // Process labels using the service
    const processingResult = await shipstationOrderService.processLabelsForShippingCosts(
      labels,
      shop,
      storeId,
      {
        progressCallback: (message) => {
          console.log(`Processing progress: ${message}`);

          // Check memory usage and warn if high
          if (isMemoryUsageHigh(0.8)) {
            console.warn('High memory usage detected during label processing:', getMemoryUsage());
          }
        },
        batchSize: 50
      }
    );

    const { totalShippingCost, processedLabels, skippedLabels: skippedOldLabels } = processingResult;

    // Get updated shipping cost record using the service
    const shippingService = container.resolve('shippingService');
    const shippingCost = await shippingService.getShippingCostByStore(shop, storeId);

    console.log(`Sync complete: Processed ${processedLabels} labels, skipped ${skippedOldLabels} old labels`);

    return Response.json({
      success: true,
      processedLabels,
      skippedOldLabels,
      totalShippingCost,
      shippingCost,
      dateRange: {
        startDate: formattedStartDate,
        endDate: formattedEndDate
      }
    });
  } catch (error) {
    console.error(`Error syncing ShipStation data: ${error.message}`);

    // Log more detailed error information
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error(`Response status: ${error.response.status}`);
      console.error(`Response headers: ${JSON.stringify(error.response.headers)}`);
      console.error(`Response data: ${JSON.stringify(error.response.data)}`);
    } else if (error.request) {
      // The request was made but no response was received
      console.error(`No response received: ${error.request}`);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error(`Error setting up request: ${error.message}`);
    }

    return Response.json({
      success: false,
      error: error.message,
      details: error.response ? error.response.data : null
    }, { status: 500 });
  }
}

// Loader to check status
export async function loader({ request }) {
  try {
    // Check for API key
    const apiKey = request.headers.get("X-API-Key");
    if (!apiKey || apiKey !== process.env.RECONCILIATION_API_KEY) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    return Response.json({
      status: "ShipStation sync API is available",
      usage: "Send a POST request to sync shipping costs from ShipStation"
    });
  } catch (error) {
    return Response.json({ error: error.message }, { status: 500 });
  }
}
