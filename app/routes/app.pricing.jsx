import {
  <PERSON><PERSON>tack,
  Card,
  DataTable,
  Layout,
  Page,
  Select,
  Text,
  TextField,
  Button,
  InlineStack,
  Banner,
  Modal
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { useState, useCallback, useMemo, useEffect } from "react";
import { useLoaderData, useActionData, useSubmit } from "@remix-run/react";

import {
  getUniqueShopsWithPrices,
  getPrices,
  setPrice,
  getPrice,
  createPrice,
  setPriceForAllShops
} from "../models/Price.server";

/**
 * Loader function to fetch all shops with prices and their price data
 */
export async function loader({ request }) {
  // Import server-only functions inside the loader
  const { authenticateMultistore } = await import("../shopify.multistore.server");

  // Use multistore authentication
  const result = await authenticateMultistore(request);

  // If result is a Response (redirect), throw it to let <PERSON> handle it
  if (result instanceof Response) {
    throw result;
  }

  const { session } = result;

  // Check if user is admin
  if (session.shop !== process.env.ADMIN_SHOP) {
    return Response.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Get all unique shops that have prices
    const shops = await getUniqueShopsWithPrices();

    if (!shops || shops.length === 0) {
      console.warn('No shops found with prices');
    }

    // Get URL parameters
    const url = new URL(request.url);
    const selectedShop = url.searchParams.get("shop") || (shops.length > 0 ? shops[0] : "");

    if (!selectedShop) {
      console.warn('No shop selected and no default shop available');
    }

    // Get prices for the selected shop
    const prices = selectedShop ? await getPrices(selectedShop) : [];
    console.log(`Retrieved ${prices.length} prices for shop: ${selectedShop}`);

    // Sort prices by category name for consistent display
    const sortedPrices = [...prices].sort((a, b) => a.category.localeCompare(b.category));

    return {
      shops,
      selectedShop,
      prices: sortedPrices
    };
  } catch (error) {
    console.error('Error in pricing loader:', error);
    throw error;
  }
}

/**
 * Action function to handle price updates
 */
export async function action({ request }) {
  // Import server-only functions inside the action
  const { authenticateMultistore } = await import("../shopify.multistore.server");

  // Use multistore authentication
  const result = await authenticateMultistore(request);

  // If result is a Response (redirect), throw it to let Remix handle it
  if (result instanceof Response) {
    throw result;
  }

  const { session } = result;

  // Check if user is admin
  if (session.shop !== process.env.ADMIN_SHOP) {
    return Response.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Parse form data
  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "updatePrice") {
    const shop = formData.get("shop");
    const category = formData.get("category");
    const cost = parseFloat(formData.get("cost"));

    if (!shop || !category || isNaN(cost)) {
      return {
        success: false,
        message: "Invalid input data"
      };
    }

    try {
      await setPrice(shop, category, cost);
      return {
        success: true,
        message: `Price for ${category} updated successfully`,
        shop,
        category,
        cost
      };
    } catch (error) {
      console.error(`Error updating price: ${error.message}`);

      // Provide a more user-friendly error message
      let errorMessage = `Error updating price: ${error.message}`;

      // Check for common errors
      if (error.message.includes("Price record not found")) {
        errorMessage = `This price record doesn't exist yet. Please create it first.`;
      } else if (error.message.includes("Prisma")) {
        errorMessage = "There was a database error. Please try again or contact support.";
      }

      return {
        success: false,
        message: errorMessage
      };
    }
  } else if (action === "createPrice") {
    const shop = formData.get("shop");
    const category = formData.get("category");
    const cost = parseFloat(formData.get("cost"));

    if (!shop || !category || isNaN(cost)) {
      return {
        success: false,
        message: "Invalid input data for creating price"
      };
    }

    try {
      // Check if the price already exists
      const existingPrice = await getPrice(shop, category);

      if (existingPrice) {
        return {
          success: false,
          message: `A price for ${category} already exists. Please update it instead.`
        };
      }

      // Create the new price
      const newPrice = await createPrice(shop, category, cost);

      return {
        success: true,
        message: `New price for ${category} created successfully`,
        shop,
        category,
        cost,
        id: newPrice.id
      };
    } catch (error) {
      console.error(`Error creating price: ${error.message}`);

      return {
        success: false,
        message: `Error creating price: ${error.message}`
      };
    }
  } else if (action === "updatePriceForAllShops") {
    const category = formData.get("category");
    const cost = parseFloat(formData.get("cost"));

    if (!category || isNaN(cost)) {
      return {
        success: false,
        message: "Invalid input data for updating price across all shops"
      };
    }

    try {
      // Update the price across all shops
      const result = await setPriceForAllShops(category, cost);

      if (result.updated === 0) {
        return {
          success: false,
          message: `No prices found for category "${category}" across any shops.`
        };
      }

      return {
        success: true,
        message: `Updated price for "${category}" across ${result.updated} ${result.updated === 1 ? 'shop' : 'shops'}`,
        category,
        cost,
        updatedCount: result.updated,
        updatedShops: result.shops,
        bulkUpdate: true
      };
    } catch (error) {
      console.error(`Error updating price across all shops: ${error.message}`);

      return {
        success: false,
        message: `Error updating price across all shops: ${error.message}`
      };
    }
  }

  return { success: false, message: "Invalid action" };
}

/**
 * Main component for the pricing management page
 */
export default function PricingPage() {
  const { shops, selectedShop, prices } = useLoaderData();
  const actionData = useActionData();
  const submit = useSubmit();

  const [currentShop, setCurrentShop] = useState(selectedShop);
  const [priceData, setPriceData] = useState(prices);
  const [editingPrice, setEditingPrice] = useState(null);
  const [newCost, setNewCost] = useState("");
  const [isUpdating, setIsUpdating] = useState(false);
  const [notification, setNotification] = useState(null);

  // State for creating new price
  const [newCategory, setNewCategory] = useState("");
  const [newCategoryPrice, setNewCategoryPrice] = useState("");
  const [isCreating, setIsCreating] = useState(false);

  // State for bulk updating prices
  const [bulkCategory, setBulkCategory] = useState("");
  const [bulkPrice, setBulkPrice] = useState("");
  const [isBulkUpdating, setIsBulkUpdating] = useState(false);
  const [showBulkUpdateModal, setShowBulkUpdateModal] = useState(false);

  // Format shop name for display
  const formatShopName = useCallback((shop) => {
    // Remove .myshopify.com from the end
    let formattedName = shop.replace(/\.myshopify\.com$/, '');

    // Replace dashes with spaces
    formattedName = formattedName.replace(/-/g, ' ');

    // Capitalize first letter of each word
    formattedName = formattedName.split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    return formattedName;
  }, []);

  // Update state when loader data changes
  useEffect(() => {
    setCurrentShop(selectedShop);
    setPriceData(prices);
  }, [selectedShop, prices]);

  // Show notification when action completes
  useEffect(() => {
    if (actionData) {
      setNotification({
        status: actionData.success ? "success" : "critical",
        message: actionData.message
      });

      if (actionData.success) {
        if (actionData.bulkUpdate) {
          // This is a bulk update across all shops
          if (actionData.updatedShops.includes(currentShop)) {
            // Update prices for the current shop if it was affected
            setPriceData(prevPrices =>
              prevPrices.map(price =>
                price.category === actionData.category
                  ? { ...price, cost: actionData.cost }
                  : price
              )
            );
          }

          // Close modal and clear bulk update state
          setShowBulkUpdateModal(false);
          setBulkCategory("");
          setBulkPrice("");
          setIsBulkUpdating(false);
        } else if (actionData.shop === currentShop) {
          if (actionData.id) {
            // This is a newly created price
            setPriceData(prevPrices => [
              ...prevPrices,
              {
                id: actionData.id,
                shop: actionData.shop,
                category: actionData.category,
                cost: actionData.cost
              }
            ]);

            // Clear create price form
            setNewCategory("");
            setNewCategoryPrice("");
            setIsCreating(false);
          } else {
            // This is an updated price for a single shop
            setPriceData(prevPrices =>
              prevPrices.map(price =>
                price.shop === actionData.shop && price.category === actionData.category
                  ? { ...price, cost: actionData.cost }
                  : price
              )
            );

            // Clear editing state
            setEditingPrice(null);
            setNewCost("");
            setIsUpdating(false);
          }
        }
      } else {
        // Clear loading states on error
        setIsUpdating(false);
        setIsCreating(false);
        setIsBulkUpdating(false);
      }

      // Auto-hide notification after 5 seconds
      const timer = setTimeout(() => {
        setNotification(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [actionData, currentShop]);

  // Handle shop selection change
  const handleShopChange = useCallback((value) => {
    // Reset editing state
    setEditingPrice(null);
    setNewCost("");

    // Navigate to the new shop
    const searchParams = new URLSearchParams();
    searchParams.set("shop", value);
    submit(searchParams, { method: "get" });
  }, [submit]);

  // Start editing a price
  const handleEditPrice = useCallback((id, currentCost) => {
    setEditingPrice(id);
    setNewCost(currentCost.toString());
  }, []);

  // Cancel editing
  const handleCancelEdit = useCallback(() => {
    setEditingPrice(null);
    setNewCost("");
  }, []);

  // Update price
  const handleUpdatePrice = useCallback((shop, category, cost) => {
    setIsUpdating(true);

    const formData = new FormData();
    formData.append("action", "updatePrice");
    formData.append("shop", shop);
    formData.append("category", category);
    formData.append("cost", cost);

    submit(formData, { method: "post" });
  }, [submit]);

  // Create new price
  const handleCreatePrice = useCallback(() => {
    if (!currentShop || !newCategory || !newCategoryPrice) {
      setNotification({
        status: "critical",
        message: "Please fill in all fields to create a new price"
      });
      return;
    }

    setIsCreating(true);

    const formData = new FormData();
    formData.append("action", "createPrice");
    formData.append("shop", currentShop);
    formData.append("category", newCategory);
    formData.append("cost", newCategoryPrice);

    submit(formData, { method: "post" });
  }, [currentShop, newCategory, newCategoryPrice, submit]);

  // Open bulk update modal
  const handleOpenBulkUpdateModal = useCallback((category, currentCost) => {
    setBulkCategory(category);
    setBulkPrice(currentCost.toString());
    setShowBulkUpdateModal(true);
  }, []);

  // Close bulk update modal
  const handleCloseBulkUpdateModal = useCallback(() => {
    setShowBulkUpdateModal(false);
    setBulkCategory("");
    setBulkPrice("");
  }, []);

  // Update price across all shops
  const handleBulkUpdatePrice = useCallback(() => {
    if (!bulkCategory || !bulkPrice) {
      setNotification({
        status: "critical",
        message: "Please fill in all fields to update price across all shops"
      });
      return;
    }

    setIsBulkUpdating(true);

    const formData = new FormData();
    formData.append("action", "updatePriceForAllShops");
    formData.append("category", bulkCategory);
    formData.append("cost", bulkPrice);

    submit(formData, { method: "post" });
  }, [bulkCategory, bulkPrice, submit]);

  // Format currency for display
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Create shop options for select dropdown
  const shopOptions = useMemo(() => {
    return shops.map(shop => ({
      label: formatShopName(shop),
      value: shop
    }));
  }, [shops, formatShopName]);

  // Create rows for data table
  const rows = useMemo(() => {
    return priceData.map(price => {
      const isEditing = editingPrice === price.id;

      return [
        price.category,
        isEditing ? (
          <TextField
            type="number"
            value={newCost}
            onChange={setNewCost}
            autoComplete="off"
            step="0.01"
            min="0"
          />
        ) : formatCurrency(Number(price.cost)),
        isEditing ? (
          <InlineStack gap="200">
            <Button
              size="slim"
              onClick={() => handleUpdatePrice(price.shop, price.category, newCost)}
              loading={isUpdating}
            >
              Save
            </Button>
            <Button
              size="slim"
              onClick={handleCancelEdit}
              disabled={isUpdating}
            >
              Cancel
            </Button>
          </InlineStack>
        ) : (
          <InlineStack gap="200">
            <Button
              size="slim"
              onClick={() => handleEditPrice(price.id, price.cost)}
            >
              Edit
            </Button>
            <Button
              size="slim"
              onClick={() => handleOpenBulkUpdateModal(price.category, price.cost)}
            >
              Update All Shops
            </Button>
          </InlineStack>
        )
      ];
    });
  }, [priceData, editingPrice, newCost, isUpdating, handleEditPrice, handleUpdatePrice, handleCancelEdit, handleOpenBulkUpdateModal]);

  return (
    <Page fullWidth>
      <TitleBar title={currentShop ?
        `Update Pricing - ${formatShopName(currentShop)}` :
        "Update Client Pricing"
      } />

      {/* Bulk Update Modal */}
      <Modal
        open={showBulkUpdateModal}
        onClose={handleCloseBulkUpdateModal}
        title={`Update "${bulkCategory}" Price Across All Shops`}
        primaryAction={{
          content: "Update All Shops",
          onAction: handleBulkUpdatePrice,
          loading: isBulkUpdating
        }}
        secondaryActions={[
          {
            content: "Cancel",
            onAction: handleCloseBulkUpdateModal
          }
        ]}
      >
        <Modal.Section>
          <BlockStack gap="400">
            <Text>
              This will update the price for "{bulkCategory}" across all shops that have this category.
              Are you sure you want to continue?
            </Text>
            <TextField
              label="New Price"
              type="number"
              value={bulkPrice}
              onChange={setBulkPrice}
              autoComplete="off"
              step="0.01"
              min="0"
              prefix="$"
            />
          </BlockStack>
        </Modal.Section>
      </Modal>

      <BlockStack gap="500">
        {notification && (
          <Banner
            title={notification.status === "success" ? "Success" : "Error"}
            status={notification.status}
            onDismiss={() => setNotification(null)}
          >
            <p>{notification.message}</p>
          </Banner>
        )}

        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingLg">
                  {currentShop ?
                    `Manage Pricing for ${formatShopName(currentShop)}` :
                    "Manage Pricing for Client Shops"
                  }
                </Text>

                <Select
                  label="Select Shop"
                  options={shopOptions}
                  value={currentShop}
                  onChange={handleShopChange}
                  disabled={shops.length === 0}
                />

                {currentShop ? (
                  <>
                    {priceData.length > 0 ? (
                      <DataTable
                        columnContentTypes={['text', 'text', 'text']}
                        headings={[
                          <Text key="category" variant="bodyMd" fontWeight="bold">Category</Text>,
                          <Text key="current-price" variant="bodyMd" fontWeight="bold">Price</Text>,
                          <Text key="actions" variant="bodyMd" fontWeight="bold">Actions</Text>
                        ]}
                        rows={rows}
                        footerContent={`Showing ${priceData.length} ${priceData.length === 1 ? 'price' : 'prices'}`}
                      />
                    ) : (
                      <Text>No prices found for this shop.</Text>
                    )}

                    <BlockStack gap="400" style={{ marginTop: '20px', paddingTop: '20px', borderTop: '1px solid #ddd' }}>
                      <Text as="h3" variant="headingMd">
                        Add New Price Category
                      </Text>

                      <InlineStack gap="300" align="start" wrap={false}>
                        <div style={{ flex: 2 }}>
                          <TextField
                            label="Category Name"
                            value={newCategory}
                            onChange={setNewCategory}
                            autoComplete="off"
                            placeholder="e.g., custom t-shirt"
                          />
                        </div>
                        <div style={{ flex: 1 }}>
                          <TextField
                            label="Price"
                            type="number"
                            value={newCategoryPrice}
                            onChange={setNewCategoryPrice}
                            autoComplete="off"
                            step="0.01"
                            min="0"
                            prefix="$"
                            placeholder="0.00"
                          />
                        </div>
                        <div style={{ marginTop: '25px' }}>
                          <Button
                            onClick={handleCreatePrice}
                            loading={isCreating}
                          >
                            Add Price
                          </Button>
                        </div>
                      </InlineStack>
                    </BlockStack>
                  </>
                ) : (
                  <Text>Please select a shop to view and manage prices.</Text>
                )}
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}
