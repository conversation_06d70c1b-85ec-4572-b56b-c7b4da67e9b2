import { authenticate } from "../shopify.server";
import { resetProcessingFlag, getSetupFlag } from "../models/IsSetup.server";

/**
 * API endpoint to reset the processing flag for a shop
 * This is useful for manually fixing shops that are stuck in a processing state
 */
export async function loader({ request }) {
  try {
    // Authenticate the request
    const { session } = await authenticate.admin(request);
    console.log(`[Reset Processing Flag] Request from shop: ${session.shop}`);
    
    // Get the current setup flag
    const setupFlag = await getSetupFlag(session.shop);
    console.log(`[Reset Processing Flag] Current setup flag:`, setupFlag);
    
    if (!setupFlag) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: "No setup flag found for this shop" 
        }),
        { 
          status: 404,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
    
    // Reset the processing flag
    await resetProcessingFlag(session.shop);
    console.log(`[Reset Processing Flag] Reset processing flag for shop: ${session.shop}`);
    
    // Get the updated setup flag
    const updatedSetupFlag = await getSetupFlag(session.shop);
    console.log(`[Reset Processing Flag] Updated setup flag:`, updatedSetupFlag);
    
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: "Processing flag reset successfully",
        before: setupFlag,
        after: updatedSetupFlag
      }),
      { 
        status: 200,
        headers: { "Content-Type": "application/json" }
      }
    );
  } catch (error) {
    console.error(`[Reset Processing Flag] Error:`, error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: `Error: ${error.message}` 
      }),
      { 
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
}
