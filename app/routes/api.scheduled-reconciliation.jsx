import { ReconciliationOrchestrator } from "../services/reconciliation/ReconciliationOrchestrator.js";
import { getRunningReconciliationJobs } from "../models/ReconciliationJob.server";
/**
 * API endpoint to trigger scheduled reconciliation
 * This endpoint should be called by a cron job or similar scheduler
 * It can be secured with an API key or other authentication mechanism
 */
export async function action({ request }) {
  // Only allow POST requests
  if (request.method !== "POST") {
    return Response.json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Parse request body for options
    const body = await request.json();

    // Check for API key using Shopify API key for authentication
    const apiKey = request.headers.get("X-API-Key");

    // Accept either the API key alone or a combined key+secret token
    const validKey = process.env.SHOPIFY_API_KEY;
    const combinedToken = `${process.env.SHOPIFY_API_KEY}:${process.env.SHOPIFY_API_SECRET}`;

    if (!apiKey || (apiKey !== validKey && apiKey !== combinedToken)) {
      console.log(`Authentication failed. Received key does not match expected values.`);
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if there are already running reconciliation jobs
    const runningJobs = await getRunningReconciliationJobs();
    if (runningJobs.length > 0) {
      return Response.json({
        success: false,
        error: "There are already running reconciliation jobs. Please wait for them to complete.",
        runningJobs
      }, { status: 409 }); // 409 Conflict
    }

    let results;

    // Create reconciliation orchestrator
    const reconciliationOrchestrator = new ReconciliationOrchestrator();

    // Determine which type of reconciliation to run
    let reconciliationType;
    let reconciliationOptions = {};

    if (body.type === "check-missing") {
      reconciliationType = "missing_orders";
      reconciliationOptions = {
        daysToLookBack: body.daysToLookBack ? parseInt(body.daysToLookBack, 10) : 30,
      };
    } else if (body.type === "shipstation-sync") {
      reconciliationType = "shipstation_sync";
      reconciliationOptions = {
        daysToLookBack: body.daysToLookBack ? parseInt(body.daysToLookBack, 10) : 7,
      };
    } else if (body.type === "test-orders") {
      reconciliationType = "scheduled";
      reconciliationOptions = {
        hoursToLookBack: body.hoursToLookBack ? parseInt(body.hoursToLookBack, 10) : 1,
        testOrdersOnly: true,
      };
    } else {
      // Run standard scheduled reconciliation
      reconciliationType = "scheduled";
      reconciliationOptions = {
        daysToLookBack: body.daysToLookBack ? parseInt(body.daysToLookBack, 10) : 30,
        ordersPerShop: body.ordersPerShop ? parseInt(body.ordersPerShop, 10) : 50,
      };
    }

    // Execute reconciliation using the orchestrator
    results = await reconciliationOrchestrator.executeReconciliation(
      reconciliationType,
      reconciliationOptions
    );

    // Return results
    return Response.json({
      success: true,
      results
    });
  } catch (error) {
    console.error(`Error during scheduled reconciliation: ${error.message}`);
    return Response.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

/**
 * Handle GET requests to show status
 */
export async function loader({ request }) {
  try {
    // Check for API key using Shopify API key for authentication
    const apiKey = request.headers.get("X-API-Key");
    if (!apiKey || apiKey !== process.env.SHOPIFY_API_KEY) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get running jobs
    const runningJobs = await getRunningReconciliationJobs();

    return Response.json({
      status: "Scheduled reconciliation API is available",
      usage: "Send a POST request to trigger scheduled reconciliation",
      types: [
        { type: "standard", description: "Standard reconciliation of all orders" },
        { type: "check-missing", description: "Check for missing orders in the current month" },
        { type: "shipstation-sync", description: "Sync shipping costs from ShipStation" },
        { type: "shipstation-stores-refresh", description: "Refresh the list of ShipStation stores" },
        { type: "test-orders", description: "Reconcile test orders that don't trigger webhooks" }
      ],
      runningJobs: runningJobs.length > 0 ? runningJobs : null
    });
  } catch (error) {
    return Response.json({success: false, error: error.message }, { status: 500 });
  }
}
