import {
  Card,
  Layout,
  Page,
  Select,
  Text,
  BlockStack,
  InlineStack,
  Spinner,
  DataTable
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { useState, useCallback, useMemo, useEffect } from 'react';
// Import will be done dynamically in the loader
import { useLoaderData } from "@remix-run/react";
import { getShops } from "../models/Shop.server";
import { getInvoiceBalances } from "../models/InvoiceBalance.server";
import { getAllShippingCosts } from "../models/ShippingCost.server";
import calculateTotalBalance from "../utils/balance";

/**
 * Loader function to fetch all shop data and their balances
 */
export async function loader({ request }) {
  console.log("[All Balances Loader] Starting loader execution");

  // Import authentication functions dynamically
  const { authenticateMultistore } = await import("../shopify.multistore.server");

  // Use multistore authentication - no fallback to single store
  const result = await authenticateMultistore(request);

  // If result is a Response (redirect), throw it to let <PERSON> handle it
  if (result instanceof Response) {
    console.log(`[All Balances Loader] Got redirect response, throwing it`);
    throw result;
  }

  const { session } = result;
  console.log(`[All Balances Loader] Authenticated for shop: ${session.shop}`);

  // Get admin shop from environment variable
  const adminShop = process.env.ADMIN_SHOP;
  console.log(`[All Balances Loader] Admin shop: ${adminShop}`);

  // Get all shops
  const shops = await getShops();
  console.log(`[All Balances Loader] Found ${shops.length} shops`);

  // Get current month and year
  const now = new Date();
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();

  // Create an array to hold all shop data
  const shopData = [];

  // For each shop, get their invoice balances and shipping costs for all months
  for (const shop of shops) {
    // Skip the admin shop
    if (shop.shop === adminShop) {
      console.log(`[All Balances Loader] Skipping admin shop: ${shop.shop}`);
      continue;
    }

    console.log(`[All Balances Loader] Processing shop: ${shop.shop}`);

    // Get all invoice balances for the shop
    const invoiceBalances = await getInvoiceBalances(shop.shop);
    console.log(`[All Balances Loader] Found ${invoiceBalances.length} total invoice balances for ${shop.shop}`);

    // Get all shipping costs for the shop
    const shippingCosts = await getAllShippingCosts(shop.shop);
    console.log(`[All Balances Loader] Found ${shippingCosts.length} total shipping costs for ${shop.shop}`);

    // Add shop data to the array
    shopData.push({
      shop: shop.shop,
      invoiceBalances,
      shippingCosts
    });
  }

  return {
    shopData,
    currentMonth,
    currentYear,
    adminShop
  };
}

/**
 * Main component for displaying all shop balances
 */
export default function AdminPage() {
  const { shopData, currentMonth, adminShop } = useLoaderData();
  const [selectedMonth, setSelectedMonth] = useState(currentMonth);
  const [isCalculating, setIsCalculating] = useState(false);

  console.log(`[All Balances Component] Rendering all-balances page with ${shopData?.length || 0} shops`);

  // Handle month change
  const handleMonthChange = useCallback((value) => {
    setIsCalculating(true);
    setSelectedMonth(Number(value));
  }, []);

  // Create month options for the select dropdown
  const options = Array.from({length: new Date().getMonth() + 1}, (_, i) => ({
    label: new Date(0, i).toLocaleString("default", { month: "long" }),
    value: i,
  }));

  // Calculate shop balances based on the selected month
  const shopBalances = useMemo(() => {
    const balances = [];

    console.log('Selected month:', selectedMonth, 'Type:', typeof selectedMonth);
    console.log('Shop data:', shopData.length, 'shops');
    console.log('Admin shop:', adminShop);

    // Log all available months in the data for debugging
    const availableMonths = new Set();
    shopData.forEach(shop => {
      shop.invoiceBalances.forEach(balance => {
        availableMonths.add(Number(balance.month));
      });
      shop.shippingCosts.forEach(cost => {
        availableMonths.add(Number(cost.month));
      });
    });
    console.log('Available months in data:', Array.from(availableMonths).sort());

    for (const shop of shopData) {
      console.log(`Processing shop: ${shop.shop}`);
      console.log(`Invoice balances before filtering: ${shop.invoiceBalances.length}`);
      console.log(`Shipping costs before filtering: ${shop.shippingCosts.length}`);

      // Log some sample data to see the month format
      if (shop.invoiceBalances.length > 0) {
        console.log('Sample invoice balance:', {
          month: shop.invoiceBalances[0].month,
          monthType: typeof shop.invoiceBalances[0].month,
          year: shop.invoiceBalances[0].year
        });
      }

      if (shop.shippingCosts.length > 0) {
        console.log('Sample shipping cost:', {
          month: shop.shippingCosts[0].month,
          monthType: typeof shop.shippingCosts[0].month,
          year: shop.shippingCosts[0].year
        });
      }

      // Get current year
      const currentYear = new Date().getFullYear();

      // Filter invoice balances for the selected month and current year
      const filteredInvoiceBalances = shop.invoiceBalances.filter(
        balance => Number(balance.month) === Number(selectedMonth) && Number(balance.year) === currentYear
      );

      console.log(`Invoice balances after filtering (month: ${selectedMonth}, year: ${currentYear}): ${filteredInvoiceBalances.length}`);

      // Filter shipping costs for the selected month and current year
      const filteredShippingCosts = shop.shippingCosts.filter(
        cost => Number(cost.month) === Number(selectedMonth) && Number(cost.year) === currentYear
      );

      console.log(`Shipping costs after filtering (month: ${selectedMonth}, year: ${currentYear}): ${filteredShippingCosts.length}`);

      // Find fulfillment costs from invoice balances
      const fulfillmentCosts = filteredInvoiceBalances.find(
        balance => balance.category === 'Fulfillment Costs'
      );

      // Calculate shipping costs total
      const shippingCostsTotal = filteredShippingCosts.reduce(
        (sum, cost) => sum + Number(cost.totalAmount), 0
      );

      // Calculate fulfillment costs total
      const fulfillmentCostsTotal = fulfillmentCosts ? Number(fulfillmentCosts.balance) : 0;

      // Calculate shipping and fulfillment total
      const shippingAndFulfillmentTotal = shippingCostsTotal + fulfillmentCostsTotal;

      // Filter out shipping and fulfillment costs to calculate cost of goods
      const costOfGoodsBalances = filteredInvoiceBalances.filter(
        balance => balance.category !== 'Fulfillment Costs' &&
                  balance.category !== 'shipping costs'
      );

      // Calculate cost of goods total
      const costOfGoodsTotal = calculateTotalBalance(costOfGoodsBalances);

      // Calculate total balance
      const totalBalance = costOfGoodsTotal + shippingAndFulfillmentTotal;

      // Add shop balance to the array
      balances.push({
        shop: shop.shop,
        costOfGoodsTotal,
        shippingAndFulfillmentTotal,
        totalBalance
      });
    }

    return balances;
  }, [shopData, selectedMonth]);

  // Calculate grand totals across all shops
  const grandTotals = useMemo(() => {
    const costOfGoodsTotal = shopBalances.reduce(
      (sum, shop) => sum + shop.costOfGoodsTotal, 0
    );

    const shippingAndFulfillmentTotal = shopBalances.reduce(
      (sum, shop) => sum + shop.shippingAndFulfillmentTotal, 0
    );

    const totalBalance = costOfGoodsTotal + shippingAndFulfillmentTotal;

    return {
      costOfGoodsTotal,
      shippingAndFulfillmentTotal,
      totalBalance
    };
  }, [shopBalances]);

  // Format currency for display
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Create data table rows for shops with enhanced styling
  const rows = useMemo(() => {
    return shopBalances.map(shop => {
      // Format shop name to be more readable
      const formattedShopName = shop.shop.replace('.myshopify.com', '').replace(/-/g, ' ');
      const displayName = formattedShopName
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      // Create styled elements for each value
      return [
        <Text key={`shop-${shop.shop}`} variant="bodyMd" fontWeight="semibold">{displayName}</Text>,
        <Text key={`cost-${shop.shop}`} variant="bodyMd" alignment="end">
          {formatCurrency(shop.costOfGoodsTotal)}
        </Text>,
        <Text key={`shipping-${shop.shop}`} variant="bodyMd" alignment="end">
          {formatCurrency(shop.shippingAndFulfillmentTotal)}
        </Text>,
        <Text key={`total-${shop.shop}`} variant="bodyMd" fontWeight="bold" alignment="end" color="success">
          {formatCurrency(shop.totalBalance)}
        </Text>
      ];
    });
  }, [shopBalances]);

  // Turn off calculating state after data is processed
  useEffect(() => {
    if (isCalculating) {
      // Add a minimum delay to ensure the loading state is visible
      // This provides better UX even if the calculation is quick
      const timer = setTimeout(() => {
        setIsCalculating(false);
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [selectedMonth, isCalculating]);

  // Show a skeleton loader when calculating
  const renderSkeletonLoader = () => {
    return (
      <div style={styles.loadingContainer}>
        <BlockStack alignment="center" gap="400">
          <Spinner size="large" color="teal" />
          <div style={styles.calculatingText}>
            <Text variant="bodyMd">Calculating balances for {options.find(opt => opt.value === selectedMonth)?.label}...</Text>
          </div>
        </BlockStack>
      </div>
    );
  };

  // Define some custom styles
  const styles = {
    grandTotalCard: {
      background: 'linear-gradient(to right, #f9fafb, #f4f6f8)',
      borderLeft: '4px solid #008060',
      padding: '20px'
    },
    grandTotalValue: {
      color: '#008060',
      fontWeight: 'bold',
      fontSize: '28px',
      marginBottom: '16px'
    },
    subtotalLabel: {
      fontWeight: 'normal',
      color: '#202223',
      display: 'inline-block',
      width: '180px'
    },
    subtotalValue: {
      fontWeight: 'bold',
      color: '#202223'
    },
    subtotalRow: {
      marginBottom: '8px'
    },
    monthSelectorContainer: {
      minWidth: '200px'
    },
    tableContainer: {
      marginTop: '24px'
    },
    shopBalancesCard: {
      padding: '0'
    },
    shopBalancesHeader: {
      padding: '20px 20px 0 20px',
      borderBottom: '1px solid #e1e3e5',
      marginBottom: '0'
    },
    tableWrapper: {
      padding: '0 20px 20px 20px'
    },
    loadingContainer: {
      display: 'flex',
      justifyContent: 'center',
      padding: '40px 20px',
      background: '#f9fafb'
    },
    calculatingText: {
      marginTop: '12px',
      color: '#6d7175'
    }
  };

  return (
    <Page fullWidth>
      <TitleBar title="All Shop Balances by Month" />
      <Layout>
        <Layout.Section>
          {/* Grand Totals Card */}
          <Card>
            <div style={styles.grandTotalCard}>
              <BlockStack gap="500">
                <InlineStack align="space-between">
                  <BlockStack gap="400">
                    <div>
                      <Text as="h2" variant="headingXl">Grand Total</Text>
                      <div style={styles.grandTotalValue}>{formatCurrency(grandTotals.totalBalance)}</div>
                    </div>
                    <BlockStack gap="200">
                      <div style={styles.subtotalRow}>
                        <Text as="span" variant="bodyLg" style={styles.subtotalLabel}>Cost of Goods: </Text>
                        <Text as="span" variant="bodyLg" style={styles.subtotalValue}><strong>{formatCurrency(grandTotals.costOfGoodsTotal)}</strong></Text>
                      </div>
                      <div style={styles.subtotalRow}>
                        <Text as="span" variant="bodyLg" style={styles.subtotalLabel}>Shipping & Fulfillment: </Text>
                        <Text as="span" variant="bodyLg" style={styles.subtotalValue}><strong>{formatCurrency(grandTotals.shippingAndFulfillmentTotal)}</strong></Text>
                      </div>
                    </BlockStack>
                  </BlockStack>
                  <div style={styles.monthSelectorContainer}>
                    <Select
                      label="Select Month"
                      options={options}
                      onChange={handleMonthChange}
                      value={selectedMonth}
                    />
                  </div>
                </InlineStack>
              </BlockStack>
            </div>
          </Card>

          {/* Shop Balances Table */}
          <div style={styles.tableContainer}>
            <Card>
              <div style={styles.shopBalancesCard}>
                <div style={styles.shopBalancesHeader}>
                  <Text as="h3" variant="headingLg">Shop Balances</Text>
                </div>

                {isCalculating ? (
                  renderSkeletonLoader()
                ) : (
                  <div style={styles.tableWrapper}>
                    <DataTable
                      columnContentTypes={['text', 'numeric', 'numeric', 'numeric']}
                      headings={[
                        'Shop',
                        <Text key="cost-heading" variant="bodyMd" fontWeight="bold">Cost of Goods</Text>,
                        <Text key="shipping-heading" variant="bodyMd" fontWeight="bold">Shipping & Fulfillment</Text>,
                        <Text key="total-heading" variant="bodyMd" fontWeight="bold">Total Balance</Text>
                      ]}
                      rows={rows}
                      footerContent={`Showing ${rows.length} ${rows.length === 1 ? 'shop' : 'shops'}`}
                      hoverable={true}
                    />
                  </div>
                )}
              </div>
            </Card>
          </div>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
