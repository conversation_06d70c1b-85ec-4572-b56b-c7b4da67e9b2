import { InlineStack, Text, Card } from "@shopify/polaris"
import "./Total.css"

/**
 * Total Component
 *
 * A React component for displaying a total balance in a table format.
 *
 * @param {Object} props - The component props.
 * @param {Object} props.balance - An object representing the total balance. It should have the following properties:
 *   @param {string} balance.category - The category of the balance.
 *   @param {number} balance.quantity - The quantity of the balance.
 *   @param {number} balance.balance - The balance amount.
 * @param {boolean} [props.isGroup=false] - A flag indicating whether the balance represents a group of items.
 *
 * @returns {JSX.Element} A table displaying the total balance.
 */

/* // TODO's
    - The items in each group need to be displayed in a static order to maintain consistency across invoices
    - The groups themselves need to be displayed in a static order (probably handled on the main page)
*/

export const Total = ({balance, isGroup = false}) => {
    // Handle grouped items with sub-items
    if (isGroup && balance.items && balance.items.length > 0) {
        return (
            <Card>
                <div className="group-header">
                    <Text variant="headingMd">{balance.category}</Text>
                    <div className="group-total">
                        <Text variant="bodyMd">Total: <strong>{Number(balance.balance).toLocaleString("en-US", { style: "currency", currency: "USD"})}</strong></Text>
                    </div>
                </div>

                <table className="group-table">
                    <thead>
                        <tr>
                            <th className="item-name">Item</th>
                            <th className="item-quantity">Quantity</th>
                            <th className="item-price">Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        {balance.items.map((item) => (
                            <tr key={item.category}>
                                <td className="item-name">{item.category}</td>
                                <td className="item-quantity">{item.quantity}</td>
                                <td className="item-price">{Number(item.balance).toLocaleString("en-US", { style: "currency", currency: "USD"})}</td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </Card>
        );
    }

    // Regular non-grouped item
    return (
        <InlineStack align="space-between">
            <span className="title">{balance.category}</span>
            <span className="quantity">{balance.quantity}</span>
            <span className="balance"><strong>{Number(balance.balance).toLocaleString("en-US", { style: "currency", currency: "USD"})}</strong></span>
        </InlineStack>
    );
}
