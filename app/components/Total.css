.title {
    width: 60%;
}

.quantity {
    width: 20%;
    text-align: center;
}

.balance {
    width: 20%;
    text-align: right;
}

h3 {
    text-decoration: underline;
}

/* Card styling is now handled by Polaris Card component */

.group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 12px 0;
    margin-bottom: 12px;
    border-bottom: 1px solid #e1e3e5;
}

.group-total {
    text-align: right;
}

.group-table {
    width: 100%;
    border-collapse: collapse;
}

.group-table th {
    background-color: #f9fafb;
    text-align: left;
    padding: 12px 16px;
    font-weight: 600;
    border-bottom: 1px solid #e1e3e5;
}

.group-table td {
    padding: 12px 16px;
    border-bottom: 1px solid #e1e3e5;
}

.group-table tr:last-child td {
    border-bottom: none;
}

.item-name {
    width: 60%;
}

.item-quantity {
    width: 20%;
    text-align: center;
}

.item-price {
    width: 20%;
    text-align: right;
}
