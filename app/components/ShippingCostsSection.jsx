/**
 * ShippingCostsSection component allows users to integrate their ShipStation account
 * by entering their store name and saving the mapping. It also provides feedback
 * on the success or failure of the operation and displays synchronization results
 * for historical shipments if applicable.
 *
 * @param {Object} props - The component props.
 * @param {Array} props.storeMappings - An array of existing store mappings. If present, the component will not render.
 * @param {string} props.shop - The shop identifier for the current Shopify store.
 *
 * @returns {JSX.Element|null} The rendered component or null if store mappings already exist.
 */

// TODO's ---
/*
  - The success banner doesn't really need to be shown on successful sync
  - The functionality of this component should be used in the admin, rather than on each individual client invoice page
  - May need to remove this component completely in favor of a different interface for manipulating shop mappings in admin
      - Maybe pull list of stores from ShipStation API and display them in a dropdown?
*/
import { useState, useCallback, useEffect, useRef } from "react";
import {
  Card,
  Text,
  BlockStack,
  InlineStack,
  Banner,
  TextField,
  Button,
  ProgressBar
} from "@shopify/polaris";



// Custom hook for SSE
function useSSE(url, eventName) {
  const [data, setData] = useState(null);
  const [lastEventId, setLastEventId] = useState(0);
  const eventSourceRef = useRef(null);

  useEffect(() => {
    // Clean up any existing connection
    if (eventSourceRef.current) {
      console.log('Closing existing EventSource connection');
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    // Only create a connection if we have a URL
    if (!url) {
      console.log('No URL provided, not creating EventSource');
      return;
    }

    console.log(`Creating EventSource connection to ${url}`);

    try {
      // Create a new EventSource connection
      const eventSource = new EventSource(url);
      eventSourceRef.current = eventSource;

      // Handle connection open
      eventSource.onopen = () => {
        console.log('SSE connection opened successfully');
        // Force an initial progress update to show something
        setData(JSON.stringify({
          status: 'connecting',
          message: 'Connected to server, waiting for updates...',
          progress: 5
        }));
      };

      // Handle messages for the specific event
      eventSource.addEventListener(eventName, (event) => {
        console.log(`Received ${eventName} event:`, event);
        console.log(`Event data:`, event.data);
        console.log(`Event ID:`, event.lastEventId);

        // Only process if this is a new event
        const eventId = parseInt(event.lastEventId || 0, 10);
        if (eventId > lastEventId) {
          setLastEventId(eventId);
          setData(event.data);
        }
      });

      // Handle general messages
      eventSource.onmessage = (event) => {
        console.log('Received general message:', event);
        console.log('Message data:', event.data);

        // Only process if this is a new event
        const eventId = parseInt(event.lastEventId || 0, 10);
        if (eventId > lastEventId) {
          setLastEventId(eventId);
          setData(event.data);
        }
      };

      // Handle errors
      eventSource.onerror = (err) => {
        console.error('SSE connection error:', err);
        // Try to reconnect after a short delay
        setTimeout(() => {
          if (eventSourceRef.current) {
            console.log('Attempting to reconnect...');
            eventSourceRef.current.close();
            eventSourceRef.current = new EventSource(url);
          }
        }, 2000);
      };

      // Clean up on unmount
      return () => {
        console.log('Cleaning up EventSource connection');
        eventSource.close();
        eventSourceRef.current = null;
      };
    } catch (error) {
      console.error('Error creating EventSource:', error);
    }
  }, [url, eventName, lastEventId]);

  return data;
}

export function ShippingCostsSection({ storeMappings, shop }) {
  const [storeName, setStoreName] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [syncResults, setSyncResults] = useState(null);

  // Progress tracking state
  const [jobId, setJobId] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [progressMessage, setProgressMessage] = useState("Connecting to server...");

  // Use our custom SSE hook
  const sseUrl = jobId && isProcessing ? `/api/shipstation-progress?jobId=${jobId}` : null;
  console.log('SSE URL:', sseUrl);

  const progressData = useSSE(sseUrl, "progress");
  console.log('Raw progress data from SSE:', progressData);

  // Fallback to polling if SSE isn't working
  useEffect(() => {
    if (!jobId || !isProcessing) return;

    // Set up polling interval
    const pollInterval = setInterval(async () => {
      try {
        console.log(`Polling for progress updates for job ${jobId}`);
        const response = await fetch(`/api/shipstation-progress?jobId=${jobId}&poll=true`);
        const data = await response.json();

        console.log('Received polling data:', data);

        // Update progress state
        if (data && typeof data.progress === 'number') {
          // Force a minimum of 5% progress
          const newProgress = Math.max(5, data.progress || 0);
          console.log(`Setting progress to ${newProgress}% from polling`);
          setProgress(newProgress);

          // Update message state
          const newMessage = data.message || "Processing...";
          console.log(`Setting message to "${newMessage}" from polling`);
          setProgressMessage(newMessage);

          // Handle completion
          if (data.complete) {
            if (data.status === 'complete') {
              console.log('Processing complete (from polling), will refresh page soon');
              setSuccess(data.message || "Processing completed successfully");
              setSyncResults({
                processedShipments: data.processedItems,
                processedLabels: data.processedItems,
                totalShippingCost: data.totalCost || 0
              });

              // Schedule page refresh
              setTimeout(() => {
                console.log('Refreshing page now');
                window.location.reload();
              }, 2000);

              // Clear the polling interval
              clearInterval(pollInterval);
            } else if (data.status === 'failed') {
              setError(data.error || "Processing failed");
              setIsProcessing(false);

              // Clear the polling interval
              clearInterval(pollInterval);
            }
          }
        }
      } catch (error) {
        console.error('Error polling for progress:', error);
      }
    }, 1000); // Poll every second

    // Clean up on unmount
    return () => {
      clearInterval(pollInterval);
    };
  }, [jobId, isProcessing]);

  // Parse the progress data and update state
  const [parsedProgressData, setParsedProgressData] = useState(null);

  useEffect(() => {
    try {
      // Only try to parse if progressData is a non-empty string
      if (progressData && typeof progressData === 'string' && progressData.trim() !== '') {
        const parsed = JSON.parse(progressData);
        console.log('Successfully parsed progress data:', parsed);

        // Update parsed data state
        setParsedProgressData(parsed);

        // Update progress state - force a minimum of 5% progress
        const newProgress = Math.max(5, parsed.progress || 0);
        console.log(`Setting progress to ${newProgress}%`);
        setProgress(newProgress);

        // Update message state
        const newMessage = parsed.message || "Processing...";
        console.log(`Setting message to "${newMessage}"`);
        setProgressMessage(newMessage);

        // Handle completion
        if (parsed.complete && parsed.status === 'complete') {
          console.log('Processing complete, will refresh page soon');
          setSuccess(parsed.message || "Processing completed successfully");
          setSyncResults({
            processedShipments: parsed.processedItems,
            processedLabels: parsed.processedItems,
            totalShippingCost: parsed.totalCost || 0
          });

          // Schedule page refresh
          setTimeout(() => {
            console.log('Refreshing page now');
            window.location.reload();
          }, 2000);
        } else if (parsed.complete && parsed.status === 'failed') {
          setError(parsed.error || "Processing failed");
          setIsProcessing(false);
        }
      }
    } catch (error) {
      console.error('Error parsing progress data:', error, progressData);
    }
  }, [progressData]);

  // Function to poll the server and check if the mapping was created (fallback)
  const checkMappingCreated = useCallback(() => {
    let attempts = 0;
    const maxAttempts = 30; // Try for up to 5 minutes (30 * 10 seconds)

    const checkInterval = setInterval(async () => {
      attempts++;

      try {
        // Check if the mapping exists by fetching all mappings
        const response = await fetch("/api/shipstation-mapping");
        const data = await response.json();

        if (data.mappings && data.mappings.length > 0) {
          // Mapping was created successfully
          clearInterval(checkInterval);
          console.log("Mapping created successfully, refreshing page");
          window.location.reload();
        } else if (attempts >= maxAttempts) {
          // Give up after max attempts
          clearInterval(checkInterval);
          setError("Operation may not have completed. Please refresh the page to check status.");
          setIsProcessing(false);
        }
      } catch (error) {
        console.error("Error checking mapping status:", error);
        if (attempts >= maxAttempts) {
          clearInterval(checkInterval);
          setError("Could not verify if operation completed. Please refresh the page to check status.");
          setIsProcessing(false);
        }
      }
    }, 10000); // Check every 10 seconds

    // Store the interval ID so we can clear it if the component unmounts
    return () => clearInterval(checkInterval);
  }, []);

  // Handle store name input
  const handleStoreNameChange = useCallback((value) => {
    setStoreName(value);
  }, []);

  // Save store mapping
  const handleSaveMapping = useCallback(async () => {
    if (!storeName) {
      setError("Store name is required");
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);
    setJobId(null); // Reset job ID
    setIsProcessing(true);

    // Show initial processing message
    setSuccess("Connecting to ShipStation and processing historical data. This may take a few minutes for stores with many shipments...");

    try {
      // Set up AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
        // When timeout occurs, don't show an error - just let the user know processing continues
        setSuccess("Processing is taking longer than expected but continues on the server. Progress will update in real-time.");
      }, 120000); // 2 minute timeout

      const response = await fetch("/api/shipstation-mapping", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          storeName,
          shop
        }),
        signal: controller.signal
      });

      // Clear the timeout
      clearTimeout(timeoutId);

      // Check if the response is JSON before trying to parse it
      const contentType = response.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        // If we get HTML but the operation might still be processing successfully,
        // don't show an error - just let the user know and set up polling
        console.log("Received non-JSON response, but operation may still be processing");
        setSuccess("Processing is continuing on the server. Progress will update in real-time.");

        // Fall back to polling since we couldn't get a job ID
        checkMappingCreated();
        return;
      }

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to save store mapping");
      }

      // If we have a job ID, set it to enable SSE connection
      if (data.jobId) {
        console.log(`Received job ID: ${data.jobId}`);
        setJobId(data.jobId);
        setIsProcessing(true); // Ensure isProcessing is true to enable polling

        // Show the message from the server
        setSuccess(data.message || "Processing started. Tracking progress in real-time...");

        // If we already have sync results, show them
        if (data.syncResults) {
          setSyncResults(data.syncResults);
        }

        // Force an initial progress update starting at 0%
        setProgress(0);
        setProgressMessage("Establishing connection to ShipStation...");
      } else {
        // No job ID, just show the results
        if (data.syncResults) {
          setSyncResults(data.syncResults);
          setSuccess(`Store mapping saved successfully. Synced ${data.syncResults.processedLabels || data.syncResults.processedShipments || 0} historical shipments with a total cost of ${formatCurrency(data.syncResults.totalShippingCost)}.`);
        } else {
          setSuccess(data.message || "Store mapping saved successfully");
        }

        setIsProcessing(false);
        setStoreName("");

        // Reload the page to show the new mapping
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      }
    } catch (err) {
      // Handle specific abort error
      if (err.name === 'AbortError') {
        // Don't show an error for timeout - just let the user know processing continues
        setSuccess("Processing is continuing on the server. Progress will update in real-time.");

        // Try to connect to SSE if we have a job ID
        if (jobId) {
          console.log(`Using existing job ID after abort: ${jobId}`);
        } else {
          // Fall back to polling
          checkMappingCreated();
        }
      } else {
        // For other errors, show the error message
        setError(err.message);
        setIsProcessing(false);
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [storeName, shop, jobId, checkMappingCreated]);

  // Log progress data changes for debugging
  useEffect(() => {
    console.log(`Current progress: ${progress}%, message: ${progressMessage}`);
  }, [progress, progressMessage]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // If a store mapping already exists, don't show this component
  if (storeMappings && storeMappings.length > 0) {
    return null;
  }

  return (
    <Card>
      <BlockStack gap="4">
        <Text variant="headingMd">ShipStation Integration</Text>

        {error && (
          <Banner status="critical" onDismiss={() => setError(null)}>
            <p>Error: {error}</p>
          </Banner>
        )}

        {success && (
          <Banner status="success" onDismiss={() => !isProcessing && setSuccess(null)}>
            <p>{success}</p>
          </Banner>
        )}

        {syncResults && !success && (
          <Banner status="success" onDismiss={() => !isProcessing && setSyncResults(null)}>
            <p>Successfully synced {syncResults.processedShipments} historical shipments with a total cost of {formatCurrency(syncResults.totalShippingCost)}.</p>
          </Banner>
        )}

        {isProcessing ? (
          <BlockStack gap="4">
            <Card>
              <BlockStack gap="4">
                <Text variant="headingMd">Processing ShipStation Data</Text>
                <Text>{progressMessage}</Text>
                {/* Convert progress percentage to a decimal between 0-1 */}
                <ProgressBar
                  progress={progress / 100}
                  color="primary"
                  size="medium"
                />
                <Text variant="bodySm">Debug: progress={progress}, progress/100={progress/100}</Text>
                <Text variant="bodySm">Progress: {progress}%</Text>
                {parsedProgressData && parsedProgressData.lastUpdate && (
                  <Text variant="bodySm">Last update: {new Date(parsedProgressData.lastUpdate).toLocaleTimeString()}</Text>
                )}
                <Text variant="bodySm">Debug: Raw progress value = {progress}</Text>
                <InlineStack gap="4">
                  <Button
                    onClick={() => {
                      const newProgress = Math.min(100, progress + 10);
                      console.log(`Manually updating progress to ${newProgress}%`);
                      setProgress(newProgress);
                    }}
                  >
                    +10% Progress
                  </Button>
                  <Button
                    onClick={() => {
                      setProgress(50);
                      console.log(`Manually setting progress to 50%`);
                    }}
                  >
                    Set 50%
                  </Button>
                  <Button
                    onClick={() => {
                      setProgress(100);
                      console.log(`Manually setting progress to 100%`);
                    }}
                  >
                    Set 100%
                  </Button>
                </InlineStack>
                <Text variant="bodySm">This may take several minutes for stores with many shipments. You can leave this page and check back later.</Text>
              </BlockStack>
            </Card>
          </BlockStack>
        ) : (
          <>
            <Text>Connect your ShipStation account to track shipping and fulfillment costs</Text>

            <BlockStack gap="4">
              <Text>
                To integrate with ShipStation, enter your ShipStation store name below.
                You can find this in your ShipStation account settings.
              </Text>

              <InlineStack gap="4" align="start">
                <TextField
                  label="ShipStation Store Name"
                  value={storeName}
                  onChange={handleStoreNameChange}
                  autoComplete="off"
                  helpText="Enter the exact store name from ShipStation"
                />
              </InlineStack>

              <Button
                primary
                onClick={handleSaveMapping}
                loading={isSubmitting}
                disabled={isSubmitting || !storeName}
              >
                Connect Store
              </Button>
            </BlockStack>
          </>
        )}
      </BlockStack>
    </Card>
  );
}
