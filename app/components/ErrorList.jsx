/**
 * ErrorList Component
 *
 * A React component for displaying a list of unprocessable items (items that could not
 * be placed into any invoice balance category) in a table format.
 *
 * @param {Object} props - The component props.
 * @param {Array} props.errors - An array of error objects to display. Each error object should have the following properties:
 *   @param {string} errors[].productType - The product type as defined in the Shopify admin.
 *   @param {string} errors[].sku - The SKU (Stock Keeping Unit) of the product.
 *   @param {string} errors[].variant - The variant of the product.
 *   @param {string} errors[].errorField - The field where the error occurred.
 *   @param {string} errors[].message - The error message describing the issue.
 *
 * @returns {JSX.Element} A table displaying the list of errors.
 */
import "./ErrorList.css"

const ErrorList = ({errors}) => {
  return (
    <div>
      <table className="table table-striped">
        <thead>
          <tr>
            <th>Product Type</th>
            <th>Product SKU</th>
            <th>Variant</th>
            <th>Field w/Error</th>
            <th>Error Message</th>
          </tr>
        </thead>
        <tbody>
          {errors.map((error) => (
            <tr key={error.sku}>
              <td>{error.productType}</td>
              <td>{error.sku}</td>
              <td>{error.variant}</td>
              <td>{error.errorField}</td>
              <td>{error.message}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

export default ErrorList
