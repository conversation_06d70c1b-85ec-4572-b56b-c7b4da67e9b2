import { Text, Card } from "@shopify/polaris";
import { Total } from "./Total";

export function ShippingCostsTotal({ shippingCosts, fulfillmentCosts }) {
  // Check if we have any data to display
  const hasShippingCosts = shippingCosts && shippingCosts.length > 0;
  const hasFulfillmentCosts = fulfillmentCosts && Number(fulfillmentCosts.balance) > 0;

  // If no data at all, show an empty state
  if (!hasShippingCosts && !hasFulfillmentCosts) {
    console.log('No shipping or fulfillment costs to display');
    return (
      <Card>
        <div className="group-header">
          <Text variant="headingMd">Shipping & Fulfillment Costs</Text>
          <div className="group-total">
            <Text variant="bodyMd">Total: <strong>$0.00</strong></Text>
          </div>
        </div>
        <Text as="p" variant="bodyMd" alignment="center" style={{padding: '16px 0'}}>
          No shipping or fulfillment costs for the selected month.
        </Text>
      </Card>
    );
  }

  // Log what we have for debugging
  console.log('ShippingCostsTotal - shippingCosts:', shippingCosts ? `${shippingCosts.length} items` : 'none');
  console.log('ShippingCostsTotal - fulfillmentCosts:', fulfillmentCosts ? `balance: ${fulfillmentCosts.balance}` : 'none');

  // Calculate total shipping cost
  const totalShippingCost = shippingCosts ? shippingCosts.reduce((sum, cost) => sum + Number(cost.totalAmount), 0) : 0;
  const totalShipments = shippingCosts ? shippingCosts.reduce((sum, cost) => sum + Number(cost.quantity), 0) : 0;
  console.log('ShippingCostsTotal - totalShippingCost:', totalShippingCost);
  console.log('ShippingCostsTotal - totalShipments:', totalShipments);

  // Get fulfillment costs
  const fulfillmentCostAmount = fulfillmentCosts ? Number(fulfillmentCosts.balance) : 0;
  const fulfillmentCostQuantity = fulfillmentCosts ? Number(fulfillmentCosts.quantity) : 0;
  console.log('ShippingCostsTotal - fulfillmentCostAmount:', fulfillmentCostAmount);
  console.log('ShippingCostsTotal - fulfillmentCostQuantity:', fulfillmentCostQuantity);

  // Calculate combined total
  const totalAmount = totalShippingCost + fulfillmentCostAmount;

  // Create items array
  const items = [];

  // Add shipping costs items
  if (shippingCosts && shippingCosts.length > 0) {
    shippingCosts.forEach(cost => {
      items.push({
        category: `ShipStation Store ${cost.storeId}`,
        quantity: cost.quantity,
        balance: Number(cost.totalAmount)
      });
    });
  }

  // Add fulfillment costs item
  if (fulfillmentCosts && fulfillmentCostAmount > 0) {
    items.push({
      category: "Fulfillment Costs",
      quantity: fulfillmentCostQuantity,
      balance: fulfillmentCostAmount
    });
  }

  // Create a balance object in the format expected by the Total component
  const shippingBalance = {
    category: "Shipping & Fulfillment Costs",
    quantity: totalShipments + fulfillmentCostQuantity,
    balance: totalAmount,
    items: items
  };

  return <Total balance={shippingBalance} isGroup={true} />;
}
