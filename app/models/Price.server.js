/**
 * Price.server.js
 *
 * This file contains database operations for managing product pricing data.
 * It provides functions to create, read, update, and delete price records for different product categories.
 */

import db from "../db.server"

// Todo - Setting a price should also update the cost of goods on the Shopify product

/**
 * Retrieves a price record for a specific shop and category
 * @param {string} shop - The shop identifier
 * @param {string} category - The product category
 * @returns {Promise<object|null>} - The price record or null if not found
 */
export async function getPrice(shop, category) {
    const price = await db.price.findFirst({
        where: {
            shop,
            category
        }
    })

    if (!price) {
        return null;
    }

    return price;
}

/**
 * Retrieves a price record by its unique identifier
 * @param {string} id - The unique identifier of the price record
 * @returns {Promise<object|null>} - The price record or null if not found
 */
export async function getPriceById(id) {
    const price = await db.price.findFirst({
        where: { id }
    })

    if (!price) {
        return null;
    }

    return price;
}

/**
 * Retrieves all price records for a specific shop
 * @param {string} shop - The shop identifier
 * @returns {Promise<Array>} - An array of price records, or an empty array if none are found
 */
export async function getPrices(shop) {
    try {
        const prices = await db.price.findMany({
            where: { shop },
            orderBy: { category: "asc" }
        });

        return prices || [];
    } catch (error) {
        console.error(`Error getting prices for shop ${shop}: ${error.message}`);
        return [];
    }
}

/**
 * Retrieves all price records across all shops
 * @returns {Promise<Array>} - An array of price records, or an empty array if none are found
 */
export async function getAllPrices() {
    try {
        const prices = await db.price.findMany({
            orderBy: [
                { shop: "asc" },
                { category: "asc" }
            ]
        });

        if (prices.length === 0) return [];

        return prices;
    } catch (error) {
        console.error(`Error getting all prices: ${error.message}`);
        return [];
    }
}

/**
 * Retrieves all unique shops that have price records
 * @returns {Promise<Array>} - An array of unique shop identifiers
 */
export async function getUniqueShopsWithPrices() {
    try {
        const shops = await db.price.findMany({
            select: {
                shop: true
            },
            distinct: ['shop'],
            where: {
                AND: [
                    { shop: { not: "" } }
                ]
            },
            orderBy: {
                shop: "asc"
            }
        });

        console.log(`Found ${shops.length} unique shops with prices`);
        return shops.map(item => item.shop);
    } catch (error) {
        console.error(`Error getting unique shops with prices: ${error.message}`);
        return [];
    }
}

/**
 * Creates a new price record
 * @param {string} shop - The shop identifier
 * @param {string} category - The product category
 * @param {number} cost - The cost value
 * @returns {Promise<object>} - The created price record
 */
export async function createPrice(shop, category, cost) {
    const result = await db.price.create({
        data: {
            shop: shop,
            category: category,
            cost: cost
        }
    });

    return result;
}

/**
 * Updates an existing price record
 * @param {string} shop - The shop identifier
 * @param {string} category - The product category
 * @param {number} cost - The new cost value
 * @returns {Promise<object>} - The updated price record
 */
export async function setPrice(shop, category, cost) {
    try {
        // First, find the price record by shop and category
        const existingPrice = await db.price.findFirst({
            where: {
                shop,
                category
            }
        });

        if (!existingPrice) {
            throw new Error(`Price record not found for shop: ${shop}, category: ${category}`);
        }

        // Then update it by its ID
        const result = await db.price.update({
            where: {
                id: existingPrice.id
            },
            data: {
                cost: cost
            }
        });

        return result;
    } catch (error) {
        console.error(`Error updating price: ${error.message}`);
        throw error;
    }
}

/**
 * Deletes a price record by its unique identifier
 * @param {string} id - The unique identifier of the price record
 * @returns {Promise<object>} - The deleted price record
 */
export async function deletePrice(id) {
    const result = await db.price.delete({
        where: { id }
    })

    return result;
}

/**
 * Updates price records across all shops for a specific category
 * @param {string} category - The product category
 * @param {number} cost - The new cost value
 * @returns {Promise<{updated: number, shops: string[]}>} - The number of records updated and the list of shops affected
 */
export async function setPriceForAllShops(category, cost) {
    try {
        // Find all price records for this category
        const existingPrices = await db.price.findMany({
            where: {
                category
            }
        });

        if (existingPrices.length === 0) {
            return { updated: 0, shops: [] };
        }

        // Update each price record
        const updatePromises = existingPrices.map(async (price) => {
            await db.price.update({
                where: {
                    id: price.id
                },
                data: {
                    cost: cost
                }
            });
            return price.shop;
        });

        const updatedShops = await Promise.all(updatePromises);

        return {
            updated: existingPrices.length,
            shops: updatedShops
        };
    } catch (error) {
        console.error(`Error updating prices across all shops: ${error.message}`);
        throw error;
    }
}

/**
 * Initializes a shop with all default prices from the pricing data
 * @param {string} shop - The shop identifier
 * @param {object} pricingData - The default pricing data
 * @returns {Promise<{created: number, categories: string[]}>} - The number of prices created and the list of categories
 */
export async function initializeShopWithDefaultPrices(shop, pricingData) {
    try {
        // Get existing prices for the shop
        const existingPrices = await db.price.findMany({
            where: { shop },
            select: { category: true }
        });

        // Create a set of existing categories for quick lookup
        const existingCategories = new Set(existingPrices.map(price => price.category));

        // Create an array to hold all categories that need to be created
        const categoriesToCreate = [];

        // Check which categories need to be created
        for (const category in pricingData) {
            if (!existingCategories.has(category)) {
                categoriesToCreate.push({
                    shop,
                    category,
                    cost: pricingData[category]
                });
            }
        }

        if (categoriesToCreate.length === 0) {
            return { created: 0, categories: [] };
        }

        // Create all missing prices in a single transaction
        await db.$transaction(
            categoriesToCreate.map(data =>
                db.price.create({ data })
            )
        );

        return {
            created: categoriesToCreate.length,
            categories: categoriesToCreate.map(data => data.category)
        };
    } catch (error) {
        console.error(`Error initializing shop with default prices: ${error.message}`);
        throw error;
    }
}
