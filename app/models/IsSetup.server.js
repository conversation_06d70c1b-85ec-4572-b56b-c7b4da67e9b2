import db from "../db.server"

export async function getSetupFlag(shop) {
  const result = await db.setupFlag.findFirst({where: {shop}})

  if (!result) {
    return null;
  }

  return result;
}

export async function createSetupFlag(shop) {
  const result = await db.setupFlag.create({
    data: {
      shop: shop,
      isSetup: false,
    }
  });

  return result;
}

export async function setSetupFlag(shop, isSetup) {
  const result = await db.setupFlag.update({
    where: { shop },
    data: {
      isSetup: isSetup
    }
  });

  return result;
}

/**
 * Atomically check if setup is already in progress or completed, and mark it as in progress if not
 * This helps prevent race conditions where multiple routes try to process the same bulk operation
 * @param {string} shop - The shop identifier
 * @returns {Promise<{isProcessing: boolean, isSetup: boolean, canProceed: boolean}>} - Status and whether this request can proceed
 */
export async function checkAndMarkSetupInProgress(shop) {
  // Use a transaction to ensure atomicity
  return await db.$transaction(async (tx) => {
    // Get the current setup flag with a lock to prevent concurrent updates
    const setupFlag = await tx.setupFlag.findFirst({
      where: { shop }
    });

    if (!setupFlag) {
      // Shop doesn't exist in setup flags table
      return { isProcessing: false, isSetup: false, canProceed: false };
    }

    if (setupFlag.isSetup) {
      // Setup is already completed
      return { isProcessing: false, isSetup: true, canProceed: false };
    }

    // Check if we have a processing flag
    if (setupFlag.isProcessing) {
      // Setup is already in progress by another request
      return { isProcessing: true, isSetup: false, canProceed: false };
    }

    // Mark as processing
    await tx.setupFlag.update({
      where: { shop },
      data: { isProcessing: true }
    });

    // Setup is not completed and we've now marked it as in progress
    // This request can proceed with processing
    return { isProcessing: true, isSetup: false, canProceed: true };
  });
}

/**
 * Mark setup as completed and no longer processing
 * @param {string} shop - The shop identifier
 * @returns {Promise<object>} - The updated setup flag
 */
export async function markSetupCompleted(shop) {
  return await db.setupFlag.update({
    where: { shop },
    data: {
      isSetup: true,
      isProcessing: false
    }
  });
}

/**
 * Reset the processing flag without changing the setup status
 * This is useful when a process fails or when starting a new bulk operation
 * @param {string} shop - The shop identifier
 * @returns {Promise<object>} - The updated setup flag
 */
export async function resetProcessingFlag(shop) {
  console.log(`[resetProcessingFlag] Resetting processing flag for shop: ${shop}`);
  return await db.setupFlag.update({
    where: { shop },
    data: {
      isProcessing: false
    }
  });
}
