import db from "../db.server"

/**
 * Create a new reconciliation job
 * @param {string} shop - The shop identifier
 * @param {string} type - The job type ('scheduled', 'manual', 'missing-orders')
 * @param {Date} startDate - The start date for reconciliation
 * @param {Date} endDate - The end date for reconciliation
 * @returns {Promise<object>} - The created job
 */
export async function createReconciliationJob(shop, type, startDate, endDate) {
  try {
    const job = await db.reconciliationJob.create({
      data: {
        shop,
        type,
        status: 'queued',
        startDate,
        endDate,
        startedAt: new Date()
      }
    });
    
    return job;
  } catch (error) {
    console.error(`Error creating reconciliation job: ${error.message}`);
    throw error;
  }
}

/**
 * Update a reconciliation job's status
 * @param {number} id - The job ID
 * @param {string} status - The new status ('running', 'completed', 'failed')
 * @param {object} data - Additional data to update
 * @returns {Promise<object>} - The updated job
 */
export async function updateReconciliationJobStatus(id, status, data = {}) {
  try {
    const updateData = {
      status,
      ...data
    };
    
    // If status is 'completed' or 'failed', set completedAt
    if (status === 'completed' || status === 'failed') {
      updateData.completedAt = new Date();
    }
    
    const job = await db.reconciliationJob.update({
      where: { id },
      data: updateData
    });
    
    return job;
  } catch (error) {
    console.error(`Error updating reconciliation job: ${error.message}`);
    throw error;
  }
}

/**
 * Update a reconciliation job's results
 * @param {number} id - The job ID
 * @param {number} processed - Number of orders processed
 * @param {number} skipped - Number of orders skipped
 * @param {number} failed - Number of orders failed
 * @returns {Promise<object>} - The updated job
 */
export async function updateReconciliationJobResults(id, processed, skipped, failed) {
  try {
    const job = await db.reconciliationJob.update({
      where: { id },
      data: {
        ordersProcessed: processed,
        ordersSkipped: skipped,
        ordersFailed: failed
      }
    });
    
    return job;
  } catch (error) {
    console.error(`Error updating reconciliation job results: ${error.message}`);
    throw error;
  }
}

/**
 * Get a reconciliation job by ID
 * @param {number} id - The job ID
 * @returns {Promise<object>} - The job
 */
export async function getReconciliationJob(id) {
  try {
    const job = await db.reconciliationJob.findUnique({
      where: { id }
    });
    
    return job;
  } catch (error) {
    console.error(`Error getting reconciliation job: ${error.message}`);
    return null;
  }
}

/**
 * Get all reconciliation jobs for a shop
 * @param {string} shop - The shop identifier
 * @param {number} limit - Maximum number of jobs to return (default: 10)
 * @returns {Promise<Array>} - Array of jobs
 */
export async function getReconciliationJobs(shop, limit = 10) {
  try {
    const jobs = await db.reconciliationJob.findMany({
      where: { shop },
      orderBy: { startedAt: 'desc' },
      take: limit
    });
    
    return jobs;
  } catch (error) {
    console.error(`Error getting reconciliation jobs: ${error.message}`);
    return [];
  }
}

/**
 * Get the most recent reconciliation job for a shop
 * @param {string} shop - The shop identifier
 * @returns {Promise<object>} - The most recent job
 */
export async function getMostRecentReconciliationJob(shop) {
  try {
    const job = await db.reconciliationJob.findFirst({
      where: { shop },
      orderBy: { startedAt: 'desc' }
    });
    
    return job;
  } catch (error) {
    console.error(`Error getting most recent reconciliation job: ${error.message}`);
    return null;
  }
}

/**
 * Get all running reconciliation jobs
 * @returns {Promise<Array>} - Array of running jobs
 */
export async function getRunningReconciliationJobs() {
  try {
    const jobs = await db.reconciliationJob.findMany({
      where: { status: 'running' },
      orderBy: { startedAt: 'asc' }
    });
    
    return jobs;
  } catch (error) {
    console.error(`Error getting running reconciliation jobs: ${error.message}`);
    return [];
  }
}

/**
 * Mark a reconciliation job as failed
 * @param {number} id - The job ID
 * @param {string} error - The error message
 * @returns {Promise<object>} - The updated job
 */
export async function markReconciliationJobFailed(id, error) {
  try {
    const job = await db.reconciliationJob.update({
      where: { id },
      data: {
        status: 'failed',
        completedAt: new Date(),
        error
      }
    });
    
    return job;
  } catch (error) {
    console.error(`Error marking reconciliation job as failed: ${error.message}`);
    throw error;
  }
}
