import db from "../db.server"

/**
 * Creates or updates a ShippingCost record
 * @param {string} shop - The shop identifier
 * @param {string} storeId - The ShipStation store ID
 * @param {number} shippingAmount - The shipping cost amount
 * @param {number} markupPercentage - The markup percentage (default: 10)
 * @param {Date|null} labelDate - The date of the label (optional, defaults to current date)
 * @returns {Promise<object>} - The created or updated ShippingCost
 */
export async function updateShippingCost(shop, storeId, shippingAmount, markupPercentage = 10, labelDate = null) {
  try {
    console.log(`Updating shipping cost for shop: ${shop}, storeId: ${storeId}, amount: ${shippingAmount}`);

    // Validate inputs
    if (!shop || !storeId || isNaN(shippingAmount) || shippingAmount <= 0) {
      console.error(`Invalid shipping cost parameters: shop=${shop}, storeId=${storeId}, amount=${shippingAmount}`);
      throw new Error('Invalid shipping cost parameters');
    }

    // Calculate markup amount
    const markupAmount = (shippingAmount * markupPercentage) / 100;
    const totalAmount = shippingAmount + markupAmount;

    console.log(`Calculated markup: ${markupAmount.toFixed(2)}, total: ${totalAmount.toFixed(2)}`);

    // Use label date if provided, otherwise use current date
    const dateToUse = labelDate instanceof Date ? labelDate : new Date();
    const month = dateToUse.getMonth();
    const year = dateToUse.getFullYear();

    console.log(`Using month/year: ${month}/${year} from ${labelDate ? 'label date' : 'current date'}`);

    if (labelDate) {
      console.log(`Label date: ${labelDate.toISOString()}`);
    }

    // Check if a shipping cost record already exists for this store
    const existingRecord = await db.shippingCost.findFirst({
      where: {
        shop,
        storeId,
        month: month,
        year: year
      }
    });

    if (existingRecord) {
      console.log(`Found existing record with ID: ${existingRecord.id}, current quantity: ${existingRecord.quantity}`);

      // Update existing record
      const updatedRecord = await db.shippingCost.update({
        where: { id: existingRecord.id },
        data: {
          quantity: existingRecord.quantity + 1, // Increment quantity for each shipment
          shippingCost: { increment: shippingAmount },
          markupAmount: { increment: markupAmount },
          totalAmount: { increment: totalAmount },
          updatedAt: new Date()
        }
      });

      console.log(`Updated record: quantity=${updatedRecord.quantity}, total=${updatedRecord.totalAmount}`);

      // Create a transaction record for this update
      const transaction = await db.shippingTransaction.create({
        data: {
          shippingCostId: existingRecord.id,
          amount: totalAmount,
          shippingAmount: shippingAmount,
          markupAmount: markupAmount,
          description: `Shipping cost: $${shippingAmount.toFixed(2)} + ${markupPercentage}% markup: $${markupAmount.toFixed(2)}`,
          createdAt: new Date(),
          metadata: JSON.stringify({
            shippingAmount,
            markupAmount,
            markupPercentage,
            storeId
          })
        }
      });

      console.log(`Created transaction record with ID: ${transaction.id}`);
      return updatedRecord;
    } else {
      console.log(`No existing record found, creating new record`);

      // Create new record
      const newRecord = await db.shippingCost.create({
        data: {
          shop,
          storeId,
          month: month,
          year: year,
          quantity: 1, // Start with 1 for the first shipment
          shippingCost: shippingAmount,
          markupAmount: markupAmount,
          totalAmount: totalAmount,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      console.log(`Created new record with ID: ${newRecord.id}`);

      // Create a transaction record for this new record
      const transaction = await db.shippingTransaction.create({
        data: {
          shippingCostId: newRecord.id,
          amount: totalAmount,
          shippingAmount: shippingAmount,
          markupAmount: markupAmount,
          description: `Shipping cost: $${shippingAmount.toFixed(2)} + ${markupPercentage}% markup: $${markupAmount.toFixed(2)}`,
          createdAt: new Date(),
          metadata: JSON.stringify({
            shippingAmount,
            markupAmount,
            markupPercentage,
            storeId
          })
        }
      });

      console.log(`Created transaction record with ID: ${transaction.id}`);
      return newRecord;
    }
  } catch (error) {
    console.error(`Error updating shipping cost: ${error.message}`);
    console.error(error.stack);
    throw error;
  }
}

/**
 * Get the shipping cost for a store
 * @param {string} shop - The shop identifier
 * @param {string} storeId - The ShipStation store ID
 * @returns {Promise<object>} - The shipping cost with recent transactions
 */
export async function getShippingCost(shop, storeId) {
  try {
    // Get current date for month/year
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    const record = await db.shippingCost.findFirst({
      where: {
        shop,
        storeId,
        month: currentMonth,
        year: currentYear
      }
    });

    if (!record) {
      return null;
    }

    // Get recent transactions for this record
    const transactions = await db.shippingTransaction.findMany({
      where: {
        shippingCostId: record.id
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    return {
      ...record,
      transactions
    };
  } catch (error) {
    console.error(`Error getting shipping cost: ${error.message}`);
    return null;
  }
}

/**
 * Get all shipping costs for a shop
 * @param {string} shop - The shop identifier
 * @returns {Promise<Array>} - Array of shipping costs
 */
export async function getAllShippingCosts(shop) {
  try {
    const records = await db.shippingCost.findMany({
      where: { shop },
      orderBy: [
        { year: 'desc' },
        { month: 'desc' }
      ]
    });

    return records;
  } catch (error) {
    console.error(`Error getting all shipping costs: ${error.message}`);
    return [];
  }
}

/**
 * Get shipping costs for a specific month and year
 * @param {string} shop - The shop identifier
 * @param {number} month - The month (0-11)
 * @param {number} year - The year
 * @returns {Promise<Array>} - Array of shipping costs
 */
export async function getShippingCostsByMonth(shop, month, year) {
  try {
    const records = await db.shippingCost.findMany({
      where: {
        shop,
        month,
        year
      }
    });

    return records;
  } catch (error) {
    console.error(`Error getting shipping costs by month: ${error.message}`);
    return [];
  }
}

/**
 * Updates the fulfillment costs for a shipped order
 * @param {string} shop - The shop identifier
 * @param {number} fulfillmentCost - The fulfillment cost per order (default: 1.50)
 * @param {Date|null} labelDate - The date of the label (optional, defaults to current date)
 * @returns {Promise<object>} - The created or updated InvoiceBalance
 */
export async function updateFulfillmentCost(shop, fulfillmentCost = 1.50, labelDate = null) {
  try {
    console.log(`Updating fulfillment cost for shop: ${shop}, amount: ${fulfillmentCost}`);

    // Validate inputs
    if (!shop || isNaN(fulfillmentCost) || fulfillmentCost <= 0) {
      console.error(`Invalid fulfillment cost parameters: shop=${shop}, amount=${fulfillmentCost}`);
      throw new Error('Invalid fulfillment cost parameters');
    }

    // Use label date if provided, otherwise use current date
    const dateToUse = labelDate instanceof Date ? labelDate : new Date();
    const month = dateToUse.getMonth();
    const year = dateToUse.getFullYear();

    console.log(`Using month/year: ${month}/${year} from ${labelDate ? 'label date' : 'current date'}`);

    if (labelDate) {
      console.log(`Label date: ${labelDate.toISOString()}`);
    }

    // Check if a fulfillment cost record already exists for this month
    const existingRecord = await db.invoiceBalance.findFirst({
      where: {
        shop,
        category: 'Fulfillment Costs',
        month: month,
        year: year
      }
    });

    if (existingRecord) {
      console.log(`Found existing fulfillment cost record with ID: ${existingRecord.id}, current quantity: ${existingRecord.quantity}`);

      // Update existing record
      const updatedRecord = await db.invoiceBalance.update({
        where: { id: existingRecord.id },
        data: {
          quantity: { increment: 1 }, // Increment quantity for each order
          balance: { increment: fulfillmentCost } // Add the fulfillment cost
        }
      });

      console.log(`Updated fulfillment cost record: quantity=${updatedRecord.quantity}, total=${updatedRecord.balance}`);

      // Create a transaction record for this update
      const transaction = await db.invoiceTransaction.create({
        data: {
          invoiceBalanceId: existingRecord.id,
          amount: fulfillmentCost,
          description: `Fulfillment cost for shipped order: $${fulfillmentCost.toFixed(2)}`,
          createdAt: new Date(),
          metadata: JSON.stringify({
            fulfillmentCost,
            date: new Date().toISOString()
          })
        }
      });

      console.log(`Created fulfillment cost transaction record with ID: ${transaction.id}`);
      return updatedRecord;
    } else {
      console.log(`No existing fulfillment cost record found, creating new record`);

      // Create new record
      const newRecord = await db.invoiceBalance.create({
        data: {
          shop,
          category: 'Fulfillment Costs',
          month: month,
          year: year,
          quantity: 1, // Start with 1 for the first order
          balance: fulfillmentCost
        }
      });

      console.log(`Created new fulfillment cost record with ID: ${newRecord.id}`);

      // Create a transaction record for this new record
      const transaction = await db.invoiceTransaction.create({
        data: {
          invoiceBalanceId: newRecord.id,
          amount: fulfillmentCost,
          description: `Fulfillment cost for shipped order: $${fulfillmentCost.toFixed(2)}`,
          createdAt: new Date(),
          metadata: JSON.stringify({
            fulfillmentCost,
            date: new Date().toISOString()
          })
        }
      });

      console.log(`Created fulfillment cost transaction record with ID: ${transaction.id}`);
      return newRecord;
    }
  } catch (error) {
    console.error(`Error updating fulfillment cost: ${error.message}`);
    console.error(error.stack);
    throw error;
  }
}
