import db from "../db.server"

/**
 * Get all processed orders for a specific date range
 * @param {string} shop - The shop identifier
 * @param {Date} startDate - The start date
 * @param {Date} endDate - The end date
 * @returns {Promise<Array>} - Array of processed order IDs
 */
export async function getProcessedOrdersInDateRange(shop, startDate, endDate) {
  try {
    if (!shop || !startDate || !endDate) {
      return [];
    }

    const processed = await db.processedWebhook.findMany({
      where: {
        shop,
        topic: 'ORDERS_CREATE',
        orderId: { not: null },
        processedAt: {
          gte: startDate,
          lte: endDate
        }
      },
      select: {
        orderId: true
      }
    });

    return processed.map(p => p.orderId);
  } catch (error) {
    console.error(`Error getting processed orders in date range: ${error.message}`);
    return [];
  }
}

/**
 * Check if a webhook has already been processed
 * @param {string} shop - The shop identifier
 * @param {string} webhookId - The unique webhook ID
 * @returns {Promise<boolean>} - Whether the webhook has been processed
 */
export async function hasProcessedWebhook(shop, webhookId) {
  try {
    if (!shop || !webhookId) {
      console.error('Invalid shop or webhookId');
      return false;
    }

    // Ensure webhookId is a string
    const webhookIdStr = String(webhookId);
    console.log(`Looking for processed webhook with ID: ${webhookIdStr} (type: ${typeof webhookIdStr})`);

    const processed = await db.processedWebhook.findFirst({
      where: {
        shop,
        webhookId: webhookIdStr
      }
    });

    return processed !== null;
  } catch (error) {
    console.error(`Error checking processed webhook: ${error.message}`);
    // In case of error, return false to allow processing
    return false;
  }
}

/**
 * Check if an event has already been processed using the Shopify Event ID
 * @param {string} shop - The shop identifier
 * @param {string} eventId - The Shopify Event ID
 * @returns {Promise<boolean>} - Whether the event has been processed
 */
export async function hasProcessedEvent(shop, eventId) {
  try {
    if (!shop || !eventId) {
      console.error('Invalid shop or eventId');
      return false;
    }

    // Ensure eventId is a string
    const eventIdStr = String(eventId);
    console.log(`Looking for processed event with ID: ${eventIdStr}`);

    const processed = await db.processedWebhook.findFirst({
      where: {
        shop,
        eventId: eventIdStr
      }
    });

    if (processed) {
      console.log(`Found previously processed event: ${eventIdStr} (processed at ${processed.processedAt})`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`Error checking processed event: ${error.message}`);
    // In case of error, return false to allow processing
    return false;
  }
}

/**
 * Check and mark an event as processed in a single atomic operation
 * This helps prevent race conditions with duplicate webhooks
 * @param {string} shop - The shop identifier
 * @param {string} eventId - The Shopify Event ID
 * @param {string} topic - The webhook topic
 * @param {string} orderId - The order ID (optional)
 * @returns {Promise<boolean>} - Whether the event was already processed
 */
export async function checkAndMarkEventProcessed(shop, eventId, topic, orderId = null) {
  if (!shop || !eventId) {
    console.error('Invalid shop or eventId');
    return false;
  }

  // Ensure eventId is a string
  const eventIdStr = String(eventId);
  console.log(`Checking and marking event with ID: ${eventIdStr}`);

  try {
    // First, check if the event has already been processed
    const webhookId = `event-${eventIdStr}`;
    const existing = await db.processedWebhook.findFirst({
      where: {
        shop,
        webhookId
      }
    });

    if (existing) {
      console.log(`Event ${eventIdStr} was already processed. Skipping.`);
      return true;
    }

    // Event hasn't been processed, so create a new record
    await db.processedWebhook.create({
      data: {
        shop,
        topic,
        webhookId,
        eventId: eventIdStr,
        orderId: orderId ? String(orderId) : null,
        processedAt: new Date()
      }
    });

    console.log(`Event ${eventIdStr} was not previously processed. Marked as processed.`);
    return false;
  } catch (error) {
    // For any errors, log and return false to allow processing
    console.error(`Error checking and marking event: ${error.message}`);
    return false;
  }
}

/**
 * Check if an order has already been processed, regardless of subscription ID
 * @param {string} shop - The shop identifier
 * @param {string} orderId - The order ID
 * @returns {Promise<boolean>} - Whether the order has been processed
 */
export async function hasProcessedOrder(shop, orderId) {
  try {
    if (!shop || !orderId) {
      console.error('Invalid shop or orderId');
      return false;
    }

    // Ensure orderId is a string
    const orderIdStr = String(orderId);
    console.log(`Looking for processed order with ID: ${orderIdStr}`);

    const processed = await db.processedWebhook.findFirst({
      where: {
        shop,
        orderId: orderIdStr
      }
    });

    if (processed) {
      console.log(`Found previously processed order: ${orderIdStr} (processed at ${processed.processedAt})`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`Error checking processed order: ${error.message}`);
    // In case of error, return false to allow processing
    return false;
  }
}

/**
 * Mark a webhook as processed
 * @param {string} shop - The shop identifier
 * @param {string} topic - The webhook topic
 * @param {string} webhookId - The unique webhook ID
 * @returns {Promise<object>} - The created record
 */
export async function markWebhookProcessed(shop, topic, webhookId, orderId = null, subscriptionId = null, eventId = null) {
  try {
    // Validate inputs
    if (!shop || !topic || !webhookId) {
      console.error('Invalid shop, topic, or webhookId');
      return null;
    }

    // Ensure webhookId is a string
    const webhookIdStr = String(webhookId);
    console.log(`Marking webhook as processed with ID: ${webhookIdStr} (type: ${typeof webhookIdStr})`);

    // Check if webhook has already been processed
    const existing = await db.processedWebhook.findFirst({
      where: {
        shop,
        webhookId: webhookIdStr
      }
    });

    if (existing) {
      console.log(`Webhook ${webhookIdStr} was already processed`);
      return existing;
    }

    // Extract the raw order ID if it's embedded in the webhookId
    let extractedOrderId = orderId;
    if (!extractedOrderId && webhookIdStr.startsWith('order-')) {
      const parts = webhookIdStr.split('-');
      if (parts.length >= 2) {
        extractedOrderId = parts[1];
      }
    }

    // Create the record
    const result = await db.processedWebhook.create({
      data: {
        shop,
        topic,
        webhookId: webhookIdStr,
        eventId: eventId ? String(eventId) : null,
        orderId: extractedOrderId ? String(extractedOrderId) : null,
        subscriptionId: subscriptionId ? String(subscriptionId) : null,
        processedAt: new Date()
      }
    });
    return result;
  } catch (error) {
    // For any errors, log and rethrow
    console.error(`Error marking webhook as processed: ${error.message}`);
    throw error;
  }
}

/**
 * Mark an order as processed without a specific webhook
 * This is useful for manual reconciliation or test orders
 * @param {string} shop - The shop identifier
 * @param {string} orderId - The order ID
 * @returns {Promise<object>} - The created record
 */
export async function markOrderProcessed(shop, orderId) {
  try {
    // Validate inputs
    if (!shop || !orderId) {
      console.error('Invalid shop or orderId');
      return null;
    }

    // Ensure orderId is a string
    const orderIdStr = String(orderId);
    console.log(`Marking order as processed with ID: ${orderIdStr}`);

    // Check if this order has already been processed
    const existing = await hasProcessedOrder(shop, orderIdStr);
    if (existing) {
      console.log(`Order ${orderIdStr} was already processed, skipping`);
      return null;
    }

    // Create a synthetic webhook ID for this order
    const webhookId = `manual-order-${orderIdStr}-${Date.now()}`;

    // Try to create the record
    const result = await db.processedWebhook.create({
      data: {
        shop,
        topic: 'ORDERS_CREATE', // Use the standard order creation topic
        webhookId,
        orderId: orderIdStr,
        processedAt: new Date()
      }
    });

    console.log(`Successfully marked order ${orderIdStr} as processed`);
    return result;
  } catch (error) {
    console.error(`Error marking order as processed: ${error.message}`);
    throw error;
  }
}
