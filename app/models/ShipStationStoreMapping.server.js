import db from "../db.server"

/**
 * Get a store mapping by store ID
 * @param {string} storeId - The ShipStation store ID
 * @returns {Promise<object>} - The store mapping
 */
export async function getStoreMappingByStoreId(storeId) {
  try {
    // Validate storeId
    if (!storeId) {
      console.error('getStoreMappingByStoreId called with undefined or null storeId');
      return null;
    }

    // Convert storeId to string to match database schema
    const storeIdString = String(storeId);

    console.log(`Looking up store mapping for storeId: ${storeIdString} (original type: ${typeof storeId})`);

    const mapping = await db.shipStationStoreMapping.findUnique({
      where: { storeId: storeIdString }
    });

    if (mapping) {
      if (mapping.shop) {
        console.log(`Found store mapping for storeId ${storeId}: shop=${mapping.shop}`);
      } else {
        console.log(`Found ShipStation store with ID ${storeId}, but it's not mapped to any shop`);
      }
    } else {
      console.log(`No store mapping found for storeId: ${storeId}`);
    }

    return mapping;
  } catch (error) {
    console.error(`Error getting store mapping by store ID: ${error.message}`);
    console.error(error.stack);
    return null;
  }
}

/**
 * Get all store mappings for a shop
 * @param {string} shop - The shop identifier
 * @returns {Promise<Array>} - Array of store mappings
 */
export async function getStoreMappingsByShop(shop) {
  try {
    const mappings = await db.shipStationStoreMapping.findMany({
      where: { shop }
    });

    return mappings;
  } catch (error) {
    console.error(`Error getting store mappings by shop: ${error.message}`);
    return [];
  }
}

/**
 * Create or update a store mapping
 * @param {string} storeId - The ShipStation store ID
 * @param {string} shop - The shop identifier
 * @param {string} [storeName] - The ShipStation store name (optional)
 * @returns {Promise<object>} - The created or updated store mapping
 */
export async function createOrUpdateStoreMapping(storeId, shop, storeName = null) {
  try {
    // Convert storeId to string to match database schema
    const storeIdString = String(storeId);

    const existingMapping = await db.shipStationStoreMapping.findUnique({
      where: { storeId: storeIdString }
    });

    if (existingMapping) {
      // Update existing mapping
      const updateData = {
        shop,
        updatedAt: new Date()
      };

      // Only update storeName if provided
      if (storeName) {
        updateData.storeName = storeName;
      }

      const updatedMapping = await db.shipStationStoreMapping.update({
        where: { storeId: storeIdString },
        data: updateData
      });

      return updatedMapping;
    } else {
      // Create new mapping
      const createData = {
        storeId: storeIdString,
        shop,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Only include storeName if provided
      if (storeName) {
        createData.storeName = storeName;
      }

      const newMapping = await db.shipStationStoreMapping.create({
        data: createData
      });

      return newMapping;
    }
  } catch (error) {
    console.error(`Error creating or updating store mapping: ${error.message}`);
    throw error;
  }
}

/**
 * Delete a store mapping
 * @param {string} storeId - The ShipStation store ID
 * @returns {Promise<object>} - The deleted store mapping
 */
export async function deleteStoreMapping(storeId) {
  try {
    // Convert storeId to string to match database schema
    const storeIdString = String(storeId);

    const deletedMapping = await db.shipStationStoreMapping.delete({
      where: { storeId: storeIdString }
    });

    return deletedMapping;
  } catch (error) {
    console.error(`Error deleting store mapping: ${error.message}`);
    throw error;
  }
}

/**
 * Get all store mappings
 * @returns {Promise<Array>} - Array of all store mappings
 */
export async function getAllStoreMappings() {
  try {
    const mappings = await db.shipStationStoreMapping.findMany({
      orderBy: { createdAt: 'desc' }
    });

    return mappings;
  } catch (error) {
    console.error(`Error getting all store mappings: ${error.message}`);
    return [];
  }
}

/**
 * Get all mapped ShipStation stores (those with a shop assigned)
 * @returns {Promise<Array>} - Array of mapped ShipStation stores
 */
export async function getMappedShipStationStores() {
  try {
    const mappings = await db.shipStationStoreMapping.findMany({
      where: {
        shop: {
          not: null
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return mappings;
  } catch (error) {
    console.error(`Error getting mapped ShipStation stores: ${error.message}`);
    return [];
  }
}

/**
 * Get all unmapped ShipStation stores (those without a shop assigned)
 * @returns {Promise<Array>} - Array of unmapped ShipStation stores
 */
export async function getUnmappedShipStationStores() {
  try {
    const mappings = await db.shipStationStoreMapping.findMany({
      where: {
        shop: null
      },
      orderBy: { createdAt: 'desc' }
    });

    return mappings;
  } catch (error) {
    console.error(`Error getting unmapped ShipStation stores: ${error.message}`);
    return [];
  }
}

/**
 * Create a ShipStation store record without mapping it to a shop
 * @param {string} storeId - The ShipStation store ID
 * @param {string} storeName - The ShipStation store name
 * @returns {Promise<object>} - The created store record
 */
export async function createShipStationStore(storeId, storeName) {
  try {
    // Convert storeId to string to match database schema
    const storeIdString = String(storeId);

    const existingStore = await db.shipStationStoreMapping.findUnique({
      where: { storeId: storeIdString }
    });

    if (existingStore) {
      // Update existing store
      const updatedStore = await db.shipStationStoreMapping.update({
        where: { storeId: storeIdString },
        data: {
          storeName,
          updatedAt: new Date()
        }
      });

      return updatedStore;
    } else {
      // Create new store
      const newStore = await db.shipStationStoreMapping.create({
        data: {
          storeId: storeIdString,
          storeName,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      return newStore;
    }
  } catch (error) {
    console.error(`Error creating ShipStation store: ${error.message}`);
    throw error;
  }
}

/**
 * Bulk create or update ShipStation stores
 * @param {Array} stores - Array of ShipStation store objects with storeId and storeName
 * @returns {Promise<Array>} - Array of created or updated store records
 */
export async function bulkCreateOrUpdateShipStationStores(stores) {
  try {
    const results = [];

    for (const store of stores) {
      // Skip if missing required fields
      if (!store.storeId || !store.storeName) {
        console.warn(`Skipping store with missing required fields: ${JSON.stringify(store)}`);
        continue;
      }

      // Create or update the store
      const result = await createShipStationStore(store.storeId, store.storeName);
      results.push(result);
    }

    return results;
  } catch (error) {
    console.error(`Error bulk creating/updating ShipStation stores: ${error.message}`);
    throw error;
  }
}
