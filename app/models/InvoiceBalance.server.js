import db from "../db.server"

export async function getInvoiceBalance(shop, category, month, year) {
  const balance = await db.invoiceBalance.findFirst({
    where: {
      shop,
      category: category,
      month: month,
      year: year,
    },
  });

  if (!balance) {
    return null;
  }

  return balance;
}

export async function getInvoiceBalanceById(id) {
  const balance = await db.invoiceBalance.findFirst({where: { id }})

  if (!balance) {
    return null;
  }

  return balance;
}

export async function getInvoiceBalances(shop) {
  try {
    const balances = await db.invoiceBalance.findMany({
      where: { shop },
      orderBy: { quantity: "desc" },
    });

    if (balances.length === 0) return [];

    return balances;
  } catch (error) {
    console.error(`Error fetching invoice balances: ${error.message}`)
    return []
  }
}

export async function getInvoiceBalancesByMonth(shop, month, year) {
  try {
    const balances = await db.invoiceBalance.findMany({
      where: {
        shop,
        month,
        year,
      },
      orderBy: {quantity: "desc"},
    });

    if (balances.length === 0) return [];

    return balances;
  } catch (error) {
    console.error(`Error fetching balances for month ${month}/${year}: ${error.message}`);
    return [];
  }
}

/**
 * Get all invoice balances for a specific date range
 * @param {string} shop - The shop identifier
 * @param {Date} startDate - The start date
 * @param {Date} endDate - The end date
 * @returns {Promise<Array>} - Array of invoice balances
 */
export async function getInvoiceBalancesByDateRange(shop, startDate, endDate) {
  try {
    if (!shop || !startDate || !endDate) {
      return [];
    }

    // Extract month and year ranges
    const startMonth = startDate.getMonth();
    const startYear = startDate.getFullYear();
    const endMonth = endDate.getMonth();
    const endYear = endDate.getFullYear();

    // Build a complex where clause to handle date ranges that span multiple months/years
    const whereClause = {
      shop,
      OR: []
    };

    // If start and end are in the same year
    if (startYear === endYear) {
      whereClause.OR.push({
        year: startYear,
        month: {
          gte: startMonth,
          lte: endMonth
        }
      });
    } else {
      // Handle start year (partial year from startMonth to December)
      whereClause.OR.push({
        year: startYear,
        month: {
          gte: startMonth
        }
      });

      // Handle any full years in between
      for (let year = startYear + 1; year < endYear; year++) {
        whereClause.OR.push({
          year
        });
      }

      // Handle end year (partial year from January to endMonth)
      whereClause.OR.push({
        year: endYear,
        month: {
          lte: endMonth
        }
      });
    }

    const balances = await db.invoiceBalance.findMany({
      where: whereClause,
      orderBy: [
        { year: "asc" },
        { month: "asc" },
        { quantity: "desc" }
      ]
    });

    return balances;
  } catch (error) {
    console.error(`Error getting invoice balances in date range: ${error.message}`);
    return [];
  }
}

export async function createInvoiceBalance(shop, month, year, category, quantity, balance, group) {
  const result = await db.invoiceBalance.create({
    data: {
      shop: shop,
      month: month,
      year: year,
      category: category,
      quantity: quantity,
      balance: balance,
      groupId: group
    }
  });

  return result
}

export async function setInvoiceBalance(shop, month, year, category, quantity, total, group) {
  let balance = await getInvoiceBalance(shop, category, month, year)

  if (!balance) {
    // If balance doesn't exist, create it
    return createInvoiceBalance(shop, month, year, category, quantity, total, group || 0);
  }

  const result = await db.invoiceBalance.update({
    where: {
      id: balance.id
    },
    data: {
      quantity: quantity,
      balance: total,
      // Only update groupId if it's provided
      ...(group !== undefined && { groupId: group })
    }
  })

  return result;
}

export async function deleteInvoiceBalance(id) {
  const result = await db.invoiceBalance.delete({
    where: {
      id,
    },
  })

  return result;
}

/**
 * Creates or updates an InvoiceBalance for shipping costs
 * @param {string} shop - The shop identifier
 * @param {string} vendorId - The vendor identifier
 * @param {number} shippingCost - The shipping cost amount
 * @param {number} markupPercentage - The markup percentage (default: 10)
 * @returns {Promise<object>} - The created or updated InvoiceBalance
 */
export async function updateShippingCostsBalance(shop, vendorId, shippingCost, markupPercentage = 10) {
  try {
    // Calculate markup amount
    const markupAmount = (shippingCost * markupPercentage) / 100;
    const totalAmount = shippingCost + markupAmount;

    // Get current date for month/year
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    // Check if a shipping costs balance already exists for this vendor
    const existingBalance = await db.invoiceBalance.findFirst({
      where: {
        shop,
        category: 'shipping costs',
        month: currentMonth,
        year: currentYear,
        vendorId
      }
    });

    if (existingBalance) {
      // Update existing balance
      const updatedBalance = await db.invoiceBalance.update({
        where: { id: existingBalance.id },
        data: {
          quantity: existingBalance.quantity + 1, // Increment quantity for each shipment
          balance: existingBalance.balance + totalAmount,
        }
      });

      // Create a transaction record for this update
      await db.invoiceTransaction.create({
        data: {
          invoiceBalanceId: existingBalance.id,
          amount: totalAmount,
          description: `Shipping cost: $${shippingCost.toFixed(2)} + ${markupPercentage}% markup: $${markupAmount.toFixed(2)}`,
          createdAt: new Date(),
          metadata: JSON.stringify({
            shippingCost,
            markupAmount,
            markupPercentage,
            vendorId
          })
        }
      });

      return updatedBalance;
    } else {
      // Create new balance
      const newBalance = await db.invoiceBalance.create({
        data: {
          shop,
          category: 'shipping costs',
          month: currentMonth,
          year: currentYear,
          quantity: 1, // Start with 1 for the first shipment
          balance: totalAmount,
          vendorId
        }
      });

      // Create a transaction record for this new balance
      await db.invoiceTransaction.create({
        data: {
          invoiceBalanceId: newBalance.id,
          amount: totalAmount,
          description: `Shipping cost: $${shippingCost.toFixed(2)} + ${markupPercentage}% markup: $${markupAmount.toFixed(2)}`,
          createdAt: new Date(),
          metadata: JSON.stringify({
            shippingCost,
            markupAmount,
            markupPercentage,
            vendorId
          })
        }
      });

      return newBalance;
    }
  } catch (error) {
    console.error(`Error updating shipping costs balance: ${error.message}`);
    throw error;
  }
}

/**
 * Get the shipping costs balance for a vendor
 * @param {string} shop - The shop identifier
 * @param {string} vendorId - The vendor identifier
 * @returns {Promise<object>} - The shipping costs balance with recent transactions
 */
export async function getShippingCostsBalance(shop, vendorId) {
  try {
    // Get current date for month/year
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    const balance = await db.invoiceBalance.findFirst({
      where: {
        shop,
        category: 'shipping costs',
        month: currentMonth,
        year: currentYear,
        vendorId
      }
    });

    if (!balance) {
      return null;
    }

    // Get recent transactions for this balance
    const transactions = await db.invoiceTransaction.findMany({
      where: {
        invoiceBalanceId: balance.id
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    return {
      ...balance,
      transactions
    };
  } catch (error) {
    console.error(`Error getting shipping costs balance: ${error.message}`);
    return null;
  }
}
