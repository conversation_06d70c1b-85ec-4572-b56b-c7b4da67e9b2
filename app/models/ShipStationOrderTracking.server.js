/**
 * ShipStationOrderTracking.server.js
 * 
 * This file provides functions to track ShipStation order processing
 * to ensure no orders are skipped or repeated during reconciliation.
 */

import db from "../db.server";

/**
 * Get the last processed order number for a specific store
 * @param {string} storeId - The ShipStation store ID
 * @returns {Promise<string|null>} - The last processed order number or null if none exists
 */
export async function getLastProcessedOrderNumber(storeId) {
  try {
    const tracking = await db.shipStationOrderTracking.findUnique({
      where: { storeId }
    });
    
    return tracking ? tracking.lastOrderNumber : null;
  } catch (error) {
    console.error(`Error getting last processed order number: ${error.message}`);
    return null;
  }
}

/**
 * Update the last processed order number for a specific store
 * @param {string} storeId - The ShipStation store ID
 * @param {string} orderNumber - The order number to set as last processed
 * @param {string} shop - The shop identifier
 * @returns {Promise<object>} - The updated tracking record
 */
export async function updateLastProcessedOrderNumber(storeId, orderNumber, shop) {
  try {
    // Check if a record already exists
    const existing = await db.shipStationOrderTracking.findUnique({
      where: { storeId }
    });
    
    if (existing) {
      // Update existing record
      return await db.shipStationOrderTracking.update({
        where: { storeId },
        data: {
          lastOrderNumber: orderNumber,
          updatedAt: new Date()
        }
      });
    } else {
      // Create new record
      return await db.shipStationOrderTracking.create({
        data: {
          storeId,
          shop,
          lastOrderNumber: orderNumber,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }
  } catch (error) {
    console.error(`Error updating last processed order number: ${error.message}`);
    throw error;
  }
}

/**
 * Get all order tracking records
 * @returns {Promise<Array>} - Array of order tracking records
 */
export async function getAllOrderTrackingRecords() {
  try {
    return await db.shipStationOrderTracking.findMany({
      orderBy: { updatedAt: 'desc' }
    });
  } catch (error) {
    console.error(`Error getting all order tracking records: ${error.message}`);
    return [];
  }
}

/**
 * Get order tracking records for a specific shop
 * @param {string} shop - The shop identifier
 * @returns {Promise<Array>} - Array of order tracking records for the shop
 */
export async function getOrderTrackingRecordsByShop(shop) {
  try {
    return await db.shipStationOrderTracking.findMany({
      where: { shop },
      orderBy: { updatedAt: 'desc' }
    });
  } catch (error) {
    console.error(`Error getting order tracking records for shop ${shop}: ${error.message}`);
    return [];
  }
}
