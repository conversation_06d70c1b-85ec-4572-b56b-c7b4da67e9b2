import db from "../db.server"

/**
 * Get all shops
 * @returns {Promise<Array>} - Array of shops
 */
export async function getShops() {
  try {
    // Get all unique shops from the Session table
    const shops = await db.session.findMany({
      select: {
        shop: true
      },
      distinct: ['shop']
    });
    
    return shops;
  } catch (error) {
    console.error(`Error getting shops: ${error.message}`);
    return [];
  }
}

/**
 * Get a shop by name
 * @param {string} shop - The shop name
 * @returns {Promise<object>} - The shop
 */
export async function getShop(shop) {
  try {
    const shopData = await db.session.findFirst({
      where: { shop },
      select: {
        shop: true,
        accessToken: true
      }
    });
    
    return shopData;
  } catch (error) {
    console.error(`Error getting shop: ${error.message}`);
    return null;
  }
}
