/**
 * Unprocessable.server.js
 *
 * This file contains database operations for managing unprocessable items.
 * Unprocessable items are order items that couldn't be processed automatically
 * due to errors or missing data, and require manual intervention.
 */

import db from "../db.server"

/**
 * Retrieves an unprocessable item by its unique identifier
 * @param {string} id - The unique identifier of the unprocessable item
 * @returns {Promise<object|null>} - The unprocessable item or null if not found
 */
export async function getUnprocessable(id) {
    const unprocessable = await db.unprocessable.findFirst({
        where: { id }
    })

    if (!unprocessable) {
        return null;
    }

    return unprocessable;
}

/**
 * Retrieves all unprocessable items for a specific shop
 * @param {string} shop - The shop identifier
 * @returns {Promise<Array>} - An array of unprocessable items, or an empty array if none are found
 */
export async function getUnprocessables(shop) {
    const unprocessables = await db.unprocessable.findMany({
        where: { shop },
        orderBy: { id: "desc" }
    })

    if (unprocessables.length === 0) return [];

    return unprocessables;
}

/**
 * Creates a new unprocessable item record
 * @param {string} shop - The shop identifier
 * @param {object} data - The data for the unprocessable item
 * @param {string} data.productType - The product type
 * @param {string} data.variant - The product variant
 * @param {string} data.sku - The product SKU
 * @param {number} data.quantity - The quantity
 * @param {Date} data.createdAt - The creation date
 * @param {string} data.errorField - The field that caused the error
 * @param {string} data.message - The error message
 * @returns {Promise<object>} - The created unprocessable item
 */
export async function createUnprocessable(shop, data) {
    const unprocessable = await db.unprocessable.create({
        data: {
            shop: shop,
            productType: data.productType,
            variant: data.variant,
            sku: data.sku,
            quantity: data.quantity,
            date: data.createdAt,
            errorField: data.errorField,
            message: data.message,
        }
    })

    return unprocessable;
}

/**
 * Deletes an unprocessable item by its unique identifier
 * @param {string} id - The unique identifier of the unprocessable item
 * @returns {Promise<object>} - The deleted unprocessable item
 */
export async function deleteUnprocessable(id) {
    const result = await db.unprocessable.delete({
        where: { id }
    })

    return result;
}
