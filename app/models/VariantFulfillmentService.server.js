import db from "../db.server";

/**
 * Creates a new variant fulfillment service record
 *
 * @param {string} variantId - The variant ID
 * @param {string} fulfillmentService - The fulfillment service type
 * @returns {Promise<object>} - The created record
 */
export async function createVariantFulfillmentService(variantId, fulfillmentService) {
  return db.variantFulfillmentService.create({
    data: {
      variantId,
      fulfillmentService,
      lastUpdated: new Date()
    }
  });
}

/**
 * Gets a variant fulfillment service record by variant ID
 *
 * @param {string} variantId - The variant ID
 * @returns {Promise<object|null>} - The variant fulfillment service record or null if not found
 */
export async function getVariantFulfillmentService(variantId) {
  return db.variantFulfillmentService.findUnique({
    where: {
      variantId
    }
  });
}

/**
 * Updates a variant fulfillment service record
 *
 * @param {string} variantId - The variant ID
 * @param {string} fulfillmentService - The fulfillment service type
 * @returns {Promise<object>} - The updated record
 */
export async function updateVariantFulfillmentService(variantId, fulfillmentService) {
  return db.variantFulfillmentService.update({
    where: {
      variantId
    },
    data: {
      fulfillmentService,
      lastUpdated: new Date()
    }
  });
}

/**
 * Creates or updates a variant fulfillment service record
 *
 * @param {string} variantId - The variant ID
 * @param {string} fulfillmentService - The fulfillment service type
 * @returns {Promise<object>} - The created or updated record
 */
export async function upsertVariantFulfillmentService(variantId, fulfillmentService) {
  return db.variantFulfillmentService.upsert({
    where: {
      variantId
    },
    update: {
      fulfillmentService,
      lastUpdated: new Date()
    },
    create: {
      variantId,
      fulfillmentService,
      lastUpdated: new Date()
    }
  });
}

/**
 * Gets all variant fulfillment service records that need to be refreshed
 *
 * @param {number} maxAgeHours - The maximum age in hours before a record needs refreshing
 * @returns {Promise<Array<object>>} - The records that need refreshing
 */
export async function getStaleVariantFulfillmentServices(maxAgeHours = 24) {
  const cutoffDate = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);

  return db.variantFulfillmentService.findMany({
    where: {
      lastUpdated: {
        lt: cutoffDate
      }
    }
  });
}

/**
 * Deletes a variant fulfillment service record
 *
 * @param {string} variantId - The variant ID
 * @returns {Promise<object>} - The deleted record
 */
export async function deleteVariantFulfillmentService(variantId) {
  return db.variantFulfillmentService.delete({
    where: {
      variantId
    }
  });
}

/**
 * Gets multiple variant fulfillment service records by variant IDs
 * This is more efficient than making multiple individual queries
 *
 * @param {Array<string>} variantIds - Array of variant IDs to retrieve
 * @returns {Promise<Array<object>>} - Array of variant fulfillment service records
 */
export async function getBulkVariantFulfillmentServices(variantIds) {
  if (!variantIds || variantIds.length === 0) {
    return [];
  }

  return db.variantFulfillmentService.findMany({
    where: {
      variantId: {
        in: variantIds
      }
    }
  });
}

/**
 * Creates or updates multiple variant fulfillment service records in a single transaction
 * This is more efficient than making multiple individual upsert calls
 *
 * @param {Array<{variantId: string, fulfillmentService: string}>} records - Array of records to upsert
 * @returns {Promise<number>} - Number of records upserted
 */
export async function bulkUpsertVariantFulfillmentServices(records) {
  if (!records || records.length === 0) {
    return 0;
  }

  // Use a transaction to ensure all operations succeed or fail together
  const now = new Date();

  const result = await db.$transaction(
    records.map(record =>
      db.variantFulfillmentService.upsert({
        where: {
          variantId: record.variantId
        },
        update: {
          fulfillmentService: record.fulfillmentService,
          lastUpdated: now
        },
        create: {
          variantId: record.variantId,
          fulfillmentService: record.fulfillmentService,
          lastUpdated: now
        }
      })
    )
  );

  return result.length;
}
