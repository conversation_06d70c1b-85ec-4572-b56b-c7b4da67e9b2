import { container } from "../lib/container/ServiceContainer.server.js";
import { getShops } from "../models/Shop.server";
import { Shopify } from "@shopify/shopify-api";

/**
 * Refreshes the fulfillment service cache for all shops
 * This job should be scheduled to run periodically (e.g., daily)
 *
 * @returns {Promise<void>}
 */
export async function refreshAllShopsFulfillmentServiceCache() {
  try {
    console.log("Starting fulfillment service cache refresh job");

    // Get all shops
    const shops = await getShops();

    console.log(`Found ${shops.length} shops to process`);

    // Process each shop
    for (const shop of shops) {
      try {
        console.log(`Processing shop: ${shop.shop}`);

        // Create admin API client
        const admin = new Shopify.Clients.Graphql(
          shop.shop,
          shop.accessToken
        );

        // Refresh the cache for this shop
        // Get fulfillment service from container
        const fulfillmentService = await container.resolve('fulfillmentService');

        // Clean up old cache entries (older than 30 days)
        const refreshedCount = await fulfillmentService.cleanupOldFulfillmentServices(30);

        console.log(`Refreshed ${refreshedCount} variants for shop ${shop.shop}`);
      } catch (shopError) {
        console.error(`Error processing shop ${shop.shop}: ${shopError.message}`);
        // Continue with next shop
      }
    }

    console.log("Completed fulfillment service cache refresh job");
  } catch (error) {
    console.error(`Error in fulfillment service cache refresh job: ${error.message}`);
    throw error;
  }
}
