/**
 * Server-only ServiceContainer wrapper
 * This file ensures the container is only used on the server side
 * and includes all necessary service registrations for production
 */

import { ServiceContainer, ServiceRegistrar } from './ServiceContainer.js';

// Lazy container instance
let _container = null;

/**
 * Get or create the server container with all services registered
 * @returns {ServiceContainer} - Configured server container
 */
function getServerContainer() {
  if (!_container) {
    // Create container instance
    _container = new ServiceContainer();

    // Register database service (required for production)
    _container.singleton('database', async () => {
      const prismaModule = await import('../../db.server.js');
      return prismaModule.default;
    });

    // Register all other services using the registrar
    const registrar = new ServiceRegistrar(_container);
    registrar.registerAll();

    // Register database-dependent services that are server-only
    _container.singleton('setupService', async (container) => {
      const { SetupService } = await import('../../services/setup/SetupService.js');
      const prisma = await container.resolve('database');
      const pricingService = await container.resolve('pricingService');
      const orderProcessingService = await container.resolve('orderProcessingService');
      return new SetupService({
        prisma,
        pricingService,
        orderProcessingService
      });
    });

    _container.singleton('orderProcessingService', async (container) => {
      const { OrderProcessingService } = await import('../../services/order/OrderProcessingService.js');
      const prisma = await container.resolve('database');
      const shopifyClient = await container.resolve('shopifyClient');
      return new OrderProcessingService({
        prisma,
        shopifyClient
      });
    });

    _container.singleton('pricingService', async (container) => {
      const { PricingService } = await import('../../services/pricing/PricingService.js');
      const prisma = await container.resolve('database');
      return new PricingService({
        prisma
      });
    });

    _container.singleton('invoiceBalanceService', async (container) => {
      const { InvoiceBalanceService } = await import('../../services/invoice/InvoiceBalanceService.js');
      const prisma = await container.resolve('database');
      return new InvoiceBalanceService({
        prisma
      });
    });

    _container.singleton('transactionProcessingService', async (container) => {
      const { TransactionProcessingService } = await import('../../services/order/TransactionProcessingService.js');
      const prisma = await container.resolve('database');
      const orderProcessingService = await container.resolve('orderProcessingService');
      const pricingService = await container.resolve('pricingService');
      return new TransactionProcessingService({
        prisma,
        orderProcessingService,
        pricingService
      });
    });

    // Register ShipStation services
    _container.singleton('shipstationApiService', async (container) => {
      const { ShipStationApiService } = await import('../../services/shipping/ShipStationApiService.js');
      const prisma = await container.resolve('database');
      return new ShipStationApiService({
        prisma
      });
    });

    _container.singleton('shipstationOrderService', async (container) => {
      const { ShipStationOrderService } = await import('../../services/shipping/ShipStationOrderService.js');
      const shipstationApiService = await container.resolve('shipstationApiService');
      return new ShipStationOrderService({
        shipstationApiService
      });
    });

    _container.singleton('fulfillmentService', async (container) => {
      const { FulfillmentService } = await import('../../services/fulfillment/FulfillmentService.js');
      const prisma = await container.resolve('database');
      return new FulfillmentService({
        prisma
      });
    });
  }

  return _container;
}

// Export lazy container
export const container = {
  resolve: (serviceName) => getServerContainer().resolve(serviceName),
  has: (serviceName) => getServerContainer().has(serviceName),
  register: (name, factory, options) => getServerContainer().register(name, factory, options),
  singleton: (name, factory) => getServerContainer().singleton(name, factory),
  instance: (name, instance) => getServerContainer().instance(name, instance),
};
