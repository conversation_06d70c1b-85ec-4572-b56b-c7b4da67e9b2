/**
 * Server-only ServiceContainer
 * This file contains server-side dependencies and services
 */

import { ServiceContainer } from './ServiceContainer.js';

// Create server container
const container = new ServiceContainer();

// Register server-specific services
container.singleton('database', async () => {
  const prismaModule = await import('../../db.server.js');
  return prismaModule.default;
});

// Export the server container
export { container };
