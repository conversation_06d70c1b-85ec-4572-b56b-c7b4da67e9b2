/**
 * Dependency Injection Container
 *
 * This module provides a simple dependency injection container for managing
 * service instances and their dependencies throughout the application.
 */

import { ConfigurationError } from '../errors/AppError.js';

/**
 * Service container for dependency injection
 */
export class ServiceContainer {
  constructor() {
    this.services = new Map();
    this.singletons = new Map();
    this.factories = new Map();
  }

  /**
   * Register a service factory
   * @param {string} name - Service name
   * @param {Function} factory - Factory function that creates the service
   * @param {object} options - Registration options
   * @param {boolean} options.singleton - Whether to create a singleton instance
   */
  register(name, factory, options = {}) {
    if (typeof factory !== 'function') {
      throw new ConfigurationError(`Service factory for '${name}' must be a function`);
    }

    this.factories.set(name, {
      factory,
      singleton: options.singleton || false,
    });

    return this;
  }

  /**
   * Register a singleton service
   * @param {string} name - Service name
   * @param {Function} factory - Factory function that creates the service
   */
  singleton(name, factory) {
    return this.register(name, factory, { singleton: true });
  }

  /**
   * Register a service instance directly
   * @param {string} name - Service name
   * @param {*} instance - Service instance
   */
  instance(name, instance) {
    this.singletons.set(name, instance);
    return this;
  }

  /**
   * Resolve a service by name
   * @param {string} name - Service name
   * @returns {Promise<*>} - Service instance
   */
  async resolve(name) {
    // Check if we have a singleton instance
    if (this.singletons.has(name)) {
      return this.singletons.get(name);
    }

    // Check if we have a factory
    if (!this.factories.has(name)) {
      throw new ConfigurationError(`Service '${name}' is not registered`);
    }

    const { factory, singleton } = this.factories.get(name);

    // Create the service instance
    const instance = await factory(this);

    // Store singleton instances
    if (singleton) {
      this.singletons.set(name, instance);
    }

    return instance;
  }

  /**
   * Check if a service is registered
   * @param {string} name - Service name
   * @returns {boolean} - True if service is registered
   */
  has(name) {
    return this.factories.has(name) || this.singletons.has(name);
  }

  /**
   * Get all registered service names
   * @returns {string[]} - Array of service names
   */
  getServiceNames() {
    const factoryNames = Array.from(this.factories.keys());
    const singletonNames = Array.from(this.singletons.keys());
    return [...new Set([...factoryNames, ...singletonNames])];
  }

  /**
   * Clear all services (useful for testing)
   */
  clear() {
    this.services.clear();
    this.singletons.clear();
    this.factories.clear();
  }

  /**
   * Create a child container that inherits from this container
   * @returns {ServiceContainer} - Child container
   */
  createChild() {
    const child = new ServiceContainer();

    // Copy factories to child
    for (const [name, config] of this.factories) {
      child.factories.set(name, config);
    }

    // Copy singletons to child
    for (const [name, instance] of this.singletons) {
      child.singletons.set(name, instance);
    }

    return child;
  }
}

/**
 * Service registration helper
 */
export class ServiceRegistrar {
  constructor(container) {
    this.container = container;
  }

  /**
   * Register core application services
   */
  registerCoreServices() {
    // Configuration service
    this.container.singleton('config', async () => {
      const { config } = await import('../config/index.js');
      return config;
    });


    return this;
  }

  /**
   * Register API client services
   */
  registerApiServices() {
    // Base API client
    this.container.register('apiClient', async (container) => {
      const { ApiClient } = await import('../api/base/ApiClient.js');
      return new ApiClient();
    });

    // Shopify API client
    this.container.singleton('shopifyClient', async (container) => {
      const { ShopifyClient } = await import('../api/shopify/ShopifyClient.js');
      const config = await container.resolve('config');
      return new ShopifyClient(config);
    });

    // ShipStation API client
    this.container.singleton('shipstationClient', async (container) => {
      const { ShipStationClient } = await import('../api/shipstation/ShipStationClient.js');
      const config = await container.resolve('config');
      return new ShipStationClient(config);
    });

    return this;
  }

  /**
   * Register business service layer
   */
  registerBusinessServices() {
    // Cache service (only non-database dependent service)
    this.container.register('cacheService', async (container) => {
      const { CacheService } = await import('../../services/cache/CacheService.js');
      return new CacheService();
    });

    return this;
  }

  /**
   * Register all services
   */
  registerAll() {
    return this
      .registerCoreServices()
      .registerApiServices()
      .registerBusinessServices();
  }
}

// Create and export the global container
export const container = new ServiceContainer();

// Register services
const registrar = new ServiceRegistrar(container);
registrar.registerAll(); // Register all services

// Export registrar instance for additional service registration
export { registrar };
