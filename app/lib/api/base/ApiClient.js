/**
 * Base API Client with Retry Logic and Rate Limiting
 * 
 * This module provides a base API client with built-in retry logic,
 * rate limiting, and error handling for external API integrations.
 */

import axios from 'axios';
import https from 'https';
import { ExternalApiError, RateLimitError } from '../../errors/AppError.js';

/**
 * Base API client with retry and rate limiting capabilities
 */
export class ApiClient {
  constructor(baseURL = '', defaultHeaders = {}, options = {}) {
    this.baseURL = baseURL;
    this.defaultHeaders = defaultHeaders;
    this.options = {
      timeout: 30000,
      maxRetries: 3,
      initialBackoff: 1000,
      maxBackoff: 10000,
      backoffMultiplier: 2,
      retryableStatusCodes: [429, 502, 503, 504],
      ...options,
    };

    this.client = this.createAxiosInstance();
    this.setupInterceptors();
  }

  /**
   * Create axios instance with default configuration
   * @returns {object} - Configured axios instance
   */
  createAxiosInstance() {
    const httpsAgent = new https.Agent({
      rejectUnauthorized: true,
      minVersion: 'TLSv1.2',
      keepAlive: true,
      timeout: this.options.timeout,
    });

    return axios.create({
      baseURL: this.baseURL,
      timeout: this.options.timeout,
      headers: this.defaultHeaders,
      httpsAgent,
      // Prevent axios from throwing on 4xx/5xx status codes
      validateStatus: () => true,
    });
  }

  /**
   * Setup request and response interceptors
   */
  setupInterceptors() {
    // Request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        // Remove sensitive headers from logs
        const safeHeaders = { ...config.headers };
        this.sanitizeHeaders(safeHeaders);

        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        console.log(`Headers: ${JSON.stringify(safeHeaders)}`);
        
        return config;
      },
      (error) => {
        console.error('Request interceptor error:', error.message);
        return Promise.reject(error);
      }
    );

    // Response interceptor for logging and error handling
    this.client.interceptors.response.use(
      (response) => {
        console.log(`API Response: ${response.status} ${response.statusText}`);
        return response;
      },
      (error) => {
        console.error('Response interceptor error:', error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Sanitize headers for logging (remove sensitive information)
   * @param {object} headers - Headers object to sanitize
   */
  sanitizeHeaders(headers) {
    const sensitiveKeys = ['authorization', 'api-key', 'x-api-key', 'token'];
    
    for (const key of Object.keys(headers)) {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        const value = headers[key];
        if (typeof value === 'string' && value.length > 6) {
          headers[key] = `${value.substring(0, 3)}...${value.substring(value.length - 3)}`;
        } else {
          headers[key] = '[REDACTED]';
        }
      }
    }
  }

  /**
   * Calculate backoff delay for retries
   * @param {number} attempt - Current attempt number (0-based)
   * @returns {number} - Delay in milliseconds
   */
  calculateBackoff(attempt) {
    const delay = this.options.initialBackoff * Math.pow(this.options.backoffMultiplier, attempt);
    return Math.min(delay, this.options.maxBackoff);
  }

  /**
   * Check if an error is retryable
   * @param {object} error - Axios error object
   * @returns {boolean} - True if error is retryable
   */
  isRetryableError(error) {
    if (!error.response) {
      // Network errors are retryable
      return true;
    }

    const status = error.response.status;
    return this.options.retryableStatusCodes.includes(status);
  }

  /**
   * Handle rate limit response
   * @param {object} error - Axios error object
   * @returns {number} - Retry delay in milliseconds
   */
  handleRateLimit(error) {
    const response = error.response;
    let retryAfter = 60; // Default to 60 seconds

    // Check for Retry-After header
    if (response.headers['retry-after']) {
      retryAfter = parseInt(response.headers['retry-after'], 10);
    }

    // Check for X-Rate-Limit-Reset header (ShipStation style)
    if (response.headers['x-rate-limit-reset']) {
      const resetTime = parseInt(response.headers['x-rate-limit-reset'], 10);
      const currentTime = Math.floor(Date.now() / 1000);
      retryAfter = Math.max(resetTime - currentTime, 1);
    }

    return retryAfter * 1000; // Convert to milliseconds
  }

  /**
   * Sleep for specified duration
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise} - Promise that resolves after delay
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Make HTTP request with retry logic
   * @param {object} config - Axios request configuration
   * @returns {Promise<object>} - Response data
   */
  async request(config) {
    let lastError;

    for (let attempt = 0; attempt <= this.options.maxRetries; attempt++) {
      try {
        const response = await this.client.request(config);

        // Check for successful response
        if (response.status >= 200 && response.status < 300) {
          return response;
        }

        // Handle rate limiting
        if (response.status === 429) {
          const retryDelay = this.handleRateLimit({ response });
          
          if (attempt < this.options.maxRetries) {
            console.log(`Rate limited. Retrying after ${retryDelay}ms (attempt ${attempt + 1}/${this.options.maxRetries})`);
            await this.sleep(retryDelay);
            continue;
          } else {
            throw new RateLimitError('Rate limit exceeded', retryDelay / 1000);
          }
        }

        // Handle other HTTP errors
        const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
        error.response = response;
        lastError = error;

        if (!this.isRetryableError(error) || attempt === this.options.maxRetries) {
          break;
        }

      } catch (error) {
        lastError = error;

        // Don't retry on non-retryable errors
        if (!this.isRetryableError(error) || attempt === this.options.maxRetries) {
          break;
        }
      }

      // Calculate backoff delay
      const backoffDelay = this.calculateBackoff(attempt);
      console.log(`Request failed. Retrying after ${backoffDelay}ms (attempt ${attempt + 1}/${this.options.maxRetries})`);
      await this.sleep(backoffDelay);
    }

    // All retries exhausted, throw the last error
    if (lastError.response) {
      throw new ExternalApiError(
        'API',
        `Request failed after ${this.options.maxRetries} retries: ${lastError.message}`,
        lastError.response.status,
        {
          url: config.url,
          method: config.method,
          responseData: lastError.response.data,
        }
      );
    } else {
      throw new ExternalApiError(
        'API',
        `Network error after ${this.options.maxRetries} retries: ${lastError.message}`,
        502,
        {
          url: config.url,
          method: config.method,
        }
      );
    }
  }

  /**
   * GET request
   * @param {string} url - Request URL
   * @param {object} config - Additional request configuration
   * @returns {Promise<object>} - Response data
   */
  async get(url, config = {}) {
    return this.request({ ...config, method: 'GET', url });
  }

  /**
   * POST request
   * @param {string} url - Request URL
   * @param {*} data - Request data
   * @param {object} config - Additional request configuration
   * @returns {Promise<object>} - Response data
   */
  async post(url, data = null, config = {}) {
    return this.request({ ...config, method: 'POST', url, data });
  }

  /**
   * PUT request
   * @param {string} url - Request URL
   * @param {*} data - Request data
   * @param {object} config - Additional request configuration
   * @returns {Promise<object>} - Response data
   */
  async put(url, data = null, config = {}) {
    return this.request({ ...config, method: 'PUT', url, data });
  }

  /**
   * DELETE request
   * @param {string} url - Request URL
   * @param {object} config - Additional request configuration
   * @returns {Promise<object>} - Response data
   */
  async delete(url, config = {}) {
    return this.request({ ...config, method: 'DELETE', url });
  }
}
