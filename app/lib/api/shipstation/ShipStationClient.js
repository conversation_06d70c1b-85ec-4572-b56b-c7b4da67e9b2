/**
 * ShipStation API Client
 * 
 * This module provides a unified ShipStation API client that supports both
 * V1 and V2 APIs with proper authentication and error handling.
 */

import { ApiClient } from '../base/ApiClient.js';
import { ShipStationApiError } from '../../errors/AppError.js';

/**
 * ShipStation API client with V1 and V2 support
 */
export class ShipStationClient extends ApiClient {
  constructor(config) {
    super('', {
      'Content-Type': 'application/json',
      'User-Agent': 'Americans United Inc App/1.0',
    });

    this.config = config;
    this.v1BaseURL = 'https://ssapi.shipstation.com';
    this.v2BaseURL = 'https://api.shipstation.com/v2';
  }

  /**
   * Get V1 API authentication headers
   * @returns {object} - V1 authentication headers
   */
  getV1AuthHeaders() {
    const { v1Key, v1Secret } = this.config.shipstation;
    const basicAuth = Buffer.from(`${v1Key}:${v1Secret}`).toString('base64');
    
    return {
      'Authorization': `Basic ${basicAuth}`,
    };
  }

  /**
   * Get V2 API authentication headers
   * @returns {object} - V2 authentication headers
   */
  getV2AuthHeaders() {
    return {
      'API-Key': this.config.shipstation.v2ApiKey,
    };
  }

  /**
   * Make V1 API request
   * @param {object} config - Request configuration
   * @returns {Promise<object>} - Response data
   */
  async v1Request(config) {
    const requestConfig = {
      ...config,
      url: `${this.v1BaseURL}${config.url}`,
      headers: {
        ...this.defaultHeaders,
        ...this.getV1AuthHeaders(),
        ...config.headers,
      },
    };

    try {
      const response = await this.request(requestConfig);
      
      if (response.status >= 400) {
        this.handleShipStationError(response, 'V1');
      }

      return response;
    } catch (error) {
      if (error instanceof ShipStationApiError) {
        throw error;
      }
      
      throw new ShipStationApiError(
        error.message,
        error.response?.status || 500,
        {
          apiVersion: 'V1',
          url: config.url,
          method: config.method,
        }
      );
    }
  }

  /**
   * Make V2 API request
   * @param {object} config - Request configuration
   * @returns {Promise<object>} - Response data
   */
  async v2Request(config) {
    const requestConfig = {
      ...config,
      url: `${this.v2BaseURL}${config.url}`,
      headers: {
        ...this.defaultHeaders,
        ...this.getV2AuthHeaders(),
        ...config.headers,
      },
    };

    try {
      const response = await this.request(requestConfig);
      
      if (response.status >= 400) {
        this.handleShipStationError(response, 'V2');
      }

      return response;
    } catch (error) {
      if (error instanceof ShipStationApiError) {
        throw error;
      }
      
      throw new ShipStationApiError(
        error.message,
        error.response?.status || 500,
        {
          apiVersion: 'V2',
          url: config.url,
          method: config.method,
        }
      );
    }
  }

  /**
   * Handle ShipStation-specific error responses
   * @param {object} response - Axios response object
   * @param {string} apiVersion - API version (V1 or V2)
   */
  handleShipStationError(response, apiVersion) {
    const { status, data } = response;
    let message = `ShipStation ${apiVersion} API error`;

    if (data) {
      if (data.message) {
        message = data.message;
      } else if (data.error) {
        message = data.error;
      } else if (data.errors && Array.isArray(data.errors)) {
        message = data.errors.join(', ');
      }
    }

    throw new ShipStationApiError(message, status, {
      apiVersion,
      responseData: data,
    });
  }

  /**
   * Get stores from V1 API
   * @returns {Promise<object>} - Stores response
   */
  async getStores() {
    const response = await this.v1Request({
      method: 'GET',
      url: '/stores',
    });

    return response.data;
  }

  /**
   * Get shipments from V2 API
   * @param {object} params - Query parameters
   * @returns {Promise<object>} - Shipments response
   */
  async getShipments(params = {}) {
    const response = await this.v2Request({
      method: 'GET',
      url: '/shipments',
      params,
    });

    return response.data;
  }

  /**
   * Get labels from V2 API
   * @param {object} params - Query parameters
   * @returns {Promise<object>} - Labels response
   */
  async getLabels(params = {}) {
    const response = await this.v2Request({
      method: 'GET',
      url: '/labels',
      params,
    });

    return response.data;
  }

  /**
   * Get labels for a specific shipment from V1 API
   * @param {string|number} shipmentId - Shipment ID
   * @returns {Promise<object>} - Labels response
   */
  async getShipmentLabels(shipmentId) {
    const response = await this.v1Request({
      method: 'GET',
      url: `/shipments/${shipmentId}/labels`,
    });

    return response.data;
  }

  /**
   * Get orders from V1 API
   * @param {object} params - Query parameters
   * @returns {Promise<object>} - Orders response
   */
  async getOrders(params = {}) {
    const response = await this.v1Request({
      method: 'GET',
      url: '/orders',
      params,
    });

    return response.data;
  }

  /**
   * Fetch all labels with pagination for a date range
   * @param {object} params - Query parameters
   * @param {Function} progressCallback - Progress callback function
   * @returns {Promise<object[]>} - All labels
   */
  async getAllLabels(params = {}, progressCallback = null) {
    const allLabels = [];
    let page = 1;
    let hasMorePages = true;
    const pageSize = params.page_size || 100;

    while (hasMorePages) {
      const pageParams = {
        ...params,
        page,
        page_size: pageSize,
      };

      const response = await this.getLabels(pageParams);
      const labels = response.labels || [];
      
      allLabels.push(...labels);

      // Update progress if callback provided
      if (progressCallback) {
        progressCallback({
          page,
          totalPages: response.total_pages || 1,
          currentCount: allLabels.length,
          totalCount: response.total || allLabels.length,
        });
      }

      // Check if there are more pages
      hasMorePages = labels.length === pageSize && page < (response.total_pages || 1);
      page++;
    }

    return allLabels;
  }

  /**
   * Fetch all shipments with pagination for a date range
   * @param {object} params - Query parameters
   * @param {Function} progressCallback - Progress callback function
   * @returns {Promise<object[]>} - All shipments
   */
  async getAllShipments(params = {}, progressCallback = null) {
    const allShipments = [];
    let page = 1;
    let hasMorePages = true;
    const pageSize = params.page_size || 100;

    while (hasMorePages) {
      const pageParams = {
        ...params,
        page,
        page_size: pageSize,
      };

      const response = await this.getShipments(pageParams);
      const shipments = response.shipments || [];
      
      allShipments.push(...shipments);

      // Update progress if callback provided
      if (progressCallback) {
        progressCallback({
          page,
          totalPages: response.total_pages || 1,
          currentCount: allShipments.length,
          totalCount: response.total || allShipments.length,
        });
      }

      // Check if there are more pages
      hasMorePages = shipments.length === pageSize && page < (response.total_pages || 1);
      page++;
    }

    return allShipments;
  }

  /**
   * Batch process labels with rate limiting
   * @param {object} params - Base query parameters
   * @param {Function} processor - Function to process each batch
   * @param {object} options - Processing options
   * @returns {Promise<object>} - Processing results
   */
  async batchProcessLabels(params, processor, options = {}) {
    const { batchSize = 256, maxConcurrency = 3 } = options;
    const results = [];
    const errors = [];

    try {
      const allLabels = await this.getAllLabels(params, options.progressCallback);
      
      // Process labels in batches
      for (let i = 0; i < allLabels.length; i += batchSize) {
        const batch = allLabels.slice(i, i + batchSize);
        
        try {
          const batchResult = await processor(batch);
          results.push(batchResult);
        } catch (error) {
          errors.push({
            batchIndex: Math.floor(i / batchSize),
            error: error.message,
            labelCount: batch.length,
          });
        }
      }

      return {
        success: true,
        totalLabels: allLabels.length,
        batchesProcessed: results.length,
        errors,
      };
    } catch (error) {
      throw new ShipStationApiError(
        `Batch processing failed: ${error.message}`,
        500,
        { originalError: error.message }
      );
    }
  }
}
