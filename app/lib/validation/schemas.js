/**
 * Input Validation Schemas
 * 
 * This module provides Zod validation schemas for API endpoints,
 * request parameters, and data structures throughout the application.
 */

import { z } from 'zod';

// Common validation patterns
const shopDomainSchema = z.string()
  .regex(/^[a-zA-Z0-9-]+\.myshopify\.com$/, 'Invalid shop domain format');

const dateStringSchema = z.string()
  .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format');

const isoDateStringSchema = z.string()
  .datetime('Invalid ISO date format');

const positiveNumberSchema = z.number().positive('Must be a positive number');

const nonEmptyStringSchema = z.string().min(1, 'Cannot be empty');

// Shop validation schemas
export const shopValidation = {
  domain: shopDomainSchema,
  
  createShop: z.object({
    shop: shopDomainSchema,
    accessToken: nonEmptyStringSchema,
  }),
  
  updateShop: z.object({
    shop: shopDomainSchema,
    accessToken: nonEmptyStringSchema.optional(),
    isActive: z.boolean().optional(),
  }),
};

// Order validation schemas
export const orderValidation = {
  orderId: z.union([z.string(), z.number()]).transform(String),
  
  orderQuery: z.object({
    shop: shopDomainSchema.optional(),
    status: z.enum(['open', 'closed', 'cancelled', 'any']).optional(),
    created_at_min: isoDateStringSchema.optional(),
    created_at_max: isoDateStringSchema.optional(),
    updated_at_min: isoDateStringSchema.optional(),
    updated_at_max: isoDateStringSchema.optional(),
    limit: z.number().min(1).max(250).optional(),
    page: z.number().min(1).optional(),
  }),
  
  processOrder: z.object({
    orderId: z.union([z.string(), z.number()]).transform(String),
    shop: shopDomainSchema,
    forceReprocess: z.boolean().optional().default(false),
  }),
};

// ShipStation validation schemas
export const shipstationValidation = {
  storeId: z.union([z.string(), z.number()]).transform(String),
  
  shipmentQuery: z.object({
    store_id: z.union([z.string(), z.number()]).transform(String).optional(),
    created_at_start: isoDateStringSchema,
    created_at_end: isoDateStringSchema,
    page_size: z.number().min(1).max(500).optional().default(100),
    page: z.number().min(1).optional().default(1),
  }),
  
  labelQuery: z.object({
    store_id: z.union([z.string(), z.number()]).transform(String).optional(),
    created_at_start: isoDateStringSchema,
    created_at_end: isoDateStringSchema,
    page_size: z.number().min(1).max(500).optional().default(100),
    page: z.number().min(1).optional().default(1),
  }),
  
  storeMapping: z.object({
    shipstationStoreId: z.union([z.string(), z.number()]).transform(String),
    shopDomain: shopDomainSchema,
    storeName: nonEmptyStringSchema.optional(),
    isActive: z.boolean().optional().default(true),
  }),
  
  webhook: z.object({
    resource_url: z.string().url().optional(),
    resource_type: z.string().optional(),
    resource_data: z.any().optional(),
  }),
};

// Pricing validation schemas
export const pricingValidation = {
  priceUpdate: z.object({
    shop: shopDomainSchema,
    category: nonEmptyStringSchema,
    prices: z.record(z.string(), positiveNumberSchema),
  }),
  
  bulkPriceUpdate: z.object({
    shops: z.array(shopDomainSchema).min(1),
    category: nonEmptyStringSchema,
    prices: z.record(z.string(), positiveNumberSchema),
  }),
};

// Shipping cost validation schemas
export const shippingValidation = {
  shippingCost: z.object({
    shop: shopDomainSchema,
    storeId: z.union([z.string(), z.number()]).transform(String),
    cost: positiveNumberSchema,
    markupPercentage: z.number().min(0).max(100).optional().default(10),
    date: z.date().optional(),
  }),
  
  shippingCostQuery: z.object({
    shop: shopDomainSchema.optional(),
    storeId: z.union([z.string(), z.number()]).transform(String).optional(),
    startDate: dateStringSchema.optional(),
    endDate: dateStringSchema.optional(),
    month: z.number().min(1).max(12).optional(),
    year: z.number().min(2020).max(2030).optional(),
  }),
};

// Reconciliation validation schemas
export const reconciliationValidation = {
  reconciliationJob: z.object({
    type: z.enum(['scheduled', 'missing_orders', 'shipstation_sync']),
    shop: shopDomainSchema.optional(),
    startDate: dateStringSchema.optional(),
    endDate: dateStringSchema.optional(),
    options: z.record(z.any()).optional(),
  }),
  
  reconciliationQuery: z.object({
    jobId: z.string().uuid().optional(),
    type: z.enum(['scheduled', 'missing_orders', 'shipstation_sync']).optional(),
    status: z.enum(['pending', 'running', 'completed', 'failed']).optional(),
    shop: shopDomainSchema.optional(),
    limit: z.number().min(1).max(100).optional().default(20),
    offset: z.number().min(0).optional().default(0),
  }),
};

// Authentication validation schemas
export const authValidation = {
  scheduledJobAuth: z.object({
    token: nonEmptyStringSchema,
  }),
  
  webhookAuth: z.object({
    signature: nonEmptyStringSchema.optional(),
    timestamp: z.number().optional(),
  }),
  
  shopifyAuth: z.object({
    shop: shopDomainSchema,
    code: nonEmptyStringSchema.optional(),
    state: nonEmptyStringSchema.optional(),
    timestamp: z.number().optional(),
    hmac: nonEmptyStringSchema.optional(),
  }),
};

// API response validation schemas
export const responseValidation = {
  success: z.object({
    success: z.literal(true),
    data: z.any().optional(),
    message: z.string().optional(),
  }),
  
  error: z.object({
    success: z.literal(false),
    error: z.string(),
    code: z.string().optional(),
    details: z.any().optional(),
  }),
  
  pagination: z.object({
    page: z.number().min(1),
    limit: z.number().min(1),
    total: z.number().min(0),
    totalPages: z.number().min(0),
    hasNext: z.boolean(),
    hasPrev: z.boolean(),
  }),
};

// Utility validation functions
export const validationUtils = {
  /**
   * Validate shop domain format
   * @param {string} shop - Shop domain to validate
   * @returns {boolean} - True if valid
   */
  isValidShopDomain(shop) {
    try {
      shopDomainSchema.parse(shop);
      return true;
    } catch {
      return false;
    }
  },

  /**
   * Validate date string format
   * @param {string} date - Date string to validate
   * @returns {boolean} - True if valid
   */
  isValidDateString(date) {
    try {
      dateStringSchema.parse(date);
      return true;
    } catch {
      return false;
    }
  },

  /**
   * Validate ISO date string format
   * @param {string} date - ISO date string to validate
   * @returns {boolean} - True if valid
   */
  isValidISODateString(date) {
    try {
      isoDateStringSchema.parse(date);
      return true;
    } catch {
      return false;
    }
  },

  /**
   * Sanitize and validate pagination parameters
   * @param {object} params - Raw pagination parameters
   * @returns {object} - Validated pagination parameters
   */
  validatePagination(params) {
    const schema = z.object({
      page: z.coerce.number().min(1).optional().default(1),
      limit: z.coerce.number().min(1).max(100).optional().default(20),
      offset: z.coerce.number().min(0).optional(),
    });

    const validated = schema.parse(params);
    
    // Calculate offset if not provided
    if (validated.offset === undefined) {
      validated.offset = (validated.page - 1) * validated.limit;
    }

    return validated;
  },

  /**
   * Validate and normalize date range
   * @param {object} params - Date range parameters
   * @returns {object} - Validated date range
   */
  validateDateRange(params) {
    const schema = z.object({
      startDate: dateStringSchema.optional(),
      endDate: dateStringSchema.optional(),
      month: z.coerce.number().min(1).max(12).optional(),
      year: z.coerce.number().min(2020).max(2030).optional(),
    });

    const validated = schema.parse(params);

    // If month/year provided, calculate date range
    if (validated.month && validated.year) {
      const startDate = new Date(validated.year, validated.month - 1, 1);
      const endDate = new Date(validated.year, validated.month, 0);
      
      validated.startDate = startDate.toISOString().split('T')[0];
      validated.endDate = endDate.toISOString().split('T')[0];
    }

    return validated;
  },
};
