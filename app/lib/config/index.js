/**
 * Centralized Configuration with Multistore Support
 *
 * This module provides a centralized configuration system with validation
 * and multistore support for the Americans United Inc application.
 */

import { z } from 'zod';

// Environment validation schema
const configSchema = z.object({
  // Application configuration
  app: z.object({
    environment: z.enum(['development', 'production', 'test']).default('development'),
    port: z.coerce.number().default(3000),
    expressPort: z.coerce.number().default(4000),
    adminShop: z.string().min(1, 'Admin shop is required'),
    appUrl: z.string().url('App URL must be a valid URL'),
  }),

  // Database configuration
  database: z.object({
    url: z.string().min(1, 'Database URL is required'),
    testUrl: z.string().optional(),
  }),

  // Shopify configuration
  shopify: z.object({
    apiVersion: z.string().default('2025-04'),
    scopes: z.array(z.string()).min(1, 'At least one scope is required'),
  }),

  // ShipStation configuration
  shipstation: z.object({
    v1Key: z.string().min(1, 'ShipStation V1 key is required'),
    v1Secret: z.string().min(1, 'ShipStation V1 secret is required'),
    v2ApiKey: z.string().min(1, 'ShipStation V2 API key is required'),
    webhookKey: z.string().optional(),
  }),

  // Security configuration
  security: z.object({
    scheduledJobToken: z.string().min(32, 'Scheduled job token must be at least 32 characters'),
    webhookVerificationSecret: z.string().min(32, 'Webhook verification secret must be at least 32 characters'),
  }),

  // Multistore configuration (always enabled)
  multistore: z.object({
    enabled: z.literal(true), // Always true - multistore is the only mode
    supportedStores: z.array(z.string()).min(1, 'At least one store must be supported'),
    storeConfigs: z.record(z.string(), z.object({
      apiKey: z.string().min(1),
      apiSecret: z.string().min(1),
    })).refine(
      (configs) => Object.keys(configs).length > 0,
      'At least one store configuration is required'
    ),
  }),
});

/**
 * Parse and normalize store domain for environment variable lookup
 * @param {string} storeDomain - The store domain (e.g., 'american-trigger-pullers.myshopify.com')
 * @returns {string} - Normalized name for environment variables
 */
function normalizeStoreName(storeDomain) {
  return storeDomain
    .replace('.myshopify.com', '') // Remove the .myshopify.com suffix
    .replace(/-/g, '_') // Replace hyphens with underscores
    .toUpperCase(); // Convert to uppercase
}

/**
 * Load multistore configurations from environment variables
 * @returns {object} - Store configurations object
 */
function loadMultistoreConfigs() {
  // Parse SUPPORTED_STORES with support for multi-line format
  const supportedStoresRaw = process.env.SUPPORTED_STORES || '';
  const supportedStores = supportedStoresRaw
    .split(/[,\n]/) // Split by comma OR newline
    .map(s => s.trim()) // Trim whitespace
    .filter(s => s.length > 0) // Remove empty strings
    .filter(s => !s.startsWith('#')); // Remove comment lines (optional feature)

  const storeConfigs = {};

  for (const store of supportedStores) {
    const normalizedName = normalizeStoreName(store);
    const apiKey = process.env[`SHOPIFY_API_KEY_${normalizedName}`];
    const apiSecret = process.env[`SHOPIFY_API_SECRET_${normalizedName}`];

    if (apiKey && apiSecret) {
      storeConfigs[store] = {
        apiKey,
        apiSecret,
      };
    }
  }

  return { supportedStores, storeConfigs };
}

/**
 * Load and validate configuration from environment variables
 * @returns {object} - Validated configuration object
 */
function loadConfig() {
  const multistoreConfig = loadMultistoreConfigs();

  const rawConfig = {
    app: {
      environment: process.env.NODE_ENV || 'development',
      port: process.env.PORT || 3000,
      expressPort: process.env.EXPRESS_PORT || 4000,
      adminShop: process.env.ADMIN_SHOP,
      appUrl: process.env.SHOPIFY_APP_URL,
    },
    database: {
      url: process.env.DATABASE_URL,
      testUrl: process.env.TEST_DATABASE_URL,
    },
    shopify: {
      apiVersion: process.env.SHOPIFY_API_VERSION || '2025-04',
      scopes: process.env.SCOPES?.split(',').map(s => s.trim()) || [],
    },
    shipstation: {
      v1Key: process.env.SHIPSTATION_V1_KEY,
      v1Secret: process.env.SHIPSTATION_V1_SECRET,
      v2ApiKey: process.env.SHIPSTATION_API_KEY,
      webhookKey: process.env.SHIPSTATION_WEBHOOK_KEY,
    },
    security: {
      scheduledJobToken: process.env.SCHEDULED_JOB_TOKEN,
      webhookVerificationSecret: process.env.WEBHOOK_VERIFICATION_SECRET,
    },
    multistore: {
      enabled: true, // Always true - multistore is the only mode
      supportedStores: multistoreConfig.supportedStores,
      storeConfigs: multistoreConfig.storeConfigs,
    },
  };

  try {
    return configSchema.parse(rawConfig);
  } catch (error) {
    console.error('Configuration validation failed:');
    if (error instanceof z.ZodError) {
      error.errors.forEach(err => {
        console.error(`  ${err.path.join('.')}: ${err.message}`);
      });
    }
    throw new Error('Invalid configuration. Please check your environment variables.');
  }
}

/**
 * Get store-specific configuration
 * @param {string} storeDomain - The store domain
 * @returns {object|null} - Store configuration or null if not found
 */
function getStoreConfig(storeDomain) {
  // Multistore is the only mode - always use store-specific configs
  return config.multistore.storeConfigs[storeDomain] || null;
}

/**
 * Check if a store is supported
 * @param {string} storeDomain - The store domain
 * @returns {boolean} - True if store is supported
 */
function isStoreSupported(storeDomain) {
  // Multistore is the only mode - check if store is in supported list
  return config.multistore.supportedStores.includes(storeDomain);
}

/**
 * Check if the store is the designated admin shop
 * @param {string} storeDomain - The store domain
 * @returns {boolean} - True if this is the admin shop
 */
function isAdminShop(storeDomain) {
  return storeDomain === config.app.adminShop;
}

// Load and export configuration
export const config = loadConfig();

// Export utility functions
export {
  getStoreConfig,
  isStoreSupported,
  isAdminShop,
  normalizeStoreName,
};

// Export configuration for debugging (development only)
if (config.app.environment === 'development') {
  console.log('Configuration loaded successfully');
  console.log(`Environment: ${config.app.environment}`);
  console.log(`Multistore mode: Always enabled`);
  console.log(`Supported stores: ${config.multistore.supportedStores.length}`);
}
