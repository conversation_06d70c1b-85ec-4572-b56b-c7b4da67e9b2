/**
 * Transaction Manager Tests
 *
 * Unit tests for the TransactionManager class
 */

import { jest } from '@jest/globals';
import { TransactionManager, BatchOperationManager } from './TransactionManager.js';
import { DatabaseError, BusinessLogicError } from '../errors/AppError.js';

// Mock the configuration module to prevent loading actual config
jest.mock('../config/index.js', () => ({
  config: {
    database: {
      connectionTimeout: 10000,
      queryTimeout: 30000,
    },
    multistore: {
      storeConfigs: {
        'test-store.myshopify.com': {
          apiKey: 'test-key',
          apiSecret: 'test-secret',
        },
      },
    },
  },
}));

// Mock the database module
jest.mock('../../db.server.js', () => ({
  default: {
    $transaction: jest.fn(),
    $executeRaw: jest.fn(),
  },
}));

// Mock Prisma client
const mockPrisma = {
  $transaction: jest.fn(),
  $executeRaw: jest.fn(),
};

describe('TransactionManager', () => {
  let transactionManager;

  beforeEach(() => {
    jest.clearAllMocks();
    transactionManager = new TransactionManager(mockPrisma);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('executeTransaction', () => {
    it('should execute operation within transaction successfully', async () => {
      // Setup
      const mockOperation = jest.fn().mockResolvedValue('success');
      mockPrisma.$transaction.mockImplementation(async (operation) => {
        return await operation(mockPrisma);
      });

      // Execute
      const result = await transactionManager.executeTransaction(mockOperation);

      // Assertions
      expect(result).toBe('success');
      expect(mockPrisma.$transaction).toHaveBeenCalledTimes(1);
      expect(mockOperation).toHaveBeenCalledWith(mockPrisma);
    });

    it('should handle transaction timeout', async () => {
      // Setup
      const slowOperation = jest.fn().mockImplementation(() =>
        new Promise(resolve => setTimeout(resolve, 2000))
      );
      mockPrisma.$transaction.mockImplementation(async (operation) => {
        return await operation(mockPrisma);
      });

      // Execute & Assert
      await expect(
        transactionManager.executeTransaction(slowOperation, { timeout: 1000 })
      ).rejects.toThrow(DatabaseError);
    });

    it('should retry on retryable errors', async () => {
      // Setup
      const retryableError = new Error('Connection lost');
      retryableError.code = 'P2024'; // Connection error

      const mockOperation = jest.fn().mockResolvedValue('success');

      mockPrisma.$transaction
        .mockRejectedValueOnce(retryableError)
        .mockRejectedValueOnce(retryableError)
        .mockImplementationOnce(async (operation) => {
          return await operation(mockPrisma);
        });

      // Execute
      const result = await transactionManager.executeTransaction(mockOperation, {
        maxRetries: 3,
        retryDelay: 10,
      });

      // Assertions
      expect(result).toBe('success');
      expect(mockPrisma.$transaction).toHaveBeenCalledTimes(3);
    });

    it('should not retry on non-retryable errors', async () => {
      // Setup
      const nonRetryableError = new BusinessLogicError('Validation failed');
      const mockOperation = jest.fn().mockRejectedValue(nonRetryableError);
      mockPrisma.$transaction.mockRejectedValue(nonRetryableError);

      // Execute & Assert
      await expect(
        transactionManager.executeTransaction(mockOperation, { maxRetries: 3 })
      ).rejects.toThrow(DatabaseError);

      expect(mockPrisma.$transaction).toHaveBeenCalledTimes(1);
    });

    it('should fail after max retries', async () => {
      // Setup
      const retryableError = new Error('Connection lost');
      const mockOperation = jest.fn().mockRejectedValue(retryableError);
      mockPrisma.$transaction.mockRejectedValue(retryableError);

      // Execute & Assert
      await expect(
        transactionManager.executeTransaction(mockOperation, {
          maxRetries: 2,
          retryDelay: 10,
        })
      ).rejects.toThrow(DatabaseError);

      expect(mockPrisma.$transaction).toHaveBeenCalledTimes(2);
    });
  });

  describe('executeBatch', () => {
    it('should execute multiple operations in single transaction', async () => {
      // Setup
      const operations = [
        jest.fn().mockResolvedValue('result1'),
        jest.fn().mockResolvedValue('result2'),
        jest.fn().mockResolvedValue('result3'),
      ];

      mockPrisma.$transaction.mockImplementation(async (operation) => {
        return await operation(mockPrisma);
      });

      // Execute
      const results = await transactionManager.executeBatch(operations);

      // Assertions
      expect(results).toEqual(['result1', 'result2', 'result3']);
      expect(mockPrisma.$transaction).toHaveBeenCalledTimes(1);
      operations.forEach(op => {
        expect(op).toHaveBeenCalledWith(mockPrisma);
      });
    });

    it('should rollback all operations if one fails', async () => {
      // Setup
      const operations = [
        jest.fn().mockResolvedValue('result1'),
        jest.fn().mockRejectedValue(new Error('Operation failed')),
        jest.fn().mockResolvedValue('result3'),
      ];

      mockPrisma.$transaction.mockImplementation(async (operation) => {
        return await operation(mockPrisma);
      });

      // Execute & Assert
      await expect(
        transactionManager.executeBatch(operations)
      ).rejects.toThrow(DatabaseError);

      expect(operations[0]).toHaveBeenCalled();
      expect(operations[1]).toHaveBeenCalled();
      expect(operations[2]).not.toHaveBeenCalled(); // Should not reach third operation
    });
  });

  describe('executeWithRollback', () => {
    it('should execute operations and rollback on failure', async () => {
      // Setup
      const operations = [
        jest.fn().mockResolvedValue('result1'),
        jest.fn().mockRejectedValue(new Error('Operation failed')),
      ];

      const rollbackOperations = [
        jest.fn().mockResolvedValue('rollback1'),
        jest.fn().mockResolvedValue('rollback2'),
      ];

      mockPrisma.$transaction.mockImplementation(async (operation) => {
        return await operation(mockPrisma);
      });

      // Execute & Assert
      await expect(
        transactionManager.executeWithRollback(operations, rollbackOperations)
      ).rejects.toThrow(DatabaseError);

      expect(operations[0]).toHaveBeenCalled();
      expect(operations[1]).toHaveBeenCalled();
      expect(rollbackOperations[0]).toHaveBeenCalled(); // Should rollback first operation
    });
  });

  describe('savepoint operations', () => {
    const mockTx = { $executeRaw: jest.fn() };

    it('should create savepoint', async () => {
      // Execute
      await transactionManager.createSavepoint(mockTx, 'test_savepoint');

      // Assertions - Prisma template literals are called with array and values
      expect(mockTx.$executeRaw).toHaveBeenCalledWith(
        ['SAVEPOINT ', ''], 'test_savepoint'
      );
    });

    it('should rollback to savepoint', async () => {
      // Execute
      await transactionManager.rollbackToSavepoint(mockTx, 'test_savepoint');

      // Assertions - Prisma template literals are called with array and values
      expect(mockTx.$executeRaw).toHaveBeenCalledWith(
        ['ROLLBACK TO SAVEPOINT ', ''], 'test_savepoint'
      );
    });

    it('should release savepoint', async () => {
      // Execute
      await transactionManager.releaseSavepoint(mockTx, 'test_savepoint');

      // Assertions - Prisma template literals are called with array and values
      expect(mockTx.$executeRaw).toHaveBeenCalledWith(
        ['RELEASE SAVEPOINT ', ''], 'test_savepoint'
      );
    });
  });

  describe('isNonRetryableError', () => {
    it('should identify non-retryable errors correctly', () => {
      // Test cases
      const testCases = [
        { error: new BusinessLogicError('Business logic error'), expected: true },
        { error: { code: 'P2002' }, expected: true }, // Unique constraint
        { error: { code: 'P2003' }, expected: true }, // Foreign key constraint
        { error: { code: 'P2025' }, expected: true }, // Record not found
        { error: new Error('Network error'), expected: false },
        { error: { code: 'P2024' }, expected: false }, // Connection error
      ];

      testCases.forEach(({ error, expected }) => {
        expect(transactionManager.isNonRetryableError(error)).toBe(expected);
      });
    });
  });

  describe('getStatistics', () => {
    it('should return transaction statistics', () => {
      // Execute
      const stats = transactionManager.getStatistics();

      // Assertions
      expect(stats).toHaveProperty('activeTransactions');
      expect(stats).toHaveProperty('totalTransactions');
      expect(stats).toHaveProperty('successfulTransactions');
      expect(stats).toHaveProperty('failedTransactions');
      expect(typeof stats.activeTransactions).toBe('number');
    });
  });
});

describe('BatchOperationManager', () => {
  let batchManager;
  let mockTransactionManager;

  beforeEach(() => {
    mockTransactionManager = {
      executeTransaction: jest.fn(),
    };
    batchManager = new BatchOperationManager(50);
    batchManager.transactionManager = mockTransactionManager;
  });

  describe('processBatches', () => {
    it('should process items in batches', async () => {
      // Setup
      const items = Array.from({ length: 125 }, (_, i) => ({ id: i }));
      const processor = jest.fn().mockResolvedValue('processed');
      const progressCallback = jest.fn();

      mockTransactionManager.executeTransaction.mockImplementation(async (fn) => {
        return await fn(mockPrisma);
      });

      // Execute
      const result = await batchManager.processBatches(items, processor, {
        batchSize: 50,
        progressCallback,
      });

      // Assertions
      expect(result.results).toHaveLength(3); // 125 items / 50 batch size = 3 batches
      expect(result.errors).toHaveLength(0);
      expect(mockTransactionManager.executeTransaction).toHaveBeenCalledTimes(3);
      expect(progressCallback).toHaveBeenCalledTimes(3);
    });

    it('should handle errors and continue processing', async () => {
      // Setup
      const items = [{ id: 1 }, { id: 2 }, { id: 3 }];
      const processor = jest.fn()
        .mockResolvedValueOnce('success')
        .mockRejectedValueOnce(new Error('Processing failed'))
        .mockResolvedValueOnce('success');

      mockTransactionManager.executeTransaction
        .mockResolvedValueOnce('success')
        .mockRejectedValueOnce(new Error('Processing failed'))
        .mockResolvedValueOnce('success');

      // Execute
      const result = await batchManager.processBatches(items, processor, {
        batchSize: 1,
        continueOnError: true,
      });

      // Assertions
      expect(result.results).toHaveLength(2); // Two successful batches (first and third)
      expect(result.results[0]).toBe('success'); // First batch succeeded
      expect(result.results[1]).toBe('success'); // Third batch succeeded
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].error).toBe('Processing failed');
    });

    it('should stop on error when continueOnError is false', async () => {
      // Setup
      const items = [{ id: 1 }, { id: 2 }, { id: 3 }];
      const processor = jest.fn()
        .mockResolvedValueOnce('success')
        .mockRejectedValueOnce(new Error('Processing failed'));

      mockTransactionManager.executeTransaction
        .mockImplementationOnce(async (fn) => await fn(mockPrisma))
        .mockRejectedValueOnce(new Error('Processing failed'));

      // Execute & Assert
      await expect(
        batchManager.processBatches(items, processor, {
          batchSize: 1,
          continueOnError: false,
        })
      ).rejects.toThrow(DatabaseError);
    });
  });
});
