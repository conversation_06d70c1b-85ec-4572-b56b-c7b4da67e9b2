/**
 * Database Transaction Manager
 *
 * This module provides atomic transaction support for complex operations
 * that span multiple database tables and external API calls.
 */

import { container } from '../container/ServiceContainer.js';
import { DatabaseError, BusinessLogicError, ValidationError } from '../errors/AppError.js';

/**
 * Transaction manager for atomic database operations
 */
export class TransactionManager {
  constructor(prisma = null) {
    this.prisma = prisma || container.resolve('database');
    this.activeTransactions = new Map();
  }

  /**
   * Execute a function within a database transaction
   * @param {Function} operation - Function to execute within transaction
   * @param {object} options - Transaction options
   * @returns {Promise<*>} - Result of the operation
   */
  async executeTransaction(operation, options = {}) {
    const {
      timeout = 30000, // 30 seconds default timeout
      isolationLevel = 'ReadCommitted',
      maxRetries = 3,
      retryDelay = 1000,
    } = options;

    let attempt = 0;
    let lastError;

    while (attempt < maxRetries) {
      try {
        const result = await this.prisma.$transaction(
          async (tx) => {
            // Set transaction timeout
            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => {
                reject(new DatabaseError('Transaction timeout exceeded'));
              }, timeout);
            });

            // Execute operation with timeout
            return Promise.race([
              operation(tx),
              timeoutPromise,
            ]);
          },
          {
            isolationLevel,
            timeout,
          }
        );

        return result;
      } catch (error) {
        lastError = error;
        attempt++;

        // Don't retry certain types of errors
        if (this.isNonRetryableError(error) || attempt >= maxRetries) {
          break;
        }

        // Wait before retrying
        await this.sleep(retryDelay * attempt);
      }
    }

    throw new DatabaseError(
      `Transaction failed after ${maxRetries} attempts: ${lastError.message}`,
      { originalError: lastError.message, attempts: attempt }
    );
  }

  /**
   * Execute multiple operations in a single transaction
   * @param {Array<Function>} operations - Array of operations to execute
   * @param {object} options - Transaction options
   * @returns {Promise<Array>} - Results of all operations
   */
  async executeBatch(operations, options = {}) {
    return this.executeTransaction(async (tx) => {
      const results = [];

      for (const operation of operations) {
        const result = await operation(tx);
        results.push(result);
      }

      return results;
    }, options);
  }

  /**
   * Execute operations with rollback on any failure
   * @param {Array<Function>} operations - Operations to execute
   * @param {Array<Function>} rollbackOperations - Rollback operations
   * @param {object} options - Transaction options
   * @returns {Promise<Array>} - Results of operations
   */
  async executeWithRollback(operations, rollbackOperations = [], options = {}) {
    const results = [];
    const completedOperations = [];

    try {
      return await this.executeTransaction(async (tx) => {
        for (let i = 0; i < operations.length; i++) {
          const operation = operations[i];
          const result = await operation(tx);
          results.push(result);
          completedOperations.push(i);
        }

        return results;
      }, options);
    } catch (error) {
      // Execute rollback operations for completed operations
      if (rollbackOperations.length > 0) {
        await this.executeRollback(completedOperations, rollbackOperations);
      }

      throw error;
    }
  }

  /**
   * Execute rollback operations
   * @param {Array<number>} completedOperations - Indices of completed operations
   * @param {Array<Function>} rollbackOperations - Rollback functions
   */
  async executeRollback(completedOperations, rollbackOperations) {
    // Execute rollback operations in reverse order
    for (let i = completedOperations.length - 1; i >= 0; i--) {
      const operationIndex = completedOperations[i];
      const rollbackOperation = rollbackOperations[operationIndex];

      if (rollbackOperation) {
        try {
          await rollbackOperation();
        } catch (rollbackError) {
          console.error(`Rollback operation ${operationIndex} failed:`, rollbackError);
        }
      }
    }
  }

  /**
   * Create a savepoint within a transaction
   * @param {object} tx - Transaction object
   * @param {string} savepointName - Name of the savepoint
   * @returns {Promise<void>}
   */
  async createSavepoint(tx, savepointName) {
    await tx.$executeRaw`SAVEPOINT ${savepointName}`;
  }

  /**
   * Rollback to a savepoint
   * @param {object} tx - Transaction object
   * @param {string} savepointName - Name of the savepoint
   * @returns {Promise<void>}
   */
  async rollbackToSavepoint(tx, savepointName) {
    await tx.$executeRaw`ROLLBACK TO SAVEPOINT ${savepointName}`;
  }

  /**
   * Release a savepoint
   * @param {object} tx - Transaction object
   * @param {string} savepointName - Name of the savepoint
   * @returns {Promise<void>}
   */
  async releaseSavepoint(tx, savepointName) {
    await tx.$executeRaw`RELEASE SAVEPOINT ${savepointName}`;
  }

  /**
   * Check if an error is non-retryable
   * @param {Error} error - The error to check
   * @returns {boolean} - True if error should not be retried
   */
  isNonRetryableError(error) {
    // Don't retry validation errors, constraint violations, etc.
    const nonRetryableCodes = [
      'P2002', // Unique constraint violation
      'P2003', // Foreign key constraint violation
      'P2004', // Constraint violation
      'P2014', // Invalid relation
      'P2025', // Record not found
    ];

    // Check for specific error types
    if (error instanceof BusinessLogicError || error instanceof ValidationError) {
      return true;
    }

    // Check for specific error codes
    if (error.code && nonRetryableCodes.includes(error.code)) {
      return true;
    }

    // Default to retryable for other errors
    return false;
  }

  /**
   * Sleep for specified duration
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get transaction statistics
   * @returns {object} - Transaction statistics
   */
  getStatistics() {
    return {
      activeTransactions: this.activeTransactions.size,
      totalTransactions: this.totalTransactions || 0,
      successfulTransactions: this.successfulTransactions || 0,
      failedTransactions: this.failedTransactions || 0,
    };
  }

  /**
   * Clean up resources
   */
  async cleanup() {
    this.activeTransactions.clear();
  }
}

/**
 * Transaction decorator for automatic transaction management
 * @param {object} options - Transaction options
 * @returns {Function} - Decorator function
 */
export function withTransaction(options = {}) {
  return function(target, propertyName, descriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function(...args) {
      const transactionManager = new TransactionManager();

      return transactionManager.executeTransaction(async (tx) => {
        // Replace prisma instance with transaction instance
        const originalPrisma = this.prisma;
        this.prisma = tx;

        try {
          return await originalMethod.apply(this, args);
        } finally {
          // Restore original prisma instance
          this.prisma = originalPrisma;
        }
      }, options);
    };

    return descriptor;
  };
}

/**
 * Batch operation helper
 */
export class BatchOperationManager {
  constructor(batchSize = 100) {
    this.batchSize = batchSize;
    this.transactionManager = new TransactionManager();
  }

  /**
   * Process items in batches with transaction support
   * @param {Array} items - Items to process
   * @param {Function} processor - Function to process each batch
   * @param {object} options - Processing options
   * @returns {Promise<Array>} - Results from all batches
   */
  async processBatches(items, processor, options = {}) {
    const {
      batchSize = this.batchSize,
      continueOnError = false,
      progressCallback = null,
    } = options;

    const results = [];
    const errors = [];

    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchIndex = Math.floor(i / batchSize);

      try {
        const batchResult = await this.transactionManager.executeTransaction(
          async (tx) => processor(batch, tx, batchIndex),
          options
        );

        results.push(batchResult);

        if (progressCallback) {
          progressCallback({
            batchIndex,
            totalBatches: Math.ceil(items.length / batchSize),
            processedItems: Math.min(i + batchSize, items.length),
            totalItems: items.length,
          });
        }
      } catch (error) {
        errors.push({ batchIndex, error: error.message });

        if (!continueOnError) {
          throw new DatabaseError(
            `Batch processing failed at batch ${batchIndex}: ${error.message}`,
            { batchIndex, errors }
          );
        }
      }
    }

    return { results, errors };
  }
}
