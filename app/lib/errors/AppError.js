/**
 * Application Error Handling Framework
 * 
 * This module provides a comprehensive error handling system with
 * proper error classification, logging, and response formatting.
 */

/**
 * Base application error class
 */
export class AppError extends Error {
  constructor(message, statusCode = 500, code = 'INTERNAL_ERROR', details = null) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
    this.isOperational = true;

    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * Convert error to JSON for API responses
   * @returns {object} - Serialized error object
   */
  toJSON() {
    return {
      error: {
        name: this.name,
        message: this.message,
        code: this.code,
        statusCode: this.statusCode,
        timestamp: this.timestamp,
        ...(this.details && { details: this.details }),
      },
    };
  }

  /**
   * Get safe error message for client responses
   * @returns {string} - Safe error message
   */
  getClientMessage() {
    // In production, don't expose internal error details
    if (process.env.NODE_ENV === 'production' && this.statusCode >= 500) {
      return 'An internal server error occurred';
    }
    return this.message;
  }
}

/**
 * Configuration validation error
 */
export class ConfigurationError extends AppError {
  constructor(message, details = null) {
    super(message, 500, 'CONFIGURATION_ERROR', details);
  }
}

/**
 * Authentication error
 */
export class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed', details = null) {
    super(message, 401, 'AUTHENTICATION_ERROR', details);
  }
}

/**
 * Authorization error
 */
export class AuthorizationError extends AppError {
  constructor(message = 'Access denied', details = null) {
    super(message, 403, 'AUTHORIZATION_ERROR', details);
  }
}

/**
 * Validation error
 */
export class ValidationError extends AppError {
  constructor(message, details = null) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }
}

/**
 * Rate limit error
 */
export class RateLimitError extends AppError {
  constructor(message = 'Rate limit exceeded', retryAfter = null) {
    super(message, 429, 'RATE_LIMIT_ERROR', { retryAfter });
  }
}

/**
 * External API error
 */
export class ExternalApiError extends AppError {
  constructor(service, message, statusCode = 502, details = null) {
    super(`${service} API error: ${message}`, statusCode, 'EXTERNAL_API_ERROR', {
      service,
      ...details,
    });
  }
}

/**
 * Shopify API error
 */
export class ShopifyApiError extends ExternalApiError {
  constructor(message, statusCode = 502, details = null) {
    super('Shopify', message, statusCode, details);
  }
}

/**
 * ShipStation API error
 */
export class ShipStationApiError extends ExternalApiError {
  constructor(message, statusCode = 502, details = null) {
    super('ShipStation', message, statusCode, details);
  }
}

/**
 * Database error
 */
export class DatabaseError extends AppError {
  constructor(message, details = null) {
    super(message, 500, 'DATABASE_ERROR', details);
  }
}

/**
 * Business logic error
 */
export class BusinessLogicError extends AppError {
  constructor(message, details = null) {
    super(message, 422, 'BUSINESS_LOGIC_ERROR', details);
  }
}

/**
 * Resource not found error
 */
export class NotFoundError extends AppError {
  constructor(resource = 'Resource', details = null) {
    super(`${resource} not found`, 404, 'NOT_FOUND_ERROR', details);
  }
}

/**
 * Multistore error
 */
export class MultistoreError extends AppError {
  constructor(message, details = null) {
    super(message, 400, 'MULTISTORE_ERROR', details);
  }
}

/**
 * Error logger utility
 */
export class ErrorLogger {
  /**
   * Log error with appropriate level
   * @param {Error} error - The error to log
   * @param {object} context - Additional context
   */
  static log(error, context = {}) {
    const logData = {
      timestamp: new Date().toISOString(),
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
        ...(error.code && { code: error.code }),
        ...(error.statusCode && { statusCode: error.statusCode }),
      },
      context,
    };

    if (error instanceof AppError && error.statusCode < 500) {
      // Client errors - log as warnings
      console.warn('Client Error:', JSON.stringify(logData, null, 2));
    } else {
      // Server errors - log as errors
      console.error('Server Error:', JSON.stringify(logData, null, 2));
    }
  }

  /**
   * Log error without sensitive information
   * @param {Error} error - The error to log
   * @param {object} context - Additional context
   */
  static logSafe(error, context = {}) {
    // Remove sensitive information from context
    const safeContext = { ...context };
    delete safeContext.apiKey;
    delete safeContext.apiSecret;
    delete safeContext.token;
    delete safeContext.password;

    this.log(error, safeContext);
  }
}

/**
 * Error handler utility functions
 */
export class ErrorHandler {
  /**
   * Handle async route errors
   * @param {Function} fn - Async function to wrap
   * @returns {Function} - Wrapped function with error handling
   */
  static asyncHandler(fn) {
    return async (request, ...args) => {
      try {
        return await fn(request, ...args);
      } catch (error) {
        ErrorLogger.logSafe(error, {
          url: request.url,
          method: request.method,
        });

        if (error instanceof AppError) {
          return Response.json(error.toJSON(), { status: error.statusCode });
        }

        // Handle unexpected errors
        const appError = new AppError(
          'An unexpected error occurred',
          500,
          'UNEXPECTED_ERROR'
        );
        return Response.json(appError.toJSON(), { status: 500 });
      }
    };
  }

  /**
   * Create error response
   * @param {Error} error - The error
   * @param {Request} request - The request object
   * @returns {Response} - Error response
   */
  static createErrorResponse(error, request = null) {
    ErrorLogger.logSafe(error, {
      ...(request && {
        url: request.url,
        method: request.method,
      }),
    });

    if (error instanceof AppError) {
      return Response.json(error.toJSON(), { status: error.statusCode });
    }

    // Handle unexpected errors
    const appError = new AppError(
      'An unexpected error occurred',
      500,
      'UNEXPECTED_ERROR'
    );
    return Response.json(appError.toJSON(), { status: 500 });
  }
}
