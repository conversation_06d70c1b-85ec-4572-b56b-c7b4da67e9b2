/**
 * Reconciliation Job Repository
 * 
 * This repository handles reconciliation job data access operations.
 */

import { BaseRepository } from './base/BaseRepository.js';
import { ValidationError, DatabaseError } from '../lib/errors/AppError.js';

export class ReconciliationJobRepository extends BaseRepository {
  constructor(prisma = null) {
    super('reconciliationJob', prisma);
  }

  /**
   * Create a new reconciliation job
   * @param {string} shop - The shop identifier
   * @param {string} type - Job type ('scheduled', 'manual', 'missing-orders')
   * @param {Date} startDate - Start date for reconciliation
   * @param {Date} endDate - End date for reconciliation
   * @returns {Promise<object>} - Created job
   */
  async createJob(shop, type, startDate, endDate) {
    if (!shop || !type) {
      throw new ValidationError('Shop and type are required');
    }

    if (!startDate || !endDate) {
      throw new ValidationError('Start date and end date are required');
    }

    try {
      return await this.create({
        shop,
        type,
        status: 'queued',
        startDate,
        endDate,
        startedAt: new Date(),
        ordersProcessed: 0,
        ordersSkipped: 0,
        ordersFailed: 0
      });
    } catch (error) {
      throw new DatabaseError(`Failed to create reconciliation job: ${error.message}`, error);
    }
  }

  /**
   * Update job status
   * @param {number} jobId - Job ID
   * @param {string} status - New status ('queued', 'running', 'completed', 'failed')
   * @param {string} error - Optional error message
   * @returns {Promise<object>} - Updated job
   */
  async updateJobStatus(jobId, status, error = null) {
    if (!jobId || !status) {
      throw new ValidationError('Job ID and status are required');
    }

    const validStatuses = ['queued', 'running', 'completed', 'failed'];
    if (!validStatuses.includes(status)) {
      throw new ValidationError(`Status must be one of: ${validStatuses.join(', ')}`);
    }

    try {
      const updateData = { status };
      
      if (status === 'completed' || status === 'failed') {
        updateData.completedAt = new Date();
      }
      
      if (error) {
        updateData.error = error;
      }

      return await this.update(jobId, updateData);
    } catch (error) {
      throw new DatabaseError(`Failed to update job status: ${error.message}`, error);
    }
  }

  /**
   * Update job results
   * @param {number} jobId - Job ID
   * @param {number} ordersProcessed - Number of orders processed
   * @param {number} ordersSkipped - Number of orders skipped
   * @param {number} ordersFailed - Number of orders failed
   * @returns {Promise<object>} - Updated job
   */
  async updateJobResults(jobId, ordersProcessed, ordersSkipped, ordersFailed) {
    if (!jobId) {
      throw new ValidationError('Job ID is required');
    }

    try {
      return await this.update(jobId, {
        ordersProcessed,
        ordersSkipped,
        ordersFailed
      });
    } catch (error) {
      throw new DatabaseError(`Failed to update job results: ${error.message}`, error);
    }
  }

  /**
   * Increment job counters
   * @param {number} jobId - Job ID
   * @param {object} increments - Counters to increment
   * @returns {Promise<object>} - Updated job
   */
  async incrementJobCounters(jobId, increments = {}) {
    if (!jobId) {
      throw new ValidationError('Job ID is required');
    }

    try {
      const updateData = {};
      
      if (increments.ordersProcessed) {
        updateData.ordersProcessed = { increment: increments.ordersProcessed };
      }
      
      if (increments.ordersSkipped) {
        updateData.ordersSkipped = { increment: increments.ordersSkipped };
      }
      
      if (increments.ordersFailed) {
        updateData.ordersFailed = { increment: increments.ordersFailed };
      }

      return await this.update(jobId, updateData);
    } catch (error) {
      throw new DatabaseError(`Failed to increment job counters: ${error.message}`, error);
    }
  }

  /**
   * Get jobs by shop
   * @param {string} shop - The shop identifier
   * @param {object} options - Query options
   * @returns {Promise<Array>} - Array of jobs
   */
  async getJobsByShop(shop, options = {}) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      return await this.findMany(
        { shop },
        {
          orderBy: { startedAt: 'desc' },
          ...options
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get jobs by shop: ${error.message}`, error);
    }
  }

  /**
   * Get jobs by status
   * @param {string} status - Job status
   * @param {string} shop - Optional shop filter
   * @returns {Promise<Array>} - Array of jobs
   */
  async getJobsByStatus(status, shop = null) {
    if (!status) {
      throw new ValidationError('Status is required');
    }

    try {
      const where = { status };
      if (shop) {
        where.shop = shop;
      }

      return await this.findMany(where, {
        orderBy: { startedAt: 'desc' }
      });
    } catch (error) {
      throw new DatabaseError(`Failed to get jobs by status: ${error.message}`, error);
    }
  }

  /**
   * Get running jobs
   * @param {string} shop - Optional shop filter
   * @returns {Promise<Array>} - Array of running jobs
   */
  async getRunningJobs(shop = null) {
    return await this.getJobsByStatus('running', shop);
  }

  /**
   * Get queued jobs
   * @param {string} shop - Optional shop filter
   * @returns {Promise<Array>} - Array of queued jobs
   */
  async getQueuedJobs(shop = null) {
    return await this.getJobsByStatus('queued', shop);
  }

  /**
   * Get recent jobs
   * @param {string} shop - The shop identifier
   * @param {number} limit - Number of jobs to return
   * @returns {Promise<Array>} - Array of recent jobs
   */
  async getRecentJobs(shop, limit = 10) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      return await this.findMany(
        { shop },
        {
          orderBy: { startedAt: 'desc' },
          take: limit
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get recent jobs: ${error.message}`, error);
    }
  }

  /**
   * Get jobs within date range
   * @param {string} shop - The shop identifier
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Promise<Array>} - Array of jobs
   */
  async getJobsByDateRange(shop, startDate, endDate) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    if (!startDate || !endDate) {
      throw new ValidationError('Start date and end date are required');
    }

    try {
      return await this.findMany(
        {
          shop,
          startedAt: {
            gte: startDate,
            lte: endDate
          }
        },
        {
          orderBy: { startedAt: 'desc' }
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get jobs by date range: ${error.message}`, error);
    }
  }

  /**
   * Get job statistics for a shop
   * @param {string} shop - The shop identifier
   * @param {Date} startDate - Optional start date filter
   * @param {Date} endDate - Optional end date filter
   * @returns {Promise<object>} - Job statistics
   */
  async getJobStatistics(shop, startDate = null, endDate = null) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      const where = { shop };
      
      if (startDate && endDate) {
        where.startedAt = {
          gte: startDate,
          lte: endDate
        };
      }

      const [totalJobs, completedJobs, failedJobs, runningJobs] = await Promise.all([
        this.count(where),
        this.count({ ...where, status: 'completed' }),
        this.count({ ...where, status: 'failed' }),
        this.count({ ...where, status: 'running' })
      ]);

      const aggregateResult = await this.prisma.reconciliationJob.aggregate({
        where,
        _sum: {
          ordersProcessed: true,
          ordersSkipped: true,
          ordersFailed: true
        }
      });

      return {
        totalJobs,
        completedJobs,
        failedJobs,
        runningJobs,
        queuedJobs: totalJobs - completedJobs - failedJobs - runningJobs,
        totalOrdersProcessed: aggregateResult._sum.ordersProcessed || 0,
        totalOrdersSkipped: aggregateResult._sum.ordersSkipped || 0,
        totalOrdersFailed: aggregateResult._sum.ordersFailed || 0
      };
    } catch (error) {
      throw new DatabaseError(`Failed to get job statistics: ${error.message}`, error);
    }
  }

  /**
   * Mark job as failed
   * @param {number} jobId - Job ID
   * @param {string} errorMessage - Error message
   * @returns {Promise<object>} - Updated job
   */
  async markJobFailed(jobId, errorMessage) {
    if (!jobId || !errorMessage) {
      throw new ValidationError('Job ID and error message are required');
    }

    try {
      return await this.updateJobStatus(jobId, 'failed', errorMessage);
    } catch (error) {
      throw new DatabaseError(`Failed to mark job as failed: ${error.message}`, error);
    }
  }

  /**
   * Check if there are overlapping jobs
   * @param {string} shop - The shop identifier
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @param {Array} excludeStatuses - Statuses to exclude from check
   * @returns {Promise<boolean>} - True if overlapping jobs exist
   */
  async hasOverlappingJobs(shop, startDate, endDate, excludeStatuses = ['failed', 'completed']) {
    if (!shop || !startDate || !endDate) {
      throw new ValidationError('Shop, start date, and end date are required');
    }

    try {
      const count = await this.count({
        shop,
        status: {
          notIn: excludeStatuses
        },
        OR: [
          {
            startDate: { lte: endDate },
            endDate: { gte: startDate }
          }
        ]
      });

      return count > 0;
    } catch (error) {
      throw new DatabaseError(`Failed to check for overlapping jobs: ${error.message}`, error);
    }
  }

  /**
   * Validate reconciliation job data
   * @param {object} data - Job data to validate
   */
  validateCreateData(data) {
    super.validateCreateData(data);
    
    if (!data.shop) {
      throw new ValidationError('Shop is required');
    }
    
    if (!data.type) {
      throw new ValidationError('Type is required');
    }
    
    const validTypes = ['scheduled', 'manual', 'missing-orders'];
    if (!validTypes.includes(data.type)) {
      throw new ValidationError(`Type must be one of: ${validTypes.join(', ')}`);
    }
    
    if (!data.startDate || !data.endDate) {
      throw new ValidationError('Start date and end date are required');
    }
    
    if (data.startDate >= data.endDate) {
      throw new ValidationError('Start date must be before end date');
    }
  }
}
