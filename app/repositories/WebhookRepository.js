/**
 * Webhook Repository
 * 
 * This repository handles processed webhook data access operations.
 */

import { BaseRepository } from './base/BaseRepository.js';
import { ValidationError, DatabaseError } from '../lib/errors/AppError.js';

export class WebhookRepository extends BaseRepository {
  constructor(prisma = null) {
    super('processedWebhook', prisma);
  }

  /**
   * Check if webhook has been processed
   * @param {string} shop - The shop identifier
   * @param {string} webhookId - The webhook ID
   * @returns {Promise<boolean>} - True if webhook has been processed
   */
  async isWebhookProcessed(shop, webhookId) {
    if (!shop || !webhookId) {
      throw new ValidationError('Shop and webhook ID are required');
    }

    try {
      const count = await this.count({ shop, webhookId });
      return count > 0;
    } catch (error) {
      throw new DatabaseError(`Failed to check webhook processing status: ${error.message}`, error);
    }
  }

  /**
   * Check if event has been processed
   * @param {string} shop - The shop identifier
   * @param {string} eventId - The event ID
   * @returns {Promise<boolean>} - True if event has been processed
   */
  async isEventProcessed(shop, eventId) {
    if (!shop || !eventId) {
      throw new ValidationError('Shop and event ID are required');
    }

    try {
      const count = await this.count({ shop, eventId });
      return count > 0;
    } catch (error) {
      throw new DatabaseError(`Failed to check event processing status: ${error.message}`, error);
    }
  }

  /**
   * Mark webhook as processed
   * @param {string} shop - The shop identifier
   * @param {string} topic - The webhook topic
   * @param {string} webhookId - The webhook ID
   * @param {string} eventId - Optional event ID
   * @param {string} orderId - Optional order ID
   * @param {string} subscriptionId - Optional subscription ID
   * @returns {Promise<object>} - Created webhook record
   */
  async markWebhookProcessed(shop, topic, webhookId, eventId = null, orderId = null, subscriptionId = null) {
    if (!shop || !topic || !webhookId) {
      throw new ValidationError('Shop, topic, and webhook ID are required');
    }

    try {
      return await this.create({
        shop,
        topic,
        webhookId,
        eventId,
        orderId,
        subscriptionId,
        processedAt: new Date()
      });
    } catch (error) {
      // Handle unique constraint violations gracefully
      if (error.code === 'P2002') {
        console.log(`Webhook ${webhookId} for shop ${shop} already processed`);
        return await this.findFirst({ shop, webhookId });
      }
      throw new DatabaseError(`Failed to mark webhook as processed: ${error.message}`, error);
    }
  }

  /**
   * Get processed webhooks for a shop
   * @param {string} shop - The shop identifier
   * @param {object} options - Query options
   * @returns {Promise<Array>} - Array of processed webhooks
   */
  async getProcessedWebhooks(shop, options = {}) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      return await this.findMany(
        { shop },
        {
          orderBy: { processedAt: 'desc' },
          ...options
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get processed webhooks: ${error.message}`, error);
    }
  }

  /**
   * Get processed webhooks by topic
   * @param {string} shop - The shop identifier
   * @param {string} topic - The webhook topic
   * @param {object} options - Query options
   * @returns {Promise<Array>} - Array of processed webhooks
   */
  async getWebhooksByTopic(shop, topic, options = {}) {
    if (!shop || !topic) {
      throw new ValidationError('Shop and topic are required');
    }

    try {
      return await this.findMany(
        { shop, topic },
        {
          orderBy: { processedAt: 'desc' },
          ...options
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get webhooks by topic: ${error.message}`, error);
    }
  }

  /**
   * Get processed webhooks by order ID
   * @param {string} shop - The shop identifier
   * @param {string} orderId - The order ID
   * @returns {Promise<Array>} - Array of processed webhooks for the order
   */
  async getWebhooksByOrderId(shop, orderId) {
    if (!shop || !orderId) {
      throw new ValidationError('Shop and order ID are required');
    }

    try {
      return await this.findMany(
        { shop, orderId },
        {
          orderBy: { processedAt: 'desc' }
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get webhooks by order ID: ${error.message}`, error);
    }
  }

  /**
   * Get webhooks processed within date range
   * @param {string} shop - The shop identifier
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Promise<Array>} - Array of processed webhooks
   */
  async getWebhooksByDateRange(shop, startDate, endDate) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    if (!startDate || !endDate) {
      throw new ValidationError('Start date and end date are required');
    }

    try {
      return await this.findMany(
        {
          shop,
          processedAt: {
            gte: startDate,
            lte: endDate
          }
        },
        {
          orderBy: { processedAt: 'desc' }
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get webhooks by date range: ${error.message}`, error);
    }
  }

  /**
   * Get webhook statistics for a shop
   * @param {string} shop - The shop identifier
   * @param {Date} startDate - Optional start date filter
   * @param {Date} endDate - Optional end date filter
   * @returns {Promise<object>} - Webhook statistics
   */
  async getWebhookStatistics(shop, startDate = null, endDate = null) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      const where = { shop };
      
      if (startDate && endDate) {
        where.processedAt = {
          gte: startDate,
          lte: endDate
        };
      }

      const [totalWebhooks, topicStats] = await Promise.all([
        this.count(where),
        this.prisma.processedWebhook.groupBy({
          by: ['topic'],
          where,
          _count: {
            id: true
          },
          orderBy: {
            _count: {
              id: 'desc'
            }
          }
        })
      ]);

      return {
        totalWebhooks,
        topicBreakdown: topicStats.map(stat => ({
          topic: stat.topic,
          count: stat._count.id
        }))
      };
    } catch (error) {
      throw new DatabaseError(`Failed to get webhook statistics: ${error.message}`, error);
    }
  }

  /**
   * Clean up old webhook records
   * @param {string} shop - The shop identifier
   * @param {Date} cutoffDate - Date before which to delete records
   * @returns {Promise<object>} - Delete result
   */
  async cleanupOldWebhooks(shop, cutoffDate) {
    if (!shop || !cutoffDate) {
      throw new ValidationError('Shop and cutoff date are required');
    }

    try {
      return await this.deleteMany({
        shop,
        processedAt: {
          lt: cutoffDate
        }
      });
    } catch (error) {
      throw new DatabaseError(`Failed to cleanup old webhooks: ${error.message}`, error);
    }
  }

  /**
   * Get duplicate webhooks (same shop and webhook ID)
   * @param {string} shop - The shop identifier
   * @returns {Promise<Array>} - Array of duplicate webhook groups
   */
  async getDuplicateWebhooks(shop) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      const duplicates = await this.prisma.processedWebhook.groupBy({
        by: ['shop', 'webhookId'],
        where: { shop },
        having: {
          webhookId: {
            _count: {
              gt: 1
            }
          }
        },
        _count: {
          id: true
        }
      });

      return duplicates.map(duplicate => ({
        shop: duplicate.shop,
        webhookId: duplicate.webhookId,
        count: duplicate._count.id
      }));
    } catch (error) {
      throw new DatabaseError(`Failed to get duplicate webhooks: ${error.message}`, error);
    }
  }

  /**
   * Get recent webhook activity
   * @param {string} shop - The shop identifier
   * @param {number} limit - Number of recent webhooks to return
   * @returns {Promise<Array>} - Array of recent webhooks
   */
  async getRecentActivity(shop, limit = 50) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      return await this.findMany(
        { shop },
        {
          orderBy: { processedAt: 'desc' },
          take: limit,
          select: {
            topic: true,
            webhookId: true,
            orderId: true,
            processedAt: true
          }
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get recent webhook activity: ${error.message}`, error);
    }
  }

  /**
   * Check for webhook processing gaps
   * @param {string} shop - The shop identifier
   * @param {string} topic - The webhook topic
   * @param {Date} startDate - Start date to check
   * @param {Date} endDate - End date to check
   * @returns {Promise<Array>} - Array of potential gaps
   */
  async checkProcessingGaps(shop, topic, startDate, endDate) {
    if (!shop || !topic || !startDate || !endDate) {
      throw new ValidationError('Shop, topic, start date, and end date are required');
    }

    try {
      // Get all webhooks for the topic in the date range
      const webhooks = await this.findMany(
        {
          shop,
          topic,
          processedAt: {
            gte: startDate,
            lte: endDate
          }
        },
        {
          orderBy: { processedAt: 'asc' },
          select: {
            webhookId: true,
            processedAt: true
          }
        }
      );

      // Analyze for gaps (this is a simplified implementation)
      const gaps = [];
      for (let i = 1; i < webhooks.length; i++) {
        const timeDiff = webhooks[i].processedAt - webhooks[i - 1].processedAt;
        const hoursDiff = timeDiff / (1000 * 60 * 60);
        
        // Flag gaps longer than 2 hours as potential issues
        if (hoursDiff > 2) {
          gaps.push({
            startTime: webhooks[i - 1].processedAt,
            endTime: webhooks[i].processedAt,
            gapHours: hoursDiff
          });
        }
      }

      return gaps;
    } catch (error) {
      throw new DatabaseError(`Failed to check processing gaps: ${error.message}`, error);
    }
  }

  /**
   * Validate webhook data
   * @param {object} data - Webhook data to validate
   */
  validateCreateData(data) {
    super.validateCreateData(data);
    
    if (!data.shop) {
      throw new ValidationError('Shop is required');
    }
    
    if (!data.topic) {
      throw new ValidationError('Topic is required');
    }
    
    if (!data.webhookId) {
      throw new ValidationError('Webhook ID is required');
    }
  }
}
