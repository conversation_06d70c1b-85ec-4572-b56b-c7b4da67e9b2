/**
 * Base Repository Tests
 * 
 * Tests for the BaseRepository class functionality.
 */

import { jest } from '@jest/globals';
import { BaseRepository } from './BaseRepository.js';
import { ValidationError, DatabaseError } from '../../lib/errors/AppError.js';

// Mock Prisma client
const mockPrisma = {
  testModel: {
    findUnique: jest.fn(),
    findFirst: jest.fn(),
    findMany: jest.fn(),
    count: jest.fn(),
    create: jest.fn(),
    createMany: jest.fn(),
    update: jest.fn(),
    updateMany: jest.fn(),
    upsert: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn()
  },
  $transaction: jest.fn()
};

// Mock container
jest.mock('../../lib/container/ServiceContainer.js', () => ({
  container: {
    resolve: jest.fn(() => mockPrisma)
  }
}));

describe('BaseRepository', () => {
  let repository;

  beforeEach(() => {
    repository = new BaseRepository('testModel', mockPrisma);
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with model name and prisma client', () => {
      expect(repository.modelName).toBe('testModel');
      expect(repository.prisma).toBe(mockPrisma);
      expect(repository.model).toBe(mockPrisma.testModel);
    });

    it('should throw error for invalid model name', () => {
      expect(() => new BaseRepository('invalidModel', mockPrisma)).toThrow();
    });
  });

  describe('findById', () => {
    it('should find record by ID', async () => {
      const mockRecord = { id: 1, name: 'test' };
      mockPrisma.testModel.findUnique.mockResolvedValue(mockRecord);

      const result = await repository.findById(1);

      expect(mockPrisma.testModel.findUnique).toHaveBeenCalledWith({
        where: { id: 1 }
      });
      expect(result).toEqual(mockRecord);
    });

    it('should handle database errors', async () => {
      mockPrisma.testModel.findUnique.mockRejectedValue(new Error('DB Error'));

      await expect(repository.findById(1)).rejects.toThrow(DatabaseError);
    });
  });

  describe('findFirst', () => {
    it('should find first record matching criteria', async () => {
      const mockRecord = { id: 1, name: 'test' };
      const where = { name: 'test' };
      mockPrisma.testModel.findFirst.mockResolvedValue(mockRecord);

      const result = await repository.findFirst(where);

      expect(mockPrisma.testModel.findFirst).toHaveBeenCalledWith({
        where
      });
      expect(result).toEqual(mockRecord);
    });
  });

  describe('findMany', () => {
    it('should find multiple records', async () => {
      const mockRecords = [{ id: 1, name: 'test1' }, { id: 2, name: 'test2' }];
      const where = { active: true };
      const options = { orderBy: { name: 'asc' } };
      mockPrisma.testModel.findMany.mockResolvedValue(mockRecords);

      const result = await repository.findMany(where, options);

      expect(mockPrisma.testModel.findMany).toHaveBeenCalledWith({
        where,
        ...options
      });
      expect(result).toEqual(mockRecords);
    });
  });

  describe('count', () => {
    it('should count records matching criteria', async () => {
      const where = { active: true };
      mockPrisma.testModel.count.mockResolvedValue(5);

      const result = await repository.count(where);

      expect(mockPrisma.testModel.count).toHaveBeenCalledWith({ where });
      expect(result).toBe(5);
    });
  });

  describe('create', () => {
    it('should create new record', async () => {
      const data = { name: 'test' };
      const mockRecord = { id: 1, ...data };
      mockPrisma.testModel.create.mockResolvedValue(mockRecord);

      const result = await repository.create(data);

      expect(mockPrisma.testModel.create).toHaveBeenCalledWith({
        data
      });
      expect(result).toEqual(mockRecord);
    });

    it('should validate data before creating', async () => {
      await expect(repository.create(null)).rejects.toThrow(ValidationError);
      await expect(repository.create('invalid')).rejects.toThrow(ValidationError);
    });
  });

  describe('update', () => {
    it('should update record by ID', async () => {
      const data = { name: 'updated' };
      const mockRecord = { id: 1, ...data };
      mockPrisma.testModel.update.mockResolvedValue(mockRecord);

      const result = await repository.update(1, data);

      expect(mockPrisma.testModel.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data
      });
      expect(result).toEqual(mockRecord);
    });

    it('should validate data before updating', async () => {
      await expect(repository.update(1, null)).rejects.toThrow(ValidationError);
    });
  });

  describe('upsert', () => {
    it('should upsert record', async () => {
      const where = { name: 'test' };
      const create = { name: 'test', value: 1 };
      const update = { value: 2 };
      const mockRecord = { id: 1, name: 'test', value: 2 };
      mockPrisma.testModel.upsert.mockResolvedValue(mockRecord);

      const result = await repository.upsert(where, create, update);

      expect(mockPrisma.testModel.upsert).toHaveBeenCalledWith({
        where,
        create,
        update
      });
      expect(result).toEqual(mockRecord);
    });
  });

  describe('delete', () => {
    it('should delete record by ID', async () => {
      const mockRecord = { id: 1, name: 'test' };
      mockPrisma.testModel.delete.mockResolvedValue(mockRecord);

      const result = await repository.delete(1);

      expect(mockPrisma.testModel.delete).toHaveBeenCalledWith({
        where: { id: 1 }
      });
      expect(result).toEqual(mockRecord);
    });
  });

  describe('transaction', () => {
    it('should execute transaction', async () => {
      const callback = jest.fn().mockResolvedValue('result');
      mockPrisma.$transaction.mockResolvedValue('result');

      const result = await repository.transaction(callback);

      expect(mockPrisma.$transaction).toHaveBeenCalledWith(callback);
      expect(result).toBe('result');
    });
  });

  describe('exists', () => {
    it('should check if record exists', async () => {
      mockPrisma.testModel.findUnique.mockResolvedValue({ id: 1 });

      const result = await repository.exists(1);

      expect(result).toBe(true);
    });

    it('should return false if record does not exist', async () => {
      mockPrisma.testModel.findUnique.mockResolvedValue(null);

      const result = await repository.exists(1);

      expect(result).toBe(false);
    });
  });

  describe('paginate', () => {
    it('should return paginated results', async () => {
      const mockRecords = [{ id: 1 }, { id: 2 }];
      const totalCount = 10;
      
      mockPrisma.testModel.findMany.mockResolvedValue(mockRecords);
      mockPrisma.testModel.count.mockResolvedValue(totalCount);

      const result = await repository.paginate({}, 1, 2);

      expect(result).toEqual({
        data: mockRecords,
        pagination: {
          page: 1,
          pageSize: 2,
          total: 10,
          totalPages: 5,
          hasNext: true,
          hasPrev: false
        }
      });
    });

    it('should handle last page correctly', async () => {
      const mockRecords = [{ id: 9 }, { id: 10 }];
      const totalCount = 10;
      
      mockPrisma.testModel.findMany.mockResolvedValue(mockRecords);
      mockPrisma.testModel.count.mockResolvedValue(totalCount);

      const result = await repository.paginate({}, 5, 2);

      expect(result.pagination.hasNext).toBe(false);
      expect(result.pagination.hasPrev).toBe(true);
    });
  });

  describe('validation', () => {
    it('should validate create data', () => {
      expect(() => repository.validateCreateData({})).not.toThrow();
      expect(() => repository.validateCreateData(null)).toThrow(ValidationError);
      expect(() => repository.validateCreateData('string')).toThrow(ValidationError);
    });

    it('should validate update data', () => {
      expect(() => repository.validateUpdateData({})).not.toThrow();
      expect(() => repository.validateUpdateData(null)).toThrow(ValidationError);
      expect(() => repository.validateUpdateData('string')).toThrow(ValidationError);
    });
  });

  describe('utility methods', () => {
    it('should return model name', () => {
      expect(repository.getModelName()).toBe('testModel');
    });

    it('should return query builder', () => {
      const queryBuilder = repository.getQueryBuilder();
      expect(queryBuilder).toBeDefined();
    });
  });
});
