/**
 * Query Builder Class
 * 
 * This class provides a fluent interface for building complex Prisma queries
 * with dynamic conditions, sorting, filtering, and aggregations.
 */

import { ValidationError } from '../../lib/errors/AppError.js';

export class QueryBuilder {
  constructor(model) {
    this.model = model;
    this.reset();
  }

  /**
   * Reset the query builder to initial state
   */
  reset() {
    this.query = {
      where: {},
      select: null,
      include: null,
      orderBy: [],
      take: null,
      skip: null,
      distinct: null
    };
    return this;
  }

  /**
   * Add where conditions
   * @param {object|Function} conditions - Where conditions or function
   * @returns {QueryBuilder} - This instance for chaining
   */
  where(conditions) {
    if (typeof conditions === 'function') {
      const subBuilder = new QueryBuilder(this.model);
      conditions(subBuilder);
      this.query.where = { ...this.query.where, ...subBuilder.query.where };
    } else {
      this.query.where = { ...this.query.where, ...conditions };
    }
    return this;
  }

  /**
   * Add AND conditions
   * @param {object} conditions - Conditions to AND
   * @returns {QueryBuilder} - This instance for chaining
   */
  and(conditions) {
    if (!this.query.where.AND) {
      this.query.where.AND = [];
    }
    this.query.where.AND.push(conditions);
    return this;
  }

  /**
   * Add OR conditions
   * @param {object} conditions - Conditions to OR
   * @returns {QueryBuilder} - This instance for chaining
   */
  or(conditions) {
    if (!this.query.where.OR) {
      this.query.where.OR = [];
    }
    this.query.where.OR.push(conditions);
    return this;
  }

  /**
   * Add NOT conditions
   * @param {object} conditions - Conditions to negate
   * @returns {QueryBuilder} - This instance for chaining
   */
  not(conditions) {
    this.query.where.NOT = conditions;
    return this;
  }

  /**
   * Add equals condition
   * @param {string} field - Field name
   * @param {any} value - Value to match
   * @returns {QueryBuilder} - This instance for chaining
   */
  equals(field, value) {
    this.query.where[field] = value;
    return this;
  }

  /**
   * Add in condition
   * @param {string} field - Field name
   * @param {Array} values - Array of values
   * @returns {QueryBuilder} - This instance for chaining
   */
  in(field, values) {
    if (!Array.isArray(values)) {
      throw new ValidationError('Values for "in" condition must be an array');
    }
    this.query.where[field] = { in: values };
    return this;
  }

  /**
   * Add not in condition
   * @param {string} field - Field name
   * @param {Array} values - Array of values to exclude
   * @returns {QueryBuilder} - This instance for chaining
   */
  notIn(field, values) {
    if (!Array.isArray(values)) {
      throw new ValidationError('Values for "notIn" condition must be an array');
    }
    this.query.where[field] = { notIn: values };
    return this;
  }

  /**
   * Add contains condition (for strings)
   * @param {string} field - Field name
   * @param {string} value - Value to search for
   * @param {boolean} caseSensitive - Whether search is case sensitive
   * @returns {QueryBuilder} - This instance for chaining
   */
  contains(field, value, caseSensitive = false) {
    this.query.where[field] = {
      contains: value,
      mode: caseSensitive ? 'default' : 'insensitive'
    };
    return this;
  }

  /**
   * Add starts with condition
   * @param {string} field - Field name
   * @param {string} value - Value to match at start
   * @returns {QueryBuilder} - This instance for chaining
   */
  startsWith(field, value) {
    this.query.where[field] = { startsWith: value };
    return this;
  }

  /**
   * Add ends with condition
   * @param {string} field - Field name
   * @param {string} value - Value to match at end
   * @returns {QueryBuilder} - This instance for chaining
   */
  endsWith(field, value) {
    this.query.where[field] = { endsWith: value };
    return this;
  }

  /**
   * Add greater than condition
   * @param {string} field - Field name
   * @param {any} value - Value to compare
   * @returns {QueryBuilder} - This instance for chaining
   */
  gt(field, value) {
    this.query.where[field] = { gt: value };
    return this;
  }

  /**
   * Add greater than or equal condition
   * @param {string} field - Field name
   * @param {any} value - Value to compare
   * @returns {QueryBuilder} - This instance for chaining
   */
  gte(field, value) {
    this.query.where[field] = { gte: value };
    return this;
  }

  /**
   * Add less than condition
   * @param {string} field - Field name
   * @param {any} value - Value to compare
   * @returns {QueryBuilder} - This instance for chaining
   */
  lt(field, value) {
    this.query.where[field] = { lt: value };
    return this;
  }

  /**
   * Add less than or equal condition
   * @param {string} field - Field name
   * @param {any} value - Value to compare
   * @returns {QueryBuilder} - This instance for chaining
   */
  lte(field, value) {
    this.query.where[field] = { lte: value };
    return this;
  }

  /**
   * Add date range condition
   * @param {string} field - Field name
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {QueryBuilder} - This instance for chaining
   */
  dateRange(field, startDate, endDate) {
    this.query.where[field] = {
      gte: startDate,
      lte: endDate
    };
    return this;
  }

  /**
   * Add null check condition
   * @param {string} field - Field name
   * @param {boolean} isNull - Whether field should be null
   * @returns {QueryBuilder} - This instance for chaining
   */
  isNull(field, isNull = true) {
    this.query.where[field] = isNull ? null : { not: null };
    return this;
  }

  /**
   * Set select fields
   * @param {object|Array} fields - Fields to select
   * @returns {QueryBuilder} - This instance for chaining
   */
  select(fields) {
    if (Array.isArray(fields)) {
      this.query.select = fields.reduce((acc, field) => {
        acc[field] = true;
        return acc;
      }, {});
    } else {
      this.query.select = fields;
    }
    return this;
  }

  /**
   * Set include relations
   * @param {object} relations - Relations to include
   * @returns {QueryBuilder} - This instance for chaining
   */
  include(relations) {
    this.query.include = relations;
    return this;
  }

  /**
   * Add order by clause
   * @param {string} field - Field to order by
   * @param {string} direction - 'asc' or 'desc'
   * @returns {QueryBuilder} - This instance for chaining
   */
  orderBy(field, direction = 'asc') {
    if (!['asc', 'desc'].includes(direction)) {
      throw new ValidationError('Order direction must be "asc" or "desc"');
    }
    this.query.orderBy.push({ [field]: direction });
    return this;
  }

  /**
   * Set limit (take)
   * @param {number} limit - Number of records to take
   * @returns {QueryBuilder} - This instance for chaining
   */
  limit(limit) {
    if (typeof limit !== 'number' || limit < 0) {
      throw new ValidationError('Limit must be a non-negative number');
    }
    this.query.take = limit;
    return this;
  }

  /**
   * Set offset (skip)
   * @param {number} offset - Number of records to skip
   * @returns {QueryBuilder} - This instance for chaining
   */
  offset(offset) {
    if (typeof offset !== 'number' || offset < 0) {
      throw new ValidationError('Offset must be a non-negative number');
    }
    this.query.skip = offset;
    return this;
  }

  /**
   * Set distinct fields
   * @param {Array} fields - Fields for distinct selection
   * @returns {QueryBuilder} - This instance for chaining
   */
  distinct(fields) {
    if (!Array.isArray(fields)) {
      throw new ValidationError('Distinct fields must be an array');
    }
    this.query.distinct = fields;
    return this;
  }

  /**
   * Build and return the final query object
   * @returns {object} - Prisma query object
   */
  build() {
    const query = { ...this.query };
    
    // Clean up empty arrays and null values
    if (query.orderBy.length === 0) {
      delete query.orderBy;
    }
    
    Object.keys(query).forEach(key => {
      if (query[key] === null || query[key] === undefined) {
        delete query[key];
      }
    });

    return query;
  }

  /**
   * Execute findMany with built query
   * @returns {Promise<Array>} - Query results
   */
  async findMany() {
    const query = this.build();
    return await this.model.findMany(query);
  }

  /**
   * Execute findFirst with built query
   * @returns {Promise<object|null>} - First matching record
   */
  async findFirst() {
    const query = this.build();
    return await this.model.findFirst(query);
  }

  /**
   * Execute count with built query
   * @returns {Promise<number>} - Count of matching records
   */
  async count() {
    const { where } = this.build();
    return await this.model.count({ where });
  }

  /**
   * Clone the query builder
   * @returns {QueryBuilder} - New query builder instance
   */
  clone() {
    const cloned = new QueryBuilder(this.model);
    cloned.query = JSON.parse(JSON.stringify(this.query));
    return cloned;
  }
}
