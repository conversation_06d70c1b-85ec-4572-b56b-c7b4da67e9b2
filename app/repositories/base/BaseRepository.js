/**
 * Base Repository Class
 *
 * This class provides common CRUD operations and serves as the foundation
 * for all repository implementations in the application.
 */

import { container } from '../../lib/container/ServiceContainer.server.js';
import { DatabaseError, ValidationError } from '../../lib/errors/AppError.js';
import { QueryBuilder } from './QueryBuilder.js';

export class BaseRepository {
  constructor(modelName, prisma = null) {
    this.modelName = modelName;
    this.prisma = prisma;
    this.initialized = false;

    // Initialize async if no prisma provided
    if (!this.prisma) {
      this.initializeAsync();
    } else {
      this.model = this.prisma[modelName];
      this.queryBuilder = new QueryBuilder(this.model);
      this.initialized = true;

      if (!this.model) {
        throw new Error(`Model '${modelName}' not found in Prisma client`);
      }
    }
  }

  async initializeAsync() {
    if (!this.initialized) {
      this.prisma = await container.resolve('database');
      this.model = this.prisma[this.modelName];
      this.queryBuilder = new QueryBuilder(this.model);
      this.initialized = true;

      if (!this.model) {
        throw new Error(`Model '${this.modelName}' not found in Prisma client`);
      }
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      await this.initializeAsync();
    }
  }

  /**
   * Find a single record by ID
   * @param {string|number} id - The record ID
   * @param {object} options - Query options (include, select, etc.)
   * @returns {Promise<object|null>} - The record or null if not found
   */
  async findById(id, options = {}) {
    try {
      return await this.model.findUnique({
        where: { id },
        ...options
      });
    } catch (error) {
      throw new DatabaseError(`Failed to find ${this.modelName} by ID: ${error.message}`, error);
    }
  }

  /**
   * Find a single record by criteria
   * @param {object} where - Where conditions
   * @param {object} options - Query options
   * @returns {Promise<object|null>} - The record or null if not found
   */
  async findFirst(where = {}, options = {}) {
    try {
      return await this.model.findFirst({
        where,
        ...options
      });
    } catch (error) {
      throw new DatabaseError(`Failed to find first ${this.modelName}: ${error.message}`, error);
    }
  }

  /**
   * Find multiple records
   * @param {object} where - Where conditions
   * @param {object} options - Query options (orderBy, take, skip, include, select)
   * @returns {Promise<Array>} - Array of records
   */
  async findMany(where = {}, options = {}) {
    try {
      return await this.model.findMany({
        where,
        ...options
      });
    } catch (error) {
      throw new DatabaseError(`Failed to find ${this.modelName} records: ${error.message}`, error);
    }
  }

  /**
   * Count records matching criteria
   * @param {object} where - Where conditions
   * @returns {Promise<number>} - Count of matching records
   */
  async count(where = {}) {
    try {
      return await this.model.count({ where });
    } catch (error) {
      throw new DatabaseError(`Failed to count ${this.modelName} records: ${error.message}`, error);
    }
  }

  /**
   * Create a new record
   * @param {object} data - Data to create
   * @param {object} options - Query options
   * @returns {Promise<object>} - The created record
   */
  async create(data, options = {}) {
    try {
      this.validateCreateData(data);
      return await this.model.create({
        data,
        ...options
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new DatabaseError(`Failed to create ${this.modelName}: ${error.message}`, error);
    }
  }

  /**
   * Create multiple records
   * @param {Array} data - Array of data objects to create
   * @param {object} options - Query options
   * @returns {Promise<object>} - Batch result
   */
  async createMany(data, options = {}) {
    try {
      data.forEach(item => this.validateCreateData(item));
      return await this.model.createMany({
        data,
        ...options
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new DatabaseError(`Failed to create multiple ${this.modelName}: ${error.message}`, error);
    }
  }

  /**
   * Update a record by ID
   * @param {string|number} id - The record ID
   * @param {object} data - Data to update
   * @param {object} options - Query options
   * @returns {Promise<object>} - The updated record
   */
  async update(id, data, options = {}) {
    try {
      this.validateUpdateData(data);
      return await this.model.update({
        where: { id },
        data,
        ...options
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new DatabaseError(`Failed to update ${this.modelName}: ${error.message}`, error);
    }
  }

  /**
   * Update multiple records
   * @param {object} where - Where conditions
   * @param {object} data - Data to update
   * @returns {Promise<object>} - Update result
   */
  async updateMany(where, data) {
    try {
      this.validateUpdateData(data);
      return await this.model.updateMany({
        where,
        data
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new DatabaseError(`Failed to update multiple ${this.modelName}: ${error.message}`, error);
    }
  }

  /**
   * Upsert a record (create or update)
   * @param {object} where - Where conditions for finding existing record
   * @param {object} create - Data for creating new record
   * @param {object} update - Data for updating existing record
   * @param {object} options - Query options
   * @returns {Promise<object>} - The upserted record
   */
  async upsert(where, create, update, options = {}) {
    try {
      this.validateCreateData(create);
      this.validateUpdateData(update);
      return await this.model.upsert({
        where,
        create,
        update,
        ...options
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new DatabaseError(`Failed to upsert ${this.modelName}: ${error.message}`, error);
    }
  }

  /**
   * Delete a record by ID
   * @param {string|number} id - The record ID
   * @returns {Promise<object>} - The deleted record
   */
  async delete(id) {
    try {
      return await this.model.delete({
        where: { id }
      });
    } catch (error) {
      throw new DatabaseError(`Failed to delete ${this.modelName}: ${error.message}`, error);
    }
  }

  /**
   * Delete multiple records
   * @param {object} where - Where conditions
   * @returns {Promise<object>} - Delete result
   */
  async deleteMany(where) {
    try {
      return await this.model.deleteMany({ where });
    } catch (error) {
      throw new DatabaseError(`Failed to delete multiple ${this.modelName}: ${error.message}`, error);
    }
  }

  /**
   * Execute a transaction
   * @param {Function} callback - Transaction callback
   * @returns {Promise<any>} - Transaction result
   */
  async transaction(callback) {
    try {
      return await this.prisma.$transaction(callback);
    } catch (error) {
      throw new DatabaseError(`Transaction failed for ${this.modelName}: ${error.message}`, error);
    }
  }

  /**
   * Get query builder for complex queries
   * @returns {QueryBuilder} - Query builder instance
   */
  getQueryBuilder() {
    return this.queryBuilder;
  }

  /**
   * Validate data for create operations
   * Override in subclasses for specific validation
   * @param {object} data - Data to validate
   */
  validateCreateData(data) {
    if (!data || typeof data !== 'object') {
      throw new ValidationError('Create data must be a valid object');
    }
  }

  /**
   * Validate data for update operations
   * Override in subclasses for specific validation
   * @param {object} data - Data to validate
   */
  validateUpdateData(data) {
    if (!data || typeof data !== 'object') {
      throw new ValidationError('Update data must be a valid object');
    }
  }

  /**
   * Get model name
   * @returns {string} - Model name
   */
  getModelName() {
    return this.modelName;
  }

  /**
   * Check if record exists by ID
   * @param {string|number} id - The record ID
   * @returns {Promise<boolean>} - True if record exists
   */
  async exists(id) {
    try {
      const record = await this.findById(id, { select: { id: true } });
      return !!record;
    } catch (error) {
      throw new DatabaseError(`Failed to check existence of ${this.modelName}: ${error.message}`, error);
    }
  }

  /**
   * Get paginated results
   * @param {object} where - Where conditions
   * @param {number} page - Page number (1-based)
   * @param {number} pageSize - Number of records per page
   * @param {object} options - Additional query options
   * @returns {Promise<object>} - Paginated results with metadata
   */
  async paginate(where = {}, page = 1, pageSize = 10, options = {}) {
    try {
      const skip = (page - 1) * pageSize;
      const [records, total] = await Promise.all([
        this.findMany(where, { ...options, skip, take: pageSize }),
        this.count(where)
      ]);

      return {
        data: records,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
          hasNext: page * pageSize < total,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      throw new DatabaseError(`Failed to paginate ${this.modelName}: ${error.message}`, error);
    }
  }
}
