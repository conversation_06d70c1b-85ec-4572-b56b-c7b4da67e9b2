/**
 * Shop Repository Tests
 * 
 * Tests for the ShopRepository class functionality.
 */

import { jest } from '@jest/globals';
import { ShopRepository } from './ShopRepository.js';
import { ValidationError, DatabaseError } from '../lib/errors/AppError.js';

// Mock Prisma client
const mockPrisma = {
  session: {
    findMany: jest.fn(),
    findFirst: jest.fn(),
    count: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn()
  },
  $queryRaw: jest.fn()
};

// Mock container
jest.mock('../lib/container/ServiceContainer.js', () => ({
  container: {
    resolve: jest.fn(() => mockPrisma)
  }
}));

describe('ShopRepository', () => {
  let repository;

  beforeEach(() => {
    repository = new ShopRepository(mockPrisma);
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('getAllShops', () => {
    it('should return all unique shops', async () => {
      const mockShops = [
        { shop: 'shop1.myshopify.com', accessToken: 'token1' },
        { shop: 'shop2.myshopify.com', accessToken: 'token2' }
      ];
      mockPrisma.session.findMany.mockResolvedValue(mockShops);

      const result = await repository.getAllShops();

      expect(mockPrisma.session.findMany).toHaveBeenCalledWith({
        select: {
          shop: true,
          accessToken: true,
          isOnline: true,
          scope: true,
          expires: true
        },
        distinct: ['shop'],
        orderBy: { shop: 'asc' }
      });
      expect(result).toEqual(mockShops);
    });
  });

  describe('getShopByDomain', () => {
    it('should return shop by domain', async () => {
      const mockShop = {
        shop: 'test.myshopify.com',
        accessToken: 'token123',
        isOnline: true
      };
      mockPrisma.session.findFirst.mockResolvedValue(mockShop);

      const result = await repository.getShopByDomain('test.myshopify.com');

      expect(mockPrisma.session.findFirst).toHaveBeenCalledWith(
        { shop: 'test.myshopify.com' },
        expect.objectContaining({
          select: expect.objectContaining({
            shop: true,
            accessToken: true,
            isOnline: true
          })
        })
      );
      expect(result).toEqual(mockShop);
    });

    it('should throw validation error for missing shop domain', async () => {
      await expect(repository.getShopByDomain('')).rejects.toThrow(ValidationError);
      await expect(repository.getShopByDomain(null)).rejects.toThrow(ValidationError);
    });
  });

  describe('getShopAccessToken', () => {
    it('should return access token for shop', async () => {
      const mockShop = { accessToken: 'token123' };
      mockPrisma.session.findFirst.mockResolvedValue(mockShop);

      const result = await repository.getShopAccessToken('test.myshopify.com');

      expect(result).toBe('token123');
    });

    it('should return null if shop not found', async () => {
      mockPrisma.session.findFirst.mockResolvedValue(null);

      const result = await repository.getShopAccessToken('nonexistent.myshopify.com');

      expect(result).toBeNull();
    });

    it('should throw validation error for missing shop domain', async () => {
      await expect(repository.getShopAccessToken('')).rejects.toThrow(ValidationError);
    });
  });

  describe('shopExists', () => {
    it('should return true if shop exists', async () => {
      mockPrisma.session.count.mockResolvedValue(1);

      const result = await repository.shopExists('test.myshopify.com');

      expect(mockPrisma.session.count).toHaveBeenCalledWith({ shop: 'test.myshopify.com' });
      expect(result).toBe(true);
    });

    it('should return false if shop does not exist', async () => {
      mockPrisma.session.count.mockResolvedValue(0);

      const result = await repository.shopExists('nonexistent.myshopify.com');

      expect(result).toBe(false);
    });
  });

  describe('getActiveShops', () => {
    it('should return shops with active sessions', async () => {
      const mockShops = [
        { shop: 'shop1.myshopify.com', expires: null },
        { shop: 'shop2.myshopify.com', expires: new Date(Date.now() + 86400000) }
      ];
      mockPrisma.session.findMany.mockResolvedValue(mockShops);

      const result = await repository.getActiveShops();

      expect(mockPrisma.session.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          OR: [
            { expires: null },
            { expires: { gt: expect.any(Date) } }
          ]
        }),
        expect.any(Object)
      );
      expect(result).toEqual(mockShops);
    });
  });

  describe('getShopsByScope', () => {
    it('should return shops with required scope', async () => {
      const mockShops = [
        { shop: 'shop1.myshopify.com', scope: 'read_orders,write_orders' }
      ];
      mockPrisma.session.findMany.mockResolvedValue(mockShops);

      const result = await repository.getShopsByScope('read_orders');

      expect(mockPrisma.session.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          scope: {
            contains: 'read_orders'
          }
        }),
        expect.any(Object)
      );
      expect(result).toEqual(mockShops);
    });

    it('should throw validation error for missing scope', async () => {
      await expect(repository.getShopsByScope('')).rejects.toThrow(ValidationError);
    });
  });

  describe('updateShopSession', () => {
    it('should update existing shop session', async () => {
      const existingSession = { id: 'session123', shop: 'test.myshopify.com' };
      const updateData = { accessToken: 'newtoken' };
      const updatedSession = { ...existingSession, ...updateData };

      mockPrisma.session.findFirst.mockResolvedValue(existingSession);
      mockPrisma.session.update.mockResolvedValue(updatedSession);

      const result = await repository.updateShopSession('test.myshopify.com', updateData);

      expect(mockPrisma.session.update).toHaveBeenCalledWith({
        where: { id: 'session123' },
        data: updateData
      });
      expect(result).toEqual(updatedSession);
    });

    it('should throw error if session not found', async () => {
      mockPrisma.session.findFirst.mockResolvedValue(null);

      await expect(
        repository.updateShopSession('nonexistent.myshopify.com', { accessToken: 'token' })
      ).rejects.toThrow(ValidationError);
    });

    it('should validate input parameters', async () => {
      await expect(
        repository.updateShopSession('', { accessToken: 'token' })
      ).rejects.toThrow(ValidationError);

      await expect(
        repository.updateShopSession('test.myshopify.com', null)
      ).rejects.toThrow(ValidationError);
    });
  });

  describe('upsertShopSession', () => {
    it('should upsert shop session', async () => {
      const sessionData = {
        accessToken: 'token123',
        isOnline: true,
        scope: 'read_orders'
      };
      const upsertedSession = {
        id: 'session123',
        shop: 'test.myshopify.com',
        ...sessionData
      };

      mockPrisma.session.upsert = jest.fn().mockResolvedValue(upsertedSession);

      const result = await repository.upsertShopSession('test.myshopify.com', sessionData);

      expect(mockPrisma.session.upsert).toHaveBeenCalledWith({
        where: { shop: 'test.myshopify.com' },
        create: { shop: 'test.myshopify.com', ...sessionData },
        update: sessionData
      });
      expect(result).toEqual(upsertedSession);
    });
  });

  describe('deleteShopSession', () => {
    it('should delete shop session', async () => {
      const existingSession = { id: 'session123', shop: 'test.myshopify.com' };
      mockPrisma.session.findFirst.mockResolvedValue(existingSession);
      mockPrisma.session.delete.mockResolvedValue(existingSession);

      const result = await repository.deleteShopSession('test.myshopify.com');

      expect(mockPrisma.session.delete).toHaveBeenCalledWith({
        where: { id: 'session123' }
      });
      expect(result).toEqual(existingSession);
    });

    it('should throw error if session not found', async () => {
      mockPrisma.session.findFirst.mockResolvedValue(null);

      await expect(
        repository.deleteShopSession('nonexistent.myshopify.com')
      ).rejects.toThrow(ValidationError);
    });
  });

  describe('getShopCount', () => {
    it('should return total number of unique shops', async () => {
      mockPrisma.$queryRaw.mockResolvedValue([{ count: '5' }]);

      const result = await repository.getShopCount();

      expect(mockPrisma.$queryRaw).toHaveBeenCalled();
      expect(result).toBe(5);
    });
  });

  describe('getShopsWithExpiredSessions', () => {
    it('should return shops with expired sessions', async () => {
      const expiredShops = [
        { shop: 'expired1.myshopify.com', expires: new Date('2023-01-01') },
        { shop: 'expired2.myshopify.com', expires: new Date('2023-01-02') }
      ];
      mockPrisma.session.findMany.mockResolvedValue(expiredShops);

      const result = await repository.getShopsWithExpiredSessions();

      expect(mockPrisma.session.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          expires: {
            lt: expect.any(Date)
          }
        }),
        expect.any(Object)
      );
      expect(result).toEqual(expiredShops);
    });
  });

  describe('validation', () => {
    it('should validate create data', () => {
      const validData = {
        shop: 'test.myshopify.com',
        accessToken: 'token123'
      };

      expect(() => repository.validateCreateData(validData)).not.toThrow();

      expect(() => repository.validateCreateData({})).toThrow(ValidationError);
      expect(() => repository.validateCreateData({ shop: 'test.myshopify.com' })).toThrow(ValidationError);
      expect(() => repository.validateCreateData({ accessToken: 'token123' })).toThrow(ValidationError);
    });
  });
});
