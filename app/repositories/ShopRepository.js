/**
 * Shop Repository
 *
 * This repository handles shop-related data access operations,
 * primarily working with Session data to manage shop information.
 */

import { BaseRepository } from './base/BaseRepository.js';
import { ValidationError, DatabaseError } from '../lib/errors/AppError.js';

export class ShopRepository extends BaseRepository {
  constructor(prisma = null) {
    super('session', prisma);
  }

  /**
   * Get all unique shops from sessions
   * @returns {Promise<Array>} - Array of shop objects
   */
  async getAllShops() {
    try {
      const shops = await this.findMany({}, {
        select: {
          shop: true,
          accessToken: true,
          isOnline: true,
          scope: true,
          expires: true
        },
        distinct: ['shop'],
        orderBy: { shop: 'asc' }
      });

      return shops;
    } catch (error) {
      throw new DatabaseError(`Failed to get all shops: ${error.message}`, error);
    }
  }

  /**
   * Get a shop by domain
   * @param {string} shopDomain - The shop domain
   * @returns {Promise<object|null>} - Shop data or null if not found
   */
  async getShopByDomain(shopDomain) {
    if (!shopDomain) {
      throw new ValidationError('Shop domain is required');
    }

    try {
      return await this.findFirst(
        { shop: shopDomain },
        {
          select: {
            shop: true,
            accessToken: true,
            isOnline: true,
            scope: true,
            expires: true,
            userId: true,
            firstName: true,
            lastName: true,
            email: true,
            accountOwner: true,
            locale: true
          }
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get shop by domain: ${error.message}`, error);
    }
  }

  /**
   * Get shop access token
   * @param {string} shopDomain - The shop domain
   * @returns {Promise<string|null>} - Access token or null if not found
   */
  async getShopAccessToken(shopDomain) {
    if (!shopDomain) {
      throw new ValidationError('Shop domain is required');
    }

    try {
      const shop = await this.findFirst(
        { shop: shopDomain },
        { select: { accessToken: true } }
      );

      return shop?.accessToken || null;
    } catch (error) {
      throw new DatabaseError(`Failed to get shop access token: ${error.message}`, error);
    }
  }

  /**
   * Check if shop exists
   * @param {string} shopDomain - The shop domain
   * @returns {Promise<boolean>} - True if shop exists
   */
  async shopExists(shopDomain) {
    if (!shopDomain) {
      throw new ValidationError('Shop domain is required');
    }

    try {
      const count = await this.count({ shop: shopDomain });
      return count > 0;
    } catch (error) {
      throw new DatabaseError(`Failed to check shop existence: ${error.message}`, error);
    }
  }

  /**
   * Get shops with active sessions
   * @returns {Promise<Array>} - Array of shops with active sessions
   */
  async getActiveShops() {
    try {
      const now = new Date();

      return await this.findMany(
        {
          OR: [
            { expires: null }, // Sessions that don't expire
            { expires: { gt: now } } // Sessions that haven't expired
          ]
        },
        {
          select: {
            shop: true,
            accessToken: true,
            expires: true,
            isOnline: true
          },
          distinct: ['shop'],
          orderBy: { shop: 'asc' }
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get active shops: ${error.message}`, error);
    }
  }

  /**
   * Get shops by scope
   * @param {string} requiredScope - The required scope
   * @returns {Promise<Array>} - Array of shops with the required scope
   */
  async getShopsByScope(requiredScope) {
    if (!requiredScope) {
      throw new ValidationError('Required scope is required');
    }

    try {
      return await this.findMany(
        {
          scope: {
            contains: requiredScope
          }
        },
        {
          select: {
            shop: true,
            scope: true,
            accessToken: true
          },
          distinct: ['shop'],
          orderBy: { shop: 'asc' }
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get shops by scope: ${error.message}`, error);
    }
  }

  /**
   * Update shop session
   * @param {string} shopDomain - The shop domain
   * @param {object} sessionData - Session data to update
   * @returns {Promise<object>} - Updated session
   */
  async updateShopSession(shopDomain, sessionData) {
    if (!shopDomain) {
      throw new ValidationError('Shop domain is required');
    }

    if (!sessionData || typeof sessionData !== 'object') {
      throw new ValidationError('Session data must be a valid object');
    }

    try {
      // Find existing session
      const existingSession = await this.findFirst({ shop: shopDomain });

      if (!existingSession) {
        throw new ValidationError(`No session found for shop: ${shopDomain}`);
      }

      return await this.update(existingSession.id, sessionData);
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new DatabaseError(`Failed to update shop session: ${error.message}`, error);
    }
  }

  /**
   * Create or update shop session
   * @param {string} shopDomain - The shop domain
   * @param {object} sessionData - Session data
   * @returns {Promise<object>} - Created or updated session
   */
  async upsertShopSession(shopDomain, sessionData) {
    if (!shopDomain) {
      throw new ValidationError('Shop domain is required');
    }

    if (!sessionData || typeof sessionData !== 'object') {
      throw new ValidationError('Session data must be a valid object');
    }

    try {
      return await this.upsert(
        { shop: shopDomain },
        { shop: shopDomain, ...sessionData },
        sessionData
      );
    } catch (error) {
      throw new DatabaseError(`Failed to upsert shop session: ${error.message}`, error);
    }
  }

  /**
   * Delete shop session
   * @param {string} shopDomain - The shop domain
   * @returns {Promise<object>} - Deleted session
   */
  async deleteShopSession(shopDomain) {
    if (!shopDomain) {
      throw new ValidationError('Shop domain is required');
    }

    try {
      const session = await this.findFirst({ shop: shopDomain });

      if (!session) {
        throw new ValidationError(`No session found for shop: ${shopDomain}`);
      }

      return await this.delete(session.id);
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new DatabaseError(`Failed to delete shop session: ${error.message}`, error);
    }
  }

  /**
   * Get shop count
   * @returns {Promise<number>} - Total number of unique shops
   */
  async getShopCount() {
    try {
      // Use raw query to count distinct shops
      const result = await this.prisma.$queryRaw`
        SELECT COUNT(DISTINCT shop) as count FROM "Session"
      `;

      return parseInt(result[0].count);
    } catch (error) {
      throw new DatabaseError(`Failed to get shop count: ${error.message}`, error);
    }
  }

  /**
   * Get shops with expired sessions
   * @returns {Promise<Array>} - Array of shops with expired sessions
   */
  async getShopsWithExpiredSessions() {
    try {
      const now = new Date();

      return await this.findMany(
        {
          expires: {
            lt: now
          }
        },
        {
          select: {
            shop: true,
            expires: true,
            accessToken: true
          },
          distinct: ['shop'],
          orderBy: { expires: 'asc' }
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get shops with expired sessions: ${error.message}`, error);
    }
  }

  /**
   * Validate shop data
   * @param {object} data - Shop data to validate
   */
  validateCreateData(data) {
    super.validateCreateData(data);

    if (!data.shop) {
      throw new ValidationError('Shop domain is required');
    }

    if (!data.accessToken) {
      throw new ValidationError('Access token is required');
    }
  }

  /**
   * Validate update data
   * @param {object} data - Update data to validate
   */
  validateUpdateData(data) {
    super.validateUpdateData(data);

    // Additional validation for shop updates can be added here
  }
}
