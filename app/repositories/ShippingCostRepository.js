/**
 * Shipping Cost Repository
 * 
 * This repository handles shipping cost and transaction data access operations.
 */

import { BaseRepository } from './base/BaseRepository.js';
import { ValidationError, DatabaseError } from '../lib/errors/AppError.js';

export class ShippingCostRepository extends BaseRepository {
  constructor(prisma = null) {
    super('shippingCost', prisma);
  }

  /**
   * Get shipping costs for a shop by month and year
   * @param {string} shop - The shop identifier
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @returns {Promise<Array>} - Array of shipping costs
   */
  async getCostsByMonth(shop, month, year) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      return await this.findMany(
        { shop, month, year },
        {
          include: {
            transactions: {
              orderBy: { createdAt: 'desc' }
            }
          },
          orderBy: { storeId: 'asc' }
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get costs by month: ${error.message}`, error);
    }
  }

  /**
   * Get shipping cost by shop, store, month, and year
   * @param {string} shop - The shop identifier
   * @param {string} storeId - The store ID
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @returns {Promise<object|null>} - Shipping cost or null
   */
  async getCostByStore(shop, storeId, month, year) {
    if (!shop || !storeId) {
      throw new ValidationError('Shop and store ID are required');
    }

    try {
      return await this.findFirst(
        { shop, storeId, month, year },
        {
          include: {
            transactions: {
              orderBy: { createdAt: 'desc' }
            }
          }
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get cost by store: ${error.message}`, error);
    }
  }

  /**
   * Create or update shipping cost
   * @param {string} shop - The shop identifier
   * @param {string} storeId - The store ID
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @param {number} shippingAmount - Shipping amount
   * @param {number} markupAmount - Markup amount
   * @param {number} totalAmount - Total amount
   * @returns {Promise<object>} - Created or updated shipping cost
   */
  async upsertShippingCost(shop, storeId, month, year, shippingAmount, markupAmount, totalAmount) {
    if (!shop || !storeId) {
      throw new ValidationError('Shop and store ID are required');
    }

    if (typeof shippingAmount !== 'number' || typeof markupAmount !== 'number' || typeof totalAmount !== 'number') {
      throw new ValidationError('Shipping amount, markup amount, and total amount must be numbers');
    }

    try {
      return await this.upsert(
        { shop, storeId, month, year },
        {
          shop,
          storeId,
          month,
          year,
          quantity: 1,
          shippingCost: shippingAmount,
          markupAmount,
          totalAmount
        },
        {
          quantity: { increment: 1 },
          shippingCost: { increment: shippingAmount },
          markupAmount: { increment: markupAmount },
          totalAmount: { increment: totalAmount },
          updatedAt: new Date()
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to upsert shipping cost: ${error.message}`, error);
    }
  }

  /**
   * Add transaction to shipping cost
   * @param {number} shippingCostId - Shipping cost ID
   * @param {number} amount - Total transaction amount
   * @param {number} shippingAmount - Shipping amount
   * @param {number} markupAmount - Markup amount
   * @param {string} description - Transaction description
   * @param {object} metadata - Optional metadata
   * @returns {Promise<object>} - Created transaction
   */
  async addTransaction(shippingCostId, amount, shippingAmount, markupAmount, description, metadata = null) {
    if (!shippingCostId) {
      throw new ValidationError('Shipping cost ID is required');
    }

    if (typeof amount !== 'number' || typeof shippingAmount !== 'number' || typeof markupAmount !== 'number') {
      throw new ValidationError('Amount, shipping amount, and markup amount must be numbers');
    }

    if (!description) {
      throw new ValidationError('Description is required');
    }

    try {
      return await this.prisma.shippingTransaction.create({
        data: {
          shippingCostId,
          amount,
          shippingAmount,
          markupAmount,
          description,
          metadata: metadata ? JSON.stringify(metadata) : null
        }
      });
    } catch (error) {
      throw new DatabaseError(`Failed to add shipping transaction: ${error.message}`, error);
    }
  }

  /**
   * Get total shipping costs for a shop by month and year
   * @param {string} shop - The shop identifier
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @returns {Promise<object>} - Total shipping cost summary
   */
  async getTotalCosts(shop, month, year) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      const result = await this.prisma.shippingCost.aggregate({
        where: { shop, month, year },
        _sum: {
          quantity: true,
          shippingCost: true,
          markupAmount: true,
          totalAmount: true
        }
      });

      return {
        totalQuantity: result._sum.quantity || 0,
        totalShippingCost: result._sum.shippingCost || 0,
        totalMarkupAmount: result._sum.markupAmount || 0,
        totalAmount: result._sum.totalAmount || 0
      };
    } catch (error) {
      throw new DatabaseError(`Failed to get total costs: ${error.message}`, error);
    }
  }

  /**
   * Get shipping costs by store for a shop
   * @param {string} shop - The shop identifier
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @returns {Promise<Array>} - Shipping costs grouped by store
   */
  async getCostsByStore(shop, month, year) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      const result = await this.prisma.shippingCost.groupBy({
        by: ['storeId'],
        where: { shop, month, year },
        _sum: {
          quantity: true,
          shippingCost: true,
          markupAmount: true,
          totalAmount: true
        },
        orderBy: {
          storeId: 'asc'
        }
      });

      return result.map(item => ({
        storeId: item.storeId,
        totalQuantity: item._sum.quantity || 0,
        totalShippingCost: item._sum.shippingCost || 0,
        totalMarkupAmount: item._sum.markupAmount || 0,
        totalAmount: item._sum.totalAmount || 0
      }));
    } catch (error) {
      throw new DatabaseError(`Failed to get costs by store: ${error.message}`, error);
    }
  }

  /**
   * Get all shipping costs for a shop
   * @param {string} shop - The shop identifier
   * @param {object} options - Query options
   * @returns {Promise<Array>} - Array of all shipping costs
   */
  async getAllCosts(shop, options = {}) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      return await this.findMany(
        { shop },
        {
          include: {
            transactions: {
              orderBy: { createdAt: 'desc' }
            }
          },
          orderBy: [
            { year: 'desc' },
            { month: 'desc' },
            { storeId: 'asc' }
          ],
          ...options
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get all costs: ${error.message}`, error);
    }
  }

  /**
   * Get shipping costs within a date range
   * @param {string} shop - The shop identifier
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Promise<Array>} - Array of shipping costs
   */
  async getCostsByDateRange(shop, startDate, endDate) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    if (!startDate || !endDate) {
      throw new ValidationError('Start date and end date are required');
    }

    try {
      const startMonth = startDate.getMonth();
      const startYear = startDate.getFullYear();
      const endMonth = endDate.getMonth();
      const endYear = endDate.getFullYear();

      return await this.findMany(
        {
          shop,
          OR: [
            { year: { gt: startYear, lt: endYear } },
            { year: startYear, month: { gte: startMonth } },
            { year: endYear, month: { lte: endMonth } }
          ]
        },
        {
          include: {
            transactions: {
              orderBy: { createdAt: 'desc' }
            }
          },
          orderBy: [
            { year: 'desc' },
            { month: 'desc' },
            { storeId: 'asc' }
          ]
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get costs by date range: ${error.message}`, error);
    }
  }

  /**
   * Delete shipping cost and all associated transactions
   * @param {number} costId - Cost ID to delete
   * @returns {Promise<object>} - Deleted cost
   */
  async deleteCost(costId) {
    if (!costId) {
      throw new ValidationError('Cost ID is required');
    }

    try {
      // Transactions will be deleted automatically due to cascade delete
      return await this.delete(costId);
    } catch (error) {
      throw new DatabaseError(`Failed to delete shipping cost: ${error.message}`, error);
    }
  }

  /**
   * Get shipping costs with pagination
   * @param {string} shop - The shop identifier
   * @param {number} page - Page number
   * @param {number} pageSize - Page size
   * @param {object} filters - Additional filters
   * @returns {Promise<object>} - Paginated results
   */
  async getPaginatedCosts(shop, page = 1, pageSize = 10, filters = {}) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      const where = { shop, ...filters };
      
      return await this.paginate(
        where,
        page,
        pageSize,
        {
          include: {
            transactions: {
              orderBy: { createdAt: 'desc' },
              take: 5 // Limit transactions per cost
            }
          },
          orderBy: [
            { year: 'desc' },
            { month: 'desc' },
            { storeId: 'asc' }
          ]
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get paginated costs: ${error.message}`, error);
    }
  }

  /**
   * Validate shipping cost data
   * @param {object} data - Shipping cost data to validate
   */
  validateCreateData(data) {
    super.validateCreateData(data);
    
    if (!data.shop) {
      throw new ValidationError('Shop is required');
    }
    
    if (!data.storeId) {
      throw new ValidationError('Store ID is required');
    }
    
    if (typeof data.month !== 'number' || data.month < 0 || data.month > 11) {
      throw new ValidationError('Month must be a number between 0 and 11');
    }
    
    if (typeof data.year !== 'number' || data.year < 2000) {
      throw new ValidationError('Year must be a valid number');
    }
    
    if (typeof data.quantity !== 'number') {
      throw new ValidationError('Quantity must be a number');
    }
    
    if (typeof data.shippingCost !== 'number') {
      throw new ValidationError('Shipping cost must be a number');
    }
    
    if (typeof data.markupAmount !== 'number') {
      throw new ValidationError('Markup amount must be a number');
    }
    
    if (typeof data.totalAmount !== 'number') {
      throw new ValidationError('Total amount must be a number');
    }
  }
}
