/**
 * Invoice Balance Repository
 *
 * This repository handles invoice balance and transaction data access operations.
 */

import { BaseRepository } from './base/BaseRepository.js';
import { ValidationError, DatabaseError } from '../lib/errors/AppError.js';

export class InvoiceBalanceRepository extends BaseRepository {
  constructor(prisma = null) {
    super('invoiceBalance', prisma);
  }

  /**
   * Get invoice balances for a shop by month and year
   * @param {string} shop - The shop identifier
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @returns {Promise<Array>} - Array of invoice balances
   */
  async getBalancesByMonth(shop, month, year) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      return await this.findMany(
        { shop, month, year },
        {
          include: {
            transactions: {
              orderBy: { createdAt: 'desc' }
            }
          },
          orderBy: { category: 'asc' }
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get balances by month: ${error.message}`, error);
    }
  }

  /**
   * Get invoice balances for a shop within a date range
   * @param {string} shop - The shop identifier
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Promise<Array>} - Array of invoice balances
   */
  async getBalancesByDateRange(shop, startDate, endDate) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    if (!startDate || !endDate) {
      throw new ValidationError('Start date and end date are required');
    }

    try {
      const startMonth = startDate.getMonth();
      const startYear = startDate.getFullYear();
      const endMonth = endDate.getMonth();
      const endYear = endDate.getFullYear();

      return await this.findMany(
        {
          shop,
          OR: [
            { year: { gt: startYear, lt: endYear } },
            { year: startYear, month: { gte: startMonth } },
            { year: endYear, month: { lte: endMonth } }
          ]
        },
        {
          include: {
            transactions: {
              orderBy: { createdAt: 'desc' }
            }
          },
          orderBy: [
            { year: 'desc' },
            { month: 'desc' },
            { category: 'asc' }
          ]
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get balances by date range: ${error.message}`, error);
    }
  }

  /**
   * Get balance by shop, category, month, and year
   * @param {string} shop - The shop identifier
   * @param {string} category - The category
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @returns {Promise<object|null>} - Invoice balance or null
   */
  async getBalanceByCategory(shop, category, month, year) {
    if (!shop || !category) {
      throw new ValidationError('Shop and category are required');
    }

    try {
      return await this.findFirst(
        { shop, category, month, year },
        {
          include: {
            transactions: {
              orderBy: { createdAt: 'desc' }
            }
          }
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get balance by category: ${error.message}`, error);
    }
  }

  /**
   * Create or update invoice balance
   * @param {string} shop - The shop identifier
   * @param {string} category - The category
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @param {number} quantity - Quantity to add
   * @param {number} balance - Balance amount to add
   * @param {number|null} groupId - Optional group ID
   * @returns {Promise<object>} - Created or updated balance
   */
  async upsertBalance(shop, category, month, year, quantity, balance, groupId = null) {
    if (!shop || !category) {
      throw new ValidationError('Shop and category are required');
    }

    if (typeof quantity !== 'number' || typeof balance !== 'number') {
      throw new ValidationError('Quantity and balance must be numbers');
    }

    try {
      return await this.model.upsert({
        where: {
          shop_category_month_year: {
            shop,
            category,
            month,
            year,
          },
        },
        create: {
          shop,
          category,
          month,
          year,
          quantity,
          balance,
          groupId
        },
        update: {
          quantity: { increment: quantity },
          balance: { increment: balance }
        }
      });
    } catch (error) {
      throw new DatabaseError(`Failed to upsert balance: ${error.message}`, error);
    }
  }

  /**
   * Add transaction to invoice balance
   * @param {number} invoiceBalanceId - Invoice balance ID
   * @param {number} amount - Transaction amount
   * @param {string} description - Transaction description
   * @param {object} metadata - Optional metadata
   * @returns {Promise<object>} - Created transaction
   */
  async addTransaction(invoiceBalanceId, amount, description, metadata = null) {
    if (!invoiceBalanceId) {
      throw new ValidationError('Invoice balance ID is required');
    }

    if (typeof amount !== 'number') {
      throw new ValidationError('Amount must be a number');
    }

    if (!description) {
      throw new ValidationError('Description is required');
    }

    try {
      return await this.prisma.invoiceTransaction.create({
        data: {
          invoiceBalanceId,
          amount,
          description,
          metadata: metadata ? JSON.stringify(metadata) : null
        }
      });
    } catch (error) {
      throw new DatabaseError(`Failed to add transaction: ${error.message}`, error);
    }
  }

  /**
   * Get total balance for a shop by month and year
   * @param {string} shop - The shop identifier
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @returns {Promise<number>} - Total balance
   */
  async getTotalBalance(shop, month, year) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      const result = await this.prisma.invoiceBalance.aggregate({
        where: { shop, month, year },
        _sum: { balance: true }
      });

      return result._sum.balance || 0;
    } catch (error) {
      throw new DatabaseError(`Failed to get total balance: ${error.message}`, error);
    }
  }

  /**
   * Get balance summary by category for a shop
   * @param {string} shop - The shop identifier
   * @param {number} month - Month (0-11)
   * @param {number} year - Year
   * @returns {Promise<Array>} - Balance summary by category
   */
  async getBalanceSummaryByCategory(shop, month, year) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      const result = await this.prisma.invoiceBalance.groupBy({
        by: ['category'],
        where: { shop, month, year },
        _sum: {
          quantity: true,
          balance: true
        },
        orderBy: {
          category: 'asc'
        }
      });

      return result.map(item => ({
        category: item.category,
        totalQuantity: item._sum.quantity || 0,
        totalBalance: item._sum.balance || 0
      }));
    } catch (error) {
      throw new DatabaseError(`Failed to get balance summary: ${error.message}`, error);
    }
  }

  /**
   * Get all balances for a shop
   * @param {string} shop - The shop identifier
   * @param {object} options - Query options
   * @returns {Promise<Array>} - Array of all balances
   */
  async getAllBalances(shop, options = {}) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      return await this.findMany(
        { shop },
        {
          include: {
            transactions: {
              orderBy: { createdAt: 'desc' }
            }
          },
          orderBy: [
            { year: 'desc' },
            { month: 'desc' },
            { category: 'asc' }
          ],
          ...options
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get all balances: ${error.message}`, error);
    }
  }

  /**
   * Delete balance and all associated transactions
   * @param {number} balanceId - Balance ID to delete
   * @returns {Promise<object>} - Deleted balance
   */
  async deleteBalance(balanceId) {
    if (!balanceId) {
      throw new ValidationError('Balance ID is required');
    }

    try {
      // Transactions will be deleted automatically due to cascade delete
      return await this.delete(balanceId);
    } catch (error) {
      throw new DatabaseError(`Failed to delete balance: ${error.message}`, error);
    }
  }

  /**
   * Get balances with pagination
   * @param {string} shop - The shop identifier
   * @param {number} page - Page number
   * @param {number} pageSize - Page size
   * @param {object} filters - Additional filters
   * @returns {Promise<object>} - Paginated results
   */
  async getPaginatedBalances(shop, page = 1, pageSize = 10, filters = {}) {
    if (!shop) {
      throw new ValidationError('Shop is required');
    }

    try {
      const where = { shop, ...filters };

      return await this.paginate(
        where,
        page,
        pageSize,
        {
          include: {
            transactions: {
              orderBy: { createdAt: 'desc' },
              take: 5 // Limit transactions per balance
            }
          },
          orderBy: [
            { year: 'desc' },
            { month: 'desc' },
            { category: 'asc' }
          ]
        }
      );
    } catch (error) {
      throw new DatabaseError(`Failed to get paginated balances: ${error.message}`, error);
    }
  }

  /**
   * Validate balance data
   * @param {object} data - Balance data to validate
   */
  validateCreateData(data) {
    super.validateCreateData(data);

    if (!data.shop) {
      throw new ValidationError('Shop is required');
    }

    if (!data.category) {
      throw new ValidationError('Category is required');
    }

    if (typeof data.month !== 'number' || data.month < 0 || data.month > 11) {
      throw new ValidationError('Month must be a number between 0 and 11');
    }

    if (typeof data.year !== 'number' || data.year < 2000) {
      throw new ValidationError('Year must be a valid number');
    }

    if (typeof data.quantity !== 'number') {
      throw new ValidationError('Quantity must be a number');
    }

    if (typeof data.balance !== 'number') {
      throw new ValidationError('Balance must be a number');
    }
  }
}
