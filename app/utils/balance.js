/**
 * balance.js
 *
 * This utility file provides functions for calculating and managing balance totals.
 */

/**
 * Calculates the total balance from an array of balance objects
 * @param {Array} balances - Array of balance objects, each with a balance property
 * @returns {number} - The sum of all balance values
 */
export default function calculateTotalBalance(balances) {
    let sum = 0.0

    if (!balances || !Array.isArray(balances) || balances.length === 0) {
        console.log('calculateTotalBalance: No balances to calculate');
        return 0.0
    }

    console.log(`calculateTotalBalance: Processing ${balances.length} balances`);

    for (let balance of balances) {
        if (balance && balance.balance) {
            const balanceValue = Number(balance.balance);
            console.log(`calculateTotalBalance: Adding ${balanceValue} from ${balance.category}`);
            sum += balanceValue;
        } else {
            console.log(`calculateTotalBalance: Skipping invalid balance:`, balance);
        }
    }

    console.log(`calculateTotalBalance: Final sum = ${sum}`);
    return sum;
}
