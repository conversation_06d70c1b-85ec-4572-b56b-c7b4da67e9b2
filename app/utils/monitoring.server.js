/**
 * Monitoring Service
 *
 * This service provides application monitoring, metrics collection,
 * and health check functionality for the Americans United Inc application.
 */

import { container } from '../lib/container/ServiceContainer.server.js';
import { formatDuration, formatFileSize } from './core/formatUtils.js';

/**
 * Application monitoring service
 */
export class MonitoringService {
  constructor(dependencies = {}) {
    this.prisma = dependencies.prisma || null; // Lazy load database
    this.metrics = new Map();
    this.healthChecks = new Map();
    this.startTime = Date.now();
  }

  /**
   * Get database connection (lazy loaded)
   * @returns {Promise<object>} - Prisma client
   */
  async getDatabase() {
    if (!this.prisma) {
      this.prisma = await container.resolve('database');
    }
    return this.prisma;
  }

  /**
   * Record a metric
   * @param {string} name - Metric name
   * @param {number} value - Metric value
   * @param {object} tags - Metric tags
   */
  recordMetric(name, value, tags = {}) {
    const metric = {
      name,
      value,
      tags,
      timestamp: Date.now(),
    };

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const metrics = this.metrics.get(name);
    metrics.push(metric);

    // Keep only last 1000 metrics per name
    if (metrics.length > 1000) {
      metrics.shift();
    }
  }

  /**
   * Increment a counter metric
   * @param {string} name - Counter name
   * @param {number} increment - Increment value (default: 1)
   * @param {object} tags - Metric tags
   */
  incrementCounter(name, increment = 1, tags = {}) {
    this.recordMetric(name, increment, { ...tags, type: 'counter' });
  }

  /**
   * Record a timing metric
   * @param {string} name - Timer name
   * @param {number} duration - Duration in milliseconds
   * @param {object} tags - Metric tags
   */
  recordTiming(name, duration, tags = {}) {
    this.recordMetric(name, duration, { ...tags, type: 'timing' });
  }

  /**
   * Record a gauge metric
   * @param {string} name - Gauge name
   * @param {number} value - Current value
   * @param {object} tags - Metric tags
   */
  recordGauge(name, value, tags = {}) {
    this.recordMetric(name, value, { ...tags, type: 'gauge' });
  }

  /**
   * Time a function execution
   * @param {string} name - Timer name
   * @param {Function} fn - Function to time
   * @param {object} tags - Metric tags
   * @returns {Promise<*>} - Function result
   */
  async timeFunction(name, fn, tags = {}) {
    const startTime = Date.now();
    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      this.recordTiming(name, duration, { ...tags, status: 'success' });
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordTiming(name, duration, { ...tags, status: 'error' });
      throw error;
    }
  }

  /**
   * Get metrics for a specific name
   * @param {string} name - Metric name
   * @param {number} limit - Maximum number of metrics to return
   * @returns {Array} - Array of metrics
   */
  getMetrics(name, limit = 100) {
    const metrics = this.metrics.get(name) || [];
    return metrics.slice(-limit);
  }

  /**
   * Get aggregated metrics
   * @param {string} name - Metric name
   * @param {number} timeWindow - Time window in milliseconds
   * @returns {object} - Aggregated metrics
   */
  getAggregatedMetrics(name, timeWindow = 300000) { // 5 minutes default
    const metrics = this.getMetrics(name, 1000);
    const cutoffTime = Date.now() - timeWindow;
    const recentMetrics = metrics.filter(m => m.timestamp >= cutoffTime);

    if (recentMetrics.length === 0) {
      return {
        count: 0,
        sum: 0,
        avg: 0,
        min: 0,
        max: 0,
        latest: null,
      };
    }

    const values = recentMetrics.map(m => m.value);
    const sum = values.reduce((a, b) => a + b, 0);

    return {
      count: recentMetrics.length,
      sum,
      avg: sum / recentMetrics.length,
      min: Math.min(...values),
      max: Math.max(...values),
      latest: recentMetrics[recentMetrics.length - 1],
    };
  }

  /**
   * Register a health check
   * @param {string} name - Health check name
   * @param {Function} checkFn - Health check function
   * @param {number} timeout - Timeout in milliseconds
   */
  registerHealthCheck(name, checkFn, timeout = 5000) {
    this.healthChecks.set(name, { checkFn, timeout });
  }

  /**
   * Run all health checks
   * @returns {Promise<object>} - Health check results
   */
  async runHealthChecks() {
    const results = {
      status: 'healthy',
      timestamp: new Date(),
      uptime: formatDuration(Date.now() - this.startTime),
      checks: {},
    };

    for (const [name, { checkFn, timeout }] of this.healthChecks) {
      try {
        const checkResult = await Promise.race([
          checkFn(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Health check timeout')), timeout)
          ),
        ]);

        results.checks[name] = {
          status: 'healthy',
          ...checkResult,
        };
      } catch (error) {
        results.checks[name] = {
          status: 'unhealthy',
          error: error.message,
        };
        results.status = 'unhealthy';
      }
    }

    return results;
  }

  /**
   * Get system metrics
   * @returns {object} - System metrics
   */
  getSystemMetrics() {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      memory: {
        rss: formatFileSize(memUsage.rss),
        heapTotal: formatFileSize(memUsage.heapTotal),
        heapUsed: formatFileSize(memUsage.heapUsed),
        external: formatFileSize(memUsage.external),
        arrayBuffers: formatFileSize(memUsage.arrayBuffers),
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      uptime: formatDuration(process.uptime() * 1000),
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
    };
  }

  /**
   * Get application metrics
   * @returns {Promise<object>} - Application metrics
   */
  async getApplicationMetrics() {
    try {
      const prisma = await this.getDatabase();
      const [
        totalShops,
        totalOrders,
        totalInvoiceBalance,
        totalShippingCosts,
        recentProcessedWebhooks,
        recentUnprocessable,
      ] = await Promise.all([
        prisma.shop.count(),
        prisma.order.count(),
        prisma.invoiceBalance.aggregate({
          _sum: { amount: true },
        }),
        prisma.shippingCost.aggregate({
          _sum: { cost: true },
        }),
        prisma.processedWebhook.count({
          where: {
            processedAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
            },
          },
        }),
        prisma.unprocessable.count({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
            },
          },
        }),
      ]);

      return {
        shops: {
          total: totalShops,
        },
        orders: {
          total: totalOrders,
        },
        invoiceBalance: {
          total: totalInvoiceBalance._sum.amount || 0,
        },
        shippingCosts: {
          total: totalShippingCosts._sum.cost || 0,
        },
        webhooks: {
          processedLast24h: recentProcessedWebhooks,
          failedLast24h: recentUnprocessable,
        },
      };
    } catch (error) {
      console.error('Failed to get application metrics:', error);
      return {
        error: 'Failed to retrieve application metrics',
      };
    }
  }

  /**
   * Get performance metrics
   * @returns {object} - Performance metrics
   */
  getPerformanceMetrics() {
    const performanceMetrics = {};

    // Get timing metrics
    const timingMetrics = [
      'order_processing_time',
      'reconciliation_time',
      'shopify_api_response_time',
      'shipstation_api_response_time',
      'database_query_time',
    ];

    timingMetrics.forEach(metric => {
      const aggregated = this.getAggregatedMetrics(metric);
      if (aggregated.count > 0) {
        performanceMetrics[metric] = {
          avgMs: Math.round(aggregated.avg),
          minMs: aggregated.min,
          maxMs: aggregated.max,
          count: aggregated.count,
        };
      }
    });

    return performanceMetrics;
  }

  /**
   * Get error metrics
   * @returns {object} - Error metrics
   */
  getErrorMetrics() {
    const errorMetrics = {};

    // Get error counter metrics
    const errorCounters = [
      'order_processing_errors',
      'reconciliation_errors',
      'shopify_api_errors',
      'shipstation_api_errors',
      'database_errors',
      'webhook_processing_errors',
    ];

    errorCounters.forEach(metric => {
      const aggregated = this.getAggregatedMetrics(metric);
      if (aggregated.count > 0) {
        errorMetrics[metric] = {
          total: aggregated.sum,
          rate: aggregated.count / 300, // Errors per second (5 min window)
        };
      }
    });

    return errorMetrics;
  }

  /**
   * Export metrics in Prometheus format
   * @returns {string} - Prometheus formatted metrics
   */
  exportPrometheusMetrics() {
    let output = '';

    for (const [name, metrics] of this.metrics) {
      if (metrics.length === 0) continue;

      const latest = metrics[metrics.length - 1];
      const metricName = name.replace(/[^a-zA-Z0-9_]/g, '_');

      output += `# HELP ${metricName} Application metric\n`;
      output += `# TYPE ${metricName} gauge\n`;
      output += `${metricName} ${latest.value}\n\n`;
    }

    return output;
  }

  /**
   * Clear old metrics
   * @param {number} maxAge - Maximum age in milliseconds
   */
  clearOldMetrics(maxAge = 3600000) { // 1 hour default
    const cutoffTime = Date.now() - maxAge;

    for (const [name, metrics] of this.metrics) {
      const filteredMetrics = metrics.filter(m => m.timestamp >= cutoffTime);
      this.metrics.set(name, filteredMetrics);
    }
  }

  /**
   * Get monitoring dashboard data
   * @returns {Promise<object>} - Dashboard data
   */
  async getDashboardData() {
    const [
      systemMetrics,
      applicationMetrics,
      performanceMetrics,
      errorMetrics,
      healthChecks,
    ] = await Promise.all([
      this.getSystemMetrics(),
      this.getApplicationMetrics(),
      this.getPerformanceMetrics(),
      this.getErrorMetrics(),
      this.runHealthChecks(),
    ]);

    return {
      timestamp: new Date(),
      system: systemMetrics,
      application: applicationMetrics,
      performance: performanceMetrics,
      errors: errorMetrics,
      health: healthChecks,
    };
  }
}

// Lazy singleton instance
let _monitoring = null;

/**
 * Get or create the monitoring service singleton
 * @returns {MonitoringService} - Monitoring service instance
 */
export function getMonitoring() {
  if (!_monitoring) {
    _monitoring = new MonitoringService();

    // Register default health checks
    _monitoring.registerHealthCheck('database', async () => {
      const prisma = await container.resolve('database');
      await prisma.$queryRaw`SELECT 1`;
      return { message: 'Database connection healthy' };
    });

    _monitoring.registerHealthCheck('memory', async () => {
      const memUsage = process.memoryUsage();
      const heapUsedMB = memUsage.heapUsed / 1024 / 1024;

      if (heapUsedMB > 512) { // 512MB threshold
        throw new Error(`High memory usage: ${heapUsedMB.toFixed(2)}MB`);
      }

      return {
        message: 'Memory usage normal',
        heapUsedMB: heapUsedMB.toFixed(2),
      };
    });
  }

  return _monitoring;
}

// Export for backward compatibility
export const monitoring = {
  get instance() {
    return getMonitoring();
  },
  runHealthChecks: () => getMonitoring().runHealthChecks(),
  registerHealthCheck: (name, check) => getMonitoring().registerHealthCheck(name, check),
  getSystemMetrics: () => getMonitoring().getSystemMetrics(),
  getApplicationMetrics: () => getMonitoring().getApplicationMetrics(),
  getDashboardData: () => getMonitoring().getDashboardData(),
};

// Export monitoring functions
export function recordMetric(name, value, tags) {
  getMonitoring().recordMetric(name, value, tags);
}

export function incrementCounter(name, increment, tags) {
  getMonitoring().incrementCounter(name, increment, tags);
}

export function recordTiming(name, duration, tags) {
  getMonitoring().recordTiming(name, duration, tags);
}

export function timeFunction(name, fn, tags) {
  return getMonitoring().timeFunction(name, fn, tags);
}
