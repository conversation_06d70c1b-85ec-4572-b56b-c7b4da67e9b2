/**
 * Database Health Check Utility
 *
 * This utility provides comprehensive database health monitoring,
 * connection testing, and performance metrics collection.
 */

import { container } from '../lib/container/ServiceContainer.server.js';
import { DatabaseError } from '../lib/errors/AppError.js';

export class DatabaseHealthChecker {
  constructor(prisma = null) {
    this.prisma = prisma || container.resolve('database');
    this.healthMetrics = {
      lastCheck: null,
      connectionStatus: 'unknown',
      queryPerformance: {},
      errorCount: 0,
      warningCount: 0
    };
  }

  /**
   * Perform comprehensive health check
   * @returns {Promise<object>} - Health check results
   */
  async performHealthCheck() {
    const startTime = Date.now();
    const results = {
      timestamp: new Date(),
      status: 'healthy',
      checks: {},
      metrics: {},
      errors: [],
      warnings: []
    };

    try {
      // Basic connection test
      results.checks.connection = await this.checkConnection();

      // Query performance test
      results.checks.queryPerformance = await this.checkQueryPerformance();

      // Table accessibility test
      results.checks.tableAccess = await this.checkTableAccess();

      // Index performance test
      results.checks.indexPerformance = await this.checkIndexPerformance();

      // Connection pool status
      results.checks.connectionPool = await this.checkConnectionPool();

      // Database size and statistics
      results.metrics = await this.collectMetrics();

      // Determine overall status
      results.status = this.determineOverallStatus(results.checks);

      // Update internal metrics
      this.updateHealthMetrics(results);

    } catch (error) {
      results.status = 'unhealthy';
      results.errors.push({
        type: 'health_check_failed',
        message: error.message,
        timestamp: new Date()
      });
    }

    results.duration = Date.now() - startTime;
    return results;
  }

  /**
   * Check basic database connection
   * @returns {Promise<object>} - Connection check result
   */
  async checkConnection() {
    const startTime = Date.now();

    try {
      await this.prisma.$queryRaw`SELECT 1 as test`;

      return {
        status: 'healthy',
        responseTime: Date.now() - startTime,
        message: 'Database connection successful'
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        message: `Connection failed: ${error.message}`,
        error: error.message
      };
    }
  }

  /**
   * Check query performance with sample queries
   * @returns {Promise<object>} - Query performance results
   */
  async checkQueryPerformance() {
    const queries = [
      {
        name: 'simple_select',
        query: () => this.prisma.session.findFirst({ select: { id: true } })
      },
      {
        name: 'count_sessions',
        query: () => this.prisma.session.count()
      },
      {
        name: 'recent_invoices',
        query: () => this.prisma.invoiceBalance.findMany({
          take: 10,
          orderBy: { id: 'desc' }
        })
      }
    ];

    const results = {};

    for (const { name, query } of queries) {
      const startTime = Date.now();
      try {
        await query();
        const responseTime = Date.now() - startTime;

        results[name] = {
          status: responseTime < 1000 ? 'healthy' : 'slow',
          responseTime,
          threshold: 1000
        };
      } catch (error) {
        results[name] = {
          status: 'failed',
          error: error.message,
          responseTime: Date.now() - startTime
        };
      }
    }

    return results;
  }

  /**
   * Check table accessibility
   * @returns {Promise<object>} - Table access results
   */
  async checkTableAccess() {
    const tables = [
      'Session',
      'InvoiceBalance',
      'ShippingCost',
      'ReconciliationJob',
      'ProcessedWebhook'
    ];

    // Map table names to Prisma model names
    const tableModelMap = {
      'Session': 'session',
      'InvoiceBalance': 'invoiceBalance',
      'ShippingCost': 'shippingCost',
      'ReconciliationJob': 'reconciliationJob',
      'ProcessedWebhook': 'processedWebhook'
    };

    const results = {};

    for (const table of tables) {
      const startTime = Date.now();
      try {
        const modelName = tableModelMap[table];
        const model = this.prisma[modelName];
        if (model) {
          await model.findFirst({ select: { id: true } });
          results[table] = {
            status: 'accessible',
            responseTime: Date.now() - startTime
          };
        } else {
          results[table] = {
            status: 'not_found',
            error: 'Model not found in Prisma client'
          };
        }
      } catch (error) {
        results[table] = {
          status: 'inaccessible',
          error: error.message,
          responseTime: Date.now() - startTime
        };
      }
    }

    return results;
  }

  /**
   * Check index performance
   * @returns {Promise<object>} - Index performance results
   */
  async checkIndexPerformance() {
    const indexTests = [
      {
        name: 'invoice_balance_shop_index',
        query: () => this.prisma.invoiceBalance.findMany({
          where: { shop: 'test-shop.myshopify.com' },
          take: 1
        })
      },
      {
        name: 'shipping_cost_shop_store_index',
        query: () => this.prisma.shippingCost.findMany({
          where: {
            shop: 'test-shop.myshopify.com',
            storeId: 'test-store'
          },
          take: 1
        })
      }
    ];

    const results = {};

    for (const { name, query } of indexTests) {
      const startTime = Date.now();
      try {
        await query();
        const responseTime = Date.now() - startTime;

        results[name] = {
          status: responseTime < 500 ? 'optimal' : 'slow',
          responseTime,
          threshold: 500
        };
      } catch (error) {
        results[name] = {
          status: 'failed',
          error: error.message
        };
      }
    }

    return results;
  }

  /**
   * Check connection pool status
   * @returns {Promise<object>} - Connection pool status
   */
  async checkConnectionPool() {
    try {
      // This is a simplified check - in a real implementation,
      // you might want to check actual pool metrics if available
      const startTime = Date.now();

      // Test multiple concurrent connections
      const promises = Array(5).fill().map(() =>
        this.prisma.$queryRaw`SELECT 1 as test`
      );

      await Promise.all(promises);

      return {
        status: 'healthy',
        responseTime: Date.now() - startTime,
        concurrentConnections: 5,
        message: 'Connection pool handling concurrent requests'
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        message: 'Connection pool issues detected'
      };
    }
  }

  /**
   * Collect database metrics
   * @returns {Promise<object>} - Database metrics
   */
  async collectMetrics() {
    try {
      const [
        sessionCount,
        invoiceBalanceCount,
        shippingCostCount,
        reconciliationJobCount,
        webhookCount
      ] = await Promise.all([
        this.prisma.session.count(),
        this.prisma.invoiceBalance.count(),
        this.prisma.shippingCost.count(),
        this.prisma.reconciliationJob.count(),
        this.prisma.processedWebhook.count()
      ]);

      // Get recent activity
      const recentJobs = await this.prisma.reconciliationJob.count({
        where: {
          startedAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        }
      });

      const recentWebhooks = await this.prisma.processedWebhook.count({
        where: {
          processedAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        }
      });

      return {
        tableCounts: {
          sessions: sessionCount,
          invoiceBalances: invoiceBalanceCount,
          shippingCosts: shippingCostCount,
          reconciliationJobs: reconciliationJobCount,
          processedWebhooks: webhookCount
        },
        recentActivity: {
          jobsLast24h: recentJobs,
          webhooksLast24h: recentWebhooks
        },
        totalRecords: sessionCount + invoiceBalanceCount + shippingCostCount +
                     reconciliationJobCount + webhookCount
      };
    } catch (error) {
      return {
        error: `Failed to collect metrics: ${error.message}`
      };
    }
  }

  /**
   * Determine overall health status
   * @param {object} checks - Individual check results
   * @returns {string} - Overall status
   */
  determineOverallStatus(checks) {
    // Flatten all check results to handle nested structures
    const flattenChecks = (obj) => {
      const results = [];
      for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'object' && value !== null) {
          if (value.status) {
            results.push(value);
          } else {
            // Recursively flatten nested objects (like queryPerformance)
            results.push(...flattenChecks(value));
          }
        }
      }
      return results;
    };

    const allChecks = flattenChecks(checks);

    // Check for any critical failures
    const hasCriticalFailure = allChecks.some(check => {
      return check.status === 'unhealthy' || check.status === 'failed' || check.status === 'inaccessible';
    });

    if (hasCriticalFailure) {
      return 'unhealthy';
    }

    // Check for performance issues
    const hasPerformanceIssues = allChecks.some(check => {
      return check.status === 'slow';
    });

    if (hasPerformanceIssues) {
      return 'degraded';
    }

    return 'healthy';
  }

  /**
   * Update internal health metrics
   * @param {object} results - Health check results
   */
  updateHealthMetrics(results) {
    this.healthMetrics.lastCheck = results.timestamp;
    this.healthMetrics.connectionStatus = results.status;

    if (results.checks.queryPerformance) {
      this.healthMetrics.queryPerformance = results.checks.queryPerformance;
    }

    this.healthMetrics.errorCount = results.errors.length;
    this.healthMetrics.warningCount = results.warnings.length;
  }

  /**
   * Get current health metrics
   * @returns {object} - Current health metrics
   */
  getHealthMetrics() {
    return { ...this.healthMetrics };
  }

  /**
   * Test database migration readiness
   * @returns {Promise<object>} - Migration readiness results
   */
  async testMigrationReadiness() {
    try {
      const results = {
        ready: true,
        checks: {},
        issues: []
      };

      // Check if all required tables exist
      const requiredTables = [
        'Session', 'SetupFlag', 'BalanceGroup', 'InvoiceBalance',
        'InvoiceTransaction', 'ShippingCost', 'ShippingTransaction',
        'ShipStationStoreMapping', 'ShipStationOrderTracking',
        'Price', 'Unprocessable', 'ProcessedWebhook',
        'ReconciliationJob', 'VariantFulfillmentService'
      ];

      // Map table names to Prisma model names
      const tableModelMap = {
        'Session': 'session',
        'SetupFlag': 'setupFlag',
        'BalanceGroup': 'balanceGroup',
        'InvoiceBalance': 'invoiceBalance',
        'InvoiceTransaction': 'invoiceTransaction',
        'ShippingCost': 'shippingCost',
        'ShippingTransaction': 'shippingTransaction',
        'ShipStationStoreMapping': 'shipStationStoreMapping',
        'ShipStationOrderTracking': 'shipStationOrderTracking',
        'Price': 'price',
        'Unprocessable': 'unprocessable',
        'ProcessedWebhook': 'processedWebhook',
        'ReconciliationJob': 'reconciliationJob',
        'VariantFulfillmentService': 'variantFulfillmentService'
      };

      for (const table of requiredTables) {
        try {
          const modelName = tableModelMap[table];
          const model = this.prisma[modelName];
          if (model) {
            await model.findFirst({ select: { id: true } });
            results.checks[table] = 'exists';
          } else {
            results.checks[table] = 'missing';
            results.issues.push(`Table ${table} not found`);
            results.ready = false;
          }
        } catch (error) {
          results.checks[table] = 'error';
          results.issues.push(`Error accessing table ${table}: ${error.message}`);
          results.ready = false;
        }
      }

      return results;
    } catch (error) {
      return {
        ready: false,
        error: error.message,
        checks: {},
        issues: [`Migration readiness check failed: ${error.message}`]
      };
    }
  }

  /**
   * Quick health check for monitoring
   * @returns {Promise<object>} - Quick health status
   */
  async quickHealthCheck() {
    try {
      const startTime = Date.now();
      await this.prisma.$queryRaw`SELECT 1 as test`;

      return {
        status: 'healthy',
        responseTime: Date.now() - startTime,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date()
      };
    }
  }
}

/**
 * Create a database health checker instance
 * @param {object} prisma - Optional Prisma client instance
 * @returns {DatabaseHealthChecker} - Health checker instance
 */
export function createHealthChecker(prisma = null) {
  return new DatabaseHealthChecker(prisma);
}

/**
 * Perform a quick database health check
 * @param {object} prisma - Optional Prisma client instance
 * @returns {Promise<object>} - Quick health check result
 */
export async function quickHealthCheck(prisma = null) {
  const checker = new DatabaseHealthChecker(prisma);
  return await checker.quickHealthCheck();
}

/**
 * Perform a comprehensive database health check
 * @param {object} prisma - Optional Prisma client instance
 * @returns {Promise<object>} - Comprehensive health check result
 */
export async function comprehensiveHealthCheck(prisma = null) {
  const checker = new DatabaseHealthChecker(prisma);
  return await checker.performHealthCheck();
}
