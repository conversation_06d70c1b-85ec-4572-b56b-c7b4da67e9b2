/**
 * json-response.js
 *
 * This utility file provides functions for creating standardized JSON responses.
 * It offers a modern alternative to the deprecated json function from @remix-run/node.
 */

/**
 * A utility function to create JSON responses in a way that's compatible with CommonJS modules
 * This avoids using the deprecated json function from @remix-run/node
 *
 * @param {any} data - The data to be serialized as JSON
 * @param {object} options - Response options
 * @param {number} options.status - HTTP status code (default: 200)
 * @param {object} options.headers - HTTP headers
 * @returns {Response} - A Response object with JSON content
 */
export function jsonResponse(data, options = {}) {
  const { status = 200, headers = {}, ...rest } = options;

  // Create headers object
  const responseHeaders = new Headers(headers);
  responseHeaders.set('Content-Type', 'application/json; charset=utf-8');

  // Create response init object
  const responseInit = {
    status,
    headers: responseHeaders,
    ...rest
  };

  // Create and return response
  return new Response(JSON.stringify(data), responseInit);
}
