/**
 * Simple batch processing utility for fulfillment service
 */

/**
 * Process items in batches with concurrency control
 * @param {Array} items - Items to process
 * @param {Function} processor - Function to process each item
 * @param {object} options - Processing options
 * @returns {Promise<Array>} - Array of processing results
 */
export async function processBatches(items, processor, options = {}) {
  const {
    batchSize = 50,
    concurrency = 2,
    delayBetweenBatches = 1000,
    onProgress = null
  } = options;

  if (!Array.isArray(items)) {
    throw new Error('Items must be an array');
  }

  if (typeof processor !== 'function') {
    throw new Error('Processor must be a function');
  }

  const results = [];
  const totalItems = items.length;

  // Split items into batches
  const batches = [];
  for (let i = 0; i < items.length; i += batchSize) {
    batches.push(items.slice(i, i + batchSize));
  }

  let processedCount = 0;

  // Process each batch
  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex];
    
    try {
      if (onProgress) {
        onProgress({
          batchIndex: batchIndex + 1,
          totalBatches: batches.length,
          processedItems: processedCount,
          totalItems: totalItems,
          currentBatchSize: batch.length
        });
      }

      // Process batch items with concurrency control
      const batchResults = await processBatchWithConcurrency(batch, processor, concurrency);
      results.push(...batchResults);
      processedCount += batch.length;

      // Add delay between batches if specified
      if (delayBetweenBatches > 0 && batchIndex < batches.length - 1) {
        await sleep(delayBetweenBatches);
      }

    } catch (error) {
      console.error(`Error processing batch ${batchIndex + 1}:`, error);
      // Add error results for this batch
      const errorResults = batch.map(item => ({
        success: false,
        item,
        error: error.message
      }));
      results.push(...errorResults);
      processedCount += batch.length;
    }
  }

  return results;
}

/**
 * Process a single batch with concurrency control
 * @param {Array} batch - Batch items to process
 * @param {Function} processor - Processing function
 * @param {number} concurrency - Number of concurrent operations
 * @returns {Promise<Array>} - Batch results
 */
async function processBatchWithConcurrency(batch, processor, concurrency) {
  const results = [];
  
  // Process items in chunks based on concurrency
  for (let i = 0; i < batch.length; i += concurrency) {
    const chunk = batch.slice(i, i + concurrency);
    
    // Process chunk items concurrently
    const chunkPromises = chunk.map(async (item) => {
      try {
        const result = await processor(item);
        return {
          success: true,
          item,
          result
        };
      } catch (error) {
        return {
          success: false,
          item,
          error: error.message
        };
      }
    });

    const chunkResults = await Promise.all(chunkPromises);
    results.push(...chunkResults);
  }

  return results;
}

/**
 * Sleep utility function
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise} - Promise that resolves after the specified time
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
