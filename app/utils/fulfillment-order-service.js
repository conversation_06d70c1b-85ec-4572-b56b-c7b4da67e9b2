/**
 * Utility functions for checking fulfillment service types
 */

import { processBatches } from './batch-processing';
import * as memoryCache from './memory-cache';

// Cache expiration time for fulfillment services (24 hours - fulfillment services rarely change)
const FULFILLMENT_CACHE_EXPIRATION = 24 * 60 * 60 * 1000;

// Ultra-conservative rate limiting configuration to avoid 429 errors
const RATE_LIMIT_CONFIG = {
  maxConcurrency: 1, // Single request at a time to avoid rate limits
  delayBetweenRequests: 500, // 500ms delay between requests (2 requests/second max)
  retryDelay: 3000, // 3 second delay on rate limit
  maxRetries: 3,
  batchDelay: 1000 // 1 second delay between batches
};

/**
 * Checks if a variant has a manual fulfillment service
 * @param {object} admin - The admin API client
 * @param {string} variantId - The variant ID to check
 * @param {string} orderId - The order ID (optional)
 * @returns {Promise<{isManual: boolean, serviceName: string}>} - Object with isManual flag and the service name
 */
export async function hasManualFulfillmentService(admin, variantId, orderId) {
  // Check if admin is available
  if (!admin) {
    console.error('[hasManualFulfillmentService] Admin client is not available');
    return { isManual: true, serviceName: 'unknown' }; // Default to manual if admin is not available
  }

  try {
    // Check if we have a product ID instead of a variant ID
    if (typeof variantId === 'string' && variantId.includes('/Product/')) {
      console.error('Cannot check fulfillment service with a product ID instead of a variant ID');
      return { isManual: true, serviceName: 'unknown' }; // Default to manual if we have a product ID
    }

    // Make sure we have the correct format for the variant ID
    // If it's not a GID, convert it to one
    let formattedVariantId = variantId;
    if (typeof variantId === 'string' && !variantId.startsWith('gid://')) {
      formattedVariantId = `gid://shopify/ProductVariant/${variantId}`;
    }

    // Extract the numeric ID from the variant ID
    const variantIdNumeric = formattedVariantId.split('/').pop();

    // Check in-memory cache first
    const cacheKey = memoryCache.getVariantFulfillmentKey(variantIdNumeric);
    const cachedResult = memoryCache.get(cacheKey);

    if (cachedResult) {
      return cachedResult;
    }

    // Check if admin.rest and admin.rest.get exist
    if (!admin.rest || typeof admin.rest.get !== 'function') {
      console.error('REST API is not available');
      return { isManual: true, serviceName: 'unknown' }; // Default to manual if we can't check the fulfillment service
    }

    // Make a direct REST API call to get the fulfillment service
    try {
      // Ensure we have a valid session with shop information
      const session = admin.session || { shop: admin.shop };

      // Make sure we have a shop in the session
      if (!session.shop && admin.shop) {
        session.shop = admin.shop;
      }

      // Make the REST API call with the session that has shop information
      const response = await admin.rest.get({
        path: `variants/${variantIdNumeric}`,
        session: session,
        query: {
          fields: 'id,fulfillment_service,sku,title'  // Only request the fields we need
        }
      });

      // Process the response
      if (response && response.status === 200) {
        const data = await response.json();

        if (data && data.variant) {
          const serviceName = data.variant.fulfillment_service || 'manual';
          const isManual = !data.variant.fulfillment_service || data.variant.fulfillment_service === 'manual';

          const result = { isManual, serviceName };

          // Store in memory cache
          memoryCache.set(cacheKey, result, FULFILLMENT_CACHE_EXPIRATION);

          return result;
        }
      }

      // If we couldn't get the data, default to manual fulfillment
      const defaultResult = { isManual: true, serviceName: 'manual' };
      memoryCache.set(cacheKey, defaultResult, FULFILLMENT_CACHE_EXPIRATION);
      return defaultResult;
    } catch (error) {
      // Check for rate limit errors (429)
      if (error && error.status === 429) {
        // Wait for a moment before retrying
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Retry the operation
        return hasManualFulfillmentService(admin, variantId, orderId);
      }

      // For non-rate-limit errors, default to manual fulfillment
      const defaultResult = { isManual: true, serviceName: 'manual' };
      memoryCache.set(cacheKey, defaultResult, FULFILLMENT_CACHE_EXPIRATION / 2); // Shorter cache time for errors
      return defaultResult;
    }
  } catch (error) {
    // Default to manual fulfillment for general errors
    return { isManual: true, serviceName: 'manual' };
  }
}

/**
 * Optimized batch check for fulfillment services with aggressive caching and rate limit handling
 * @param {object} admin - The admin API client
 * @param {Array<string>} variantIds - Array of variant IDs to check
 * @param {string} orderId - The order ID (optional)
 * @param {object} options - Options for batch processing
 * @returns {Promise<Object>} - Map of variant IDs to fulfillment service results
 */
export async function batchCheckManualFulfillmentServices(admin, variantIds, orderId = null, options = {}) {
  const startTime = Date.now();

  // Check if admin is available
  if (!admin) {
    console.error('[batchCheckManualFulfillmentServices] Admin client is not available');
    return variantIds.reduce((result, variantId) => {
      result[variantId] = { isManual: true, serviceName: 'unknown' };
      return result;
    }, {});
  }

  // Filter out product IDs and deduplicate
  const uniqueValidVariantIds = [...new Set(variantIds.filter(variantId =>
    variantId && !(typeof variantId === 'string' && variantId.includes('/Product/'))
  ))];

  if (uniqueValidVariantIds.length === 0) {
    return {};
  }

  console.log(`[batchCheckManualFulfillmentServices] Processing ${uniqueValidVariantIds.length} unique variants (deduplicated from ${variantIds.length})`);

  // Check cache first - this is the biggest optimization
  const results = {};
  const uncachedVariantIds = [];

  for (const variantId of uniqueValidVariantIds) {
    const numericId = variantId.startsWith('gid://') ? variantId.split('/').pop() : variantId;
    const cacheKey = memoryCache.getVariantFulfillmentKey(numericId);
    const cachedResult = memoryCache.get(cacheKey);

    if (cachedResult) {
      results[variantId] = cachedResult;
    } else {
      uncachedVariantIds.push(variantId);
    }
  }

  const cacheHitRate = ((uniqueValidVariantIds.length - uncachedVariantIds.length) / uniqueValidVariantIds.length * 100).toFixed(1);
  console.log(`[batchCheckManualFulfillmentServices] Cache hit rate: ${cacheHitRate}% (${uncachedVariantIds.length} API calls needed)`);

  // If all variants were cached, return immediately
  if (uncachedVariantIds.length === 0) {
    console.log(`[batchCheckManualFulfillmentServices] All variants cached, completed in ${Date.now() - startTime}ms`);
    return results;
  }

  // Process uncached variants with ultra-conservative settings
  const {
    batchSize = 5, // Very small batches to respect rate limits
    concurrency = RATE_LIMIT_CONFIG.maxConcurrency,
    delayBetweenBatches = RATE_LIMIT_CONFIG.batchDelay, // Use configured batch delay
    onProgress = null
  } = options;

  try {
    let processedCount = 0;
    const totalUncached = uncachedVariantIds.length;

    // Process variants sequentially to avoid rate limits completely
    for (let i = 0; i < uncachedVariantIds.length; i++) {
      const variantId = uncachedVariantIds[i];

      try {
        const result = await hasManualFulfillmentServiceWithRetry(admin, variantId, orderId);
        results[variantId] = result;
        processedCount++;
      } catch (error) {
        console.error(`Error checking fulfillment service for variant ${variantId}: ${error.message}`);
        const defaultResult = { isManual: true, serviceName: 'manual' };
        results[variantId] = defaultResult;

        // Cache the default result
        const numericId = variantId.startsWith('gid://') ? variantId.split('/').pop() : variantId;
        const cacheKey = memoryCache.getVariantFulfillmentKey(numericId);
        memoryCache.set(cacheKey, defaultResult, FULFILLMENT_CACHE_EXPIRATION / 4);

        processedCount++;
      }

      // Progress reporting every 10 items
      if (processedCount % 10 === 0 || processedCount === totalUncached) {
        if (onProgress) {
          onProgress({
            processed: processedCount,
            total: totalUncached,
            percentage: Math.round((processedCount / totalUncached) * 100)
          });
        }

        console.log(`[batchCheckManualFulfillmentServices] Processed ${processedCount}/${totalUncached} variants (${Math.round((processedCount / totalUncached) * 100)}%)`);
      }

      // Delay between each request to respect rate limits
      if (i < uncachedVariantIds.length - 1) {
        await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_CONFIG.delayBetweenRequests));
      }
    }

    const totalTime = Date.now() - startTime;
    const avgTimePerVariant = totalTime / uniqueValidVariantIds.length;
    console.log(`[batchCheckManualFulfillmentServices] Completed in ${totalTime}ms (${avgTimePerVariant.toFixed(1)}ms per variant)`);

    return results;

  } catch (error) {
    console.error(`Error in batch checking fulfillment services: ${error.message}`);

    // Return default values for all uncached variants
    for (const variantId of uncachedVariantIds) {
      results[variantId] = { isManual: true, serviceName: 'manual' };
    }

    return results;
  }
}

/**
 * Enhanced version of hasManualFulfillmentService with retry logic
 */
async function hasManualFulfillmentServiceWithRetry(admin, variantId, orderId, retryCount = 0) {
  try {
    return await hasManualFulfillmentService(admin, variantId, orderId);
  } catch (error) {
    // Handle rate limiting with exponential backoff
    if (error.status === 429 && retryCount < RATE_LIMIT_CONFIG.maxRetries) {
      const delay = RATE_LIMIT_CONFIG.retryDelay * Math.pow(2, retryCount);
      console.log(`[hasManualFulfillmentServiceWithRetry] Rate limited, retrying in ${delay}ms (attempt ${retryCount + 1})`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return hasManualFulfillmentServiceWithRetry(admin, variantId, orderId, retryCount + 1);
    }
    throw error;
  }
}
