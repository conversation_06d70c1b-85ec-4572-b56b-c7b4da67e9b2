/**
 * productGroups.js
 *
 * This utility file provides functions and constants for categorizing products into groups.
 * It helps organize products for reporting, display, and analysis purposes.
 */

// Product group definitions
export const PRODUCT_GROUPS = {
  SHIRTS: "Shirts",
  OUTERWEAR: "Outerwear",
  BOTTOMS: "Bottoms",
  HEADWEAR: "Headwear",
  STICKERS: "Stickers",
  CANVASES: "Canvases",
  DRINKWARE: "Drinkware",
  OTHER: "Other"
};

// Define the display order for groups
export const GROUP_DISPLAY_ORDER = [
  PRODUCT_GROUPS.SHIRTS,
  PRODUCT_GROUPS.OUTERWEAR,
  PRODUCT_GROUPS.BOTTOMS,
  PRODUCT_GROUPS.HEADWEAR,
  PRODUCT_GROUPS.STICKERS,
  PRODUCT_GROUPS.CANVASES,
  PRODUCT_GROUPS.DRINKWARE,
  PRODUCT_GROUPS.OTHER
];

/**
 * Maps a product category to its corresponding product group
 * @param {string} category - The product category to map
 * @returns {string} - The product group the category belongs to
 */
export function mapCategoryToGroup(category) {
  const lowerCategory = category.toLowerCase();

  // Special case for items that might be miscategorized
  if (
    lowerCategory.includes("hoodie") ||
    lowerCategory.includes("sweatshirt") ||
    lowerCategory.includes("sweater") ||
    lowerCategory.includes("crewneck") ||
    lowerCategory.includes("woobie")
  ) {
    return PRODUCT_GROUPS.OUTERWEAR;
  }

  // Outerwear group - check this first to prioritize over shirts
  if (
    lowerCategory.includes("jacket") ||
    lowerCategory.includes("outerwear") ||
    lowerCategory.includes("carhartt") ||
    lowerCategory.includes("fleece") ||
    lowerCategory.includes("flannel") ||
    lowerCategory.includes("windbreaker") ||
    lowerCategory.includes("anorak")
  ) {
    return PRODUCT_GROUPS.OUTERWEAR;
  }

  // Shirts group
  if (
    lowerCategory.includes("tee") ||
    (lowerCategory.includes("shirt") && !lowerCategory.includes("sweatshirt")) ||
    lowerCategory.includes("tank") ||
    (lowerCategory.includes("long sleeve") && !lowerCategory.includes("sweatshirt")) ||
    lowerCategory.includes("performance") ||
    lowerCategory.includes("polo")
  ) {
    return PRODUCT_GROUPS.SHIRTS;
  }

  // Bottoms group
  if (
    lowerCategory.includes("pants") ||
    lowerCategory.includes("sweatpants") ||
    lowerCategory.includes("silkies") ||
    lowerCategory.includes("shorts")
  ) {
    return PRODUCT_GROUPS.BOTTOMS;
  }

  // Canvases group
  if (
    lowerCategory.includes("canvas") ||
    lowerCategory.includes("print")
  ) {
    return PRODUCT_GROUPS.CANVASES;
  }

  // Stickers group
  if (lowerCategory.includes("sticker")) {
    return PRODUCT_GROUPS.STICKERS;
  }

  // Headwear group
  if (
    lowerCategory.includes("hat") ||
    lowerCategory.includes("beanie") ||
    lowerCategory.includes("cap") ||
    lowerCategory.includes("headwear") ||
    lowerCategory.includes("tk")
  ) {
    return PRODUCT_GROUPS.HEADWEAR;
  }

  // Drinkware group
  if (
    lowerCategory.includes("tumbler") ||
    lowerCategory.includes("water bottle") ||
    lowerCategory.includes("mug") ||
    lowerCategory.includes("cup") ||
    lowerCategory.includes("wine glass") ||
    lowerCategory.includes("pint glass") ||
    lowerCategory.includes("low ball") ||
    lowerCategory.includes("lowball") ||
    lowerCategory.includes("decanter") ||
    lowerCategory.includes("flask") ||
    lowerCategory.includes("rocks glass") ||
    lowerCategory.includes("glass")
  ) {
    return PRODUCT_GROUPS.DRINKWARE;
  }

  // Default to Other
  return PRODUCT_GROUPS.OTHER;
}

/**
 * Groups balance items by their product group for reporting and display
 * @param {Array} balances - Array of balance objects with category, quantity, and balance properties
 * @returns {Array} - Array of grouped balance objects, each containing category, quantity, balance, and items
 */
export function groupBalanceItems(balances) {
  if (!balances || balances.length === 0) {
    return [];
  }

  // Create a map to hold grouped items
  const groupedItems = {};

  // Initialize groups
  Object.values(PRODUCT_GROUPS).forEach(group => {
    groupedItems[group] = {
      category: group,
      quantity: 0,
      balance: 0,
      items: []
    };
  });

  // Group items (excluding Fulfillment Costs)
  balances.forEach(balance => {
    // Skip Fulfillment Costs as they will be displayed with Shipping Costs
    if (balance.category === 'Fulfillment Costs') {
      console.log('Skipping Fulfillment Costs item as it will be displayed with Shipping Costs');
      return;
    }

    const group = mapCategoryToGroup(balance.category);
    console.log('Processing item:', {
      category: balance.category,
      group: group,
      quantity: balance.quantity
    });
    groupedItems[group].quantity += balance.quantity;
    groupedItems[group].balance += Number(balance.balance);
    groupedItems[group].items.push(balance);
  });

  // Debug log before filtering
  console.log('Before filtering:', Object.values(groupedItems).map(g => ({
    category: g.category,
    quantity: g.quantity
  })));

  // Convert to array, filter out empty groups, and sort by defined order
  return Object.values(groupedItems)
    .filter(group => group.quantity > 0)
    .sort((a, b) => {
      const orderA = GROUP_DISPLAY_ORDER.indexOf(a.category);
      const orderB = GROUP_DISPLAY_ORDER.indexOf(b.category);
      return orderA - orderB;
    });
}
