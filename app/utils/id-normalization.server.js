/**
 * ID Normalization Utilities
 * 
 * This module provides consistent ID handling across the application,
 * ensuring proper normalization and validation of various ID formats.
 */

import { ValidationError } from '../lib/errors/AppError.js';

/**
 * Normalize Shopify order ID
 * @param {string|number} orderId - Raw order ID
 * @returns {string} - Normalized order ID
 */
export function normalizeOrderId(orderId) {
  if (!orderId) {
    throw new ValidationError('Order ID is required');
  }

  // Convert to string and remove any non-numeric characters except hyphens
  const normalized = String(orderId).replace(/[^0-9-]/g, '');
  
  if (!normalized) {
    throw new ValidationError('Invalid order ID format');
  }

  return normalized;
}

/**
 * Normalize Shopify product ID
 * @param {string|number} productId - Raw product ID
 * @returns {string} - Normalized product ID
 */
export function normalizeProductId(productId) {
  if (!productId) {
    throw new ValidationError('Product ID is required');
  }

  // Convert to string and ensure it's numeric
  const normalized = String(productId).replace(/[^0-9]/g, '');
  
  if (!normalized) {
    throw new ValidationError('Invalid product ID format');
  }

  return normalized;
}

/**
 * Normalize Shopify variant ID
 * @param {string|number} variantId - Raw variant ID
 * @returns {string} - Normalized variant ID
 */
export function normalizeVariantId(variantId) {
  if (!variantId) {
    throw new ValidationError('Variant ID is required');
  }

  // Convert to string and ensure it's numeric
  const normalized = String(variantId).replace(/[^0-9]/g, '');
  
  if (!normalized) {
    throw new ValidationError('Invalid variant ID format');
  }

  return normalized;
}

/**
 * Normalize ShipStation order ID
 * @param {string|number} shipstationOrderId - Raw ShipStation order ID
 * @returns {string} - Normalized ShipStation order ID
 */
export function normalizeShipStationOrderId(shipstationOrderId) {
  if (!shipstationOrderId) {
    throw new ValidationError('ShipStation order ID is required');
  }

  // Convert to string and remove any non-alphanumeric characters except hyphens
  const normalized = String(shipstationOrderId).replace(/[^A-Za-z0-9-]/g, '');
  
  if (!normalized) {
    throw new ValidationError('Invalid ShipStation order ID format');
  }

  return normalized;
}

/**
 * Normalize ShipStation shipment ID
 * @param {string|number} shipmentId - Raw shipment ID
 * @returns {string} - Normalized shipment ID
 */
export function normalizeShipmentId(shipmentId) {
  if (!shipmentId) {
    throw new ValidationError('Shipment ID is required');
  }

  // Convert to string and ensure it's numeric
  const normalized = String(shipmentId).replace(/[^0-9]/g, '');
  
  if (!normalized) {
    throw new ValidationError('Invalid shipment ID format');
  }

  return normalized;
}

/**
 * Normalize SKU
 * @param {string} sku - Raw SKU
 * @returns {string} - Normalized SKU
 */
export function normalizeSKU(sku) {
  if (!sku) {
    throw new ValidationError('SKU is required');
  }

  // Convert to string, trim whitespace, and convert to uppercase
  const normalized = String(sku).trim().toUpperCase();
  
  if (!normalized) {
    throw new ValidationError('Invalid SKU format');
  }

  // Validate SKU format (alphanumeric with allowed special characters)
  const skuRegex = /^[A-Z0-9._-]+$/;
  if (!skuRegex.test(normalized)) {
    throw new ValidationError('SKU contains invalid characters');
  }

  return normalized;
}

/**
 * Normalize shop domain
 * @param {string} shopDomain - Raw shop domain
 * @returns {string} - Normalized shop domain
 */
export function normalizeShopDomain(shopDomain) {
  if (!shopDomain) {
    throw new ValidationError('Shop domain is required');
  }

  // Convert to lowercase and trim
  let normalized = String(shopDomain).toLowerCase().trim();
  
  // Ensure it ends with .myshopify.com
  if (!normalized.endsWith('.myshopify.com')) {
    if (normalized.includes('.')) {
      throw new ValidationError('Invalid shop domain format');
    }
    normalized = `${normalized}.myshopify.com`;
  }

  // Validate domain format
  const domainRegex = /^[a-z0-9-]+\.myshopify\.com$/;
  if (!domainRegex.test(normalized)) {
    throw new ValidationError('Invalid shop domain format');
  }

  return normalized;
}

/**
 * Extract order ID from various formats
 * @param {string} input - Input string that may contain order ID
 * @returns {string|null} - Extracted order ID or null if not found
 */
export function extractOrderId(input) {
  if (!input) {
    return null;
  }

  const inputStr = String(input);
  
  // Try to extract numeric order ID
  const numericMatch = inputStr.match(/\d+/);
  if (numericMatch) {
    return numericMatch[0];
  }

  return null;
}

/**
 * Generate consistent hash for ID combinations
 * @param {Array<string>} ids - Array of IDs to hash
 * @returns {string} - Generated hash
 */
export function generateIdHash(ids) {
  if (!Array.isArray(ids) || ids.length === 0) {
    throw new ValidationError('IDs array is required');
  }

  // Sort IDs to ensure consistent hash regardless of order
  const sortedIds = ids.map(id => String(id)).sort();
  const combined = sortedIds.join('|');
  
  // Simple hash function (for production, consider using crypto)
  let hash = 0;
  for (let i = 0; i < combined.length; i++) {
    const char = combined.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return Math.abs(hash).toString(36);
}

/**
 * Validate and normalize webhook ID
 * @param {string} webhookId - Raw webhook ID
 * @returns {string} - Normalized webhook ID
 */
export function normalizeWebhookId(webhookId) {
  if (!webhookId) {
    throw new ValidationError('Webhook ID is required');
  }

  // Convert to string and remove any non-alphanumeric characters except hyphens and underscores
  const normalized = String(webhookId).replace(/[^A-Za-z0-9-_]/g, '');
  
  if (!normalized) {
    throw new ValidationError('Invalid webhook ID format');
  }

  return normalized;
}

/**
 * Create composite ID from multiple components
 * @param {Array<string>} components - ID components
 * @param {string} separator - Separator character (default: '-')
 * @returns {string} - Composite ID
 */
export function createCompositeId(components, separator = '-') {
  if (!Array.isArray(components) || components.length === 0) {
    throw new ValidationError('ID components array is required');
  }

  const normalizedComponents = components.map(component => {
    if (!component) {
      throw new ValidationError('All ID components must be non-empty');
    }
    return String(component).trim();
  });

  return normalizedComponents.join(separator);
}

/**
 * Parse composite ID into components
 * @param {string} compositeId - Composite ID to parse
 * @param {string} separator - Separator character (default: '-')
 * @returns {Array<string>} - ID components
 */
export function parseCompositeId(compositeId, separator = '-') {
  if (!compositeId) {
    throw new ValidationError('Composite ID is required');
  }

  const components = String(compositeId).split(separator);
  
  if (components.length === 0 || components.some(component => !component.trim())) {
    throw new ValidationError('Invalid composite ID format');
  }

  return components.map(component => component.trim());
}

/**
 * Normalize currency amount to cents
 * @param {number|string} amount - Amount in dollars
 * @returns {number} - Amount in cents
 */
export function normalizeCurrencyToCents(amount) {
  if (amount === null || amount === undefined || amount === '') {
    return 0;
  }

  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  if (isNaN(numericAmount)) {
    throw new ValidationError('Invalid currency amount');
  }

  // Convert to cents and round to avoid floating point issues
  return Math.round(numericAmount * 100);
}

/**
 * Normalize currency amount from cents to dollars
 * @param {number} cents - Amount in cents
 * @returns {number} - Amount in dollars
 */
export function normalizeCurrencyFromCents(cents) {
  if (typeof cents !== 'number' || isNaN(cents)) {
    throw new ValidationError('Invalid cents amount');
  }

  return cents / 100;
}

/**
 * Normalize date to ISO string
 * @param {Date|string|number} date - Date to normalize
 * @returns {string} - ISO date string
 */
export function normalizeDateToISO(date) {
  if (!date) {
    throw new ValidationError('Date is required');
  }

  let dateObj;
  
  if (date instanceof Date) {
    dateObj = date;
  } else if (typeof date === 'string' || typeof date === 'number') {
    dateObj = new Date(date);
  } else {
    throw new ValidationError('Invalid date format');
  }

  if (isNaN(dateObj.getTime())) {
    throw new ValidationError('Invalid date value');
  }

  return dateObj.toISOString();
}

/**
 * Normalize boolean value
 * @param {*} value - Value to normalize
 * @returns {boolean} - Normalized boolean
 */
export function normalizeBoolean(value) {
  if (typeof value === 'boolean') {
    return value;
  }

  if (typeof value === 'string') {
    const lowerValue = value.toLowerCase().trim();
    return ['true', '1', 'yes', 'on'].includes(lowerValue);
  }

  if (typeof value === 'number') {
    return value !== 0;
  }

  return Boolean(value);
}

/**
 * Batch normalize IDs
 * @param {Array} items - Items with IDs to normalize
 * @param {string} idField - Field name containing the ID
 * @param {Function} normalizer - Normalization function
 * @returns {Array} - Items with normalized IDs
 */
export function batchNormalizeIds(items, idField, normalizer) {
  if (!Array.isArray(items)) {
    throw new ValidationError('Items must be an array');
  }

  if (!idField || typeof idField !== 'string') {
    throw new ValidationError('ID field name is required');
  }

  if (!normalizer || typeof normalizer !== 'function') {
    throw new ValidationError('Normalizer function is required');
  }

  return items.map((item, index) => {
    try {
      return {
        ...item,
        [idField]: normalizer(item[idField]),
      };
    } catch (error) {
      throw new ValidationError(
        `Failed to normalize ID at index ${index}: ${error.message}`
      );
    }
  });
}
