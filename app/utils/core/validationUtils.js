/**
 * Validation Helper Utilities
 * 
 * This module provides validation helper functions that complement
 * the Zod schemas for runtime validation and data sanitization.
 */

/**
 * Validate email address format
 * @param {string} email - Email address to validate
 * @returns {boolean} - True if email is valid
 */
export function isValidEmail(email) {
  if (!email || typeof email !== 'string') {
    return false;
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate phone number format
 * @param {string} phone - Phone number to validate
 * @param {string} format - Format type ('us', 'international')
 * @returns {boolean} - True if phone is valid
 */
export function isValidPhone(phone, format = 'us') {
  if (!phone || typeof phone !== 'string') {
    return false;
  }

  const digits = phone.replace(/\D/g, '');

  if (format === 'us') {
    return digits.length === 10 || (digits.length === 11 && digits[0] === '1');
  }

  // International format - basic validation
  return digits.length >= 7 && digits.length <= 15;
}

/**
 * Validate URL format
 * @param {string} url - URL to validate
 * @param {boolean} requireHttps - Require HTTPS protocol
 * @returns {boolean} - True if URL is valid
 */
export function isValidUrl(url, requireHttps = false) {
  if (!url || typeof url !== 'string') {
    return false;
  }

  try {
    const urlObj = new URL(url);
    
    if (requireHttps && urlObj.protocol !== 'https:') {
      return false;
    }

    return ['http:', 'https:'].includes(urlObj.protocol);
  } catch {
    return false;
  }
}

/**
 * Validate Shopify domain format
 * @param {string} domain - Domain to validate
 * @returns {boolean} - True if domain is valid Shopify domain
 */
export function isValidShopifyDomain(domain) {
  if (!domain || typeof domain !== 'string') {
    return false;
  }

  const shopifyDomainRegex = /^[a-zA-Z0-9-]+\.myshopify\.com$/;
  return shopifyDomainRegex.test(domain);
}

/**
 * Validate SKU format
 * @param {string} sku - SKU to validate
 * @returns {boolean} - True if SKU is valid
 */
export function isValidSKU(sku) {
  if (!sku || typeof sku !== 'string') {
    return false;
  }

  // SKU should be alphanumeric with hyphens, underscores, and periods
  const skuRegex = /^[A-Za-z0-9._-]+$/;
  return skuRegex.test(sku) && sku.length <= 100;
}

/**
 * Validate currency amount
 * @param {number} amount - Amount to validate
 * @param {number} minAmount - Minimum allowed amount
 * @param {number} maxAmount - Maximum allowed amount
 * @returns {boolean} - True if amount is valid
 */
export function isValidCurrencyAmount(amount, minAmount = 0, maxAmount = 999999.99) {
  if (typeof amount !== 'number' || isNaN(amount)) {
    return false;
  }

  return amount >= minAmount && amount <= maxAmount;
}

/**
 * Validate percentage value
 * @param {number} percentage - Percentage to validate (0-100)
 * @returns {boolean} - True if percentage is valid
 */
export function isValidPercentage(percentage) {
  if (typeof percentage !== 'number' || isNaN(percentage)) {
    return false;
  }

  return percentage >= 0 && percentage <= 100;
}

/**
 * Validate date string format
 * @param {string} dateString - Date string to validate
 * @param {string} format - Expected format ('iso', 'date-only')
 * @returns {boolean} - True if date string is valid
 */
export function isValidDateString(dateString, format = 'iso') {
  if (!dateString || typeof dateString !== 'string') {
    return false;
  }

  if (format === 'date-only') {
    const dateOnlyRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateOnlyRegex.test(dateString)) {
      return false;
    }
  }

  const date = new Date(dateString);
  return !isNaN(date.getTime());
}

/**
 * Validate JSON string
 * @param {string} jsonString - JSON string to validate
 * @returns {boolean} - True if JSON is valid
 */
export function isValidJSON(jsonString) {
  if (!jsonString || typeof jsonString !== 'string') {
    return false;
  }

  try {
    JSON.parse(jsonString);
    return true;
  } catch {
    return false;
  }
}

/**
 * Validate hex color code
 * @param {string} color - Color code to validate
 * @returns {boolean} - True if color is valid hex
 */
export function isValidHexColor(color) {
  if (!color || typeof color !== 'string') {
    return false;
  }

  const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  return hexColorRegex.test(color);
}

/**
 * Validate postal/zip code
 * @param {string} postalCode - Postal code to validate
 * @param {string} country - Country code ('US', 'CA', etc.)
 * @returns {boolean} - True if postal code is valid
 */
export function isValidPostalCode(postalCode, country = 'US') {
  if (!postalCode || typeof postalCode !== 'string') {
    return false;
  }

  const patterns = {
    US: /^\d{5}(-\d{4})?$/, // 12345 or 12345-6789
    CA: /^[A-Za-z]\d[A-Za-z] \d[A-Za-z]\d$/, // A1A 1A1
    UK: /^[A-Za-z]{1,2}\d[A-Za-z\d]? \d[A-Za-z]{2}$/, // SW1A 1AA
  };

  const pattern = patterns[country.toUpperCase()];
  return pattern ? pattern.test(postalCode) : true; // Default to true for unknown countries
}

/**
 * Sanitize string input
 * @param {string} input - Input string to sanitize
 * @param {object} options - Sanitization options
 * @returns {string} - Sanitized string
 */
export function sanitizeString(input, options = {}) {
  if (!input || typeof input !== 'string') {
    return '';
  }

  const {
    maxLength = 1000,
    allowHtml = false,
    trimWhitespace = true,
    removeNullBytes = true,
  } = options;

  let sanitized = input;

  // Remove null bytes
  if (removeNullBytes) {
    sanitized = sanitized.replace(/\0/g, '');
  }

  // Remove HTML tags if not allowed
  if (!allowHtml) {
    sanitized = sanitized.replace(/<[^>]*>/g, '');
  }

  // Trim whitespace
  if (trimWhitespace) {
    sanitized = sanitized.trim();
  }

  // Limit length
  if (sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength);
  }

  return sanitized;
}

/**
 * Validate and sanitize numeric input
 * @param {*} input - Input to validate and convert
 * @param {object} options - Validation options
 * @returns {number|null} - Sanitized number or null if invalid
 */
export function sanitizeNumber(input, options = {}) {
  const {
    min = Number.MIN_SAFE_INTEGER,
    max = Number.MAX_SAFE_INTEGER,
    allowFloat = true,
    defaultValue = null,
  } = options;

  if (input === null || input === undefined || input === '') {
    return defaultValue;
  }

  let number;

  if (typeof input === 'string') {
    number = allowFloat ? parseFloat(input) : parseInt(input, 10);
  } else if (typeof input === 'number') {
    number = input;
  } else {
    return defaultValue;
  }

  if (isNaN(number) || number < min || number > max) {
    return defaultValue;
  }

  return number;
}

/**
 * Validate array input
 * @param {*} input - Input to validate
 * @param {object} options - Validation options
 * @returns {boolean} - True if array is valid
 */
export function isValidArray(input, options = {}) {
  const {
    minLength = 0,
    maxLength = Number.MAX_SAFE_INTEGER,
    itemValidator = null,
  } = options;

  if (!Array.isArray(input)) {
    return false;
  }

  if (input.length < minLength || input.length > maxLength) {
    return false;
  }

  if (itemValidator && typeof itemValidator === 'function') {
    return input.every(item => itemValidator(item));
  }

  return true;
}

/**
 * Validate object structure
 * @param {*} input - Input to validate
 * @param {object} schema - Schema object with required fields
 * @returns {boolean} - True if object matches schema
 */
export function isValidObjectStructure(input, schema) {
  if (!input || typeof input !== 'object' || Array.isArray(input)) {
    return false;
  }

  for (const [key, validator] of Object.entries(schema)) {
    if (typeof validator === 'function') {
      if (!validator(input[key])) {
        return false;
      }
    } else if (typeof validator === 'object' && validator.required) {
      if (!(key in input)) {
        return false;
      }
      if (validator.type && typeof input[key] !== validator.type) {
        return false;
      }
    }
  }

  return true;
}

/**
 * Validate credit card number using Luhn algorithm
 * @param {string} cardNumber - Credit card number
 * @returns {boolean} - True if card number is valid
 */
export function isValidCreditCard(cardNumber) {
  if (!cardNumber || typeof cardNumber !== 'string') {
    return false;
  }

  const digits = cardNumber.replace(/\D/g, '');
  
  if (digits.length < 13 || digits.length > 19) {
    return false;
  }

  // Luhn algorithm
  let sum = 0;
  let isEven = false;

  for (let i = digits.length - 1; i >= 0; i--) {
    let digit = parseInt(digits[i], 10);

    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }

    sum += digit;
    isEven = !isEven;
  }

  return sum % 10 === 0;
}

/**
 * Validate file extension
 * @param {string} filename - Filename to validate
 * @param {Array<string>} allowedExtensions - Array of allowed extensions
 * @returns {boolean} - True if extension is allowed
 */
export function isValidFileExtension(filename, allowedExtensions) {
  if (!filename || typeof filename !== 'string') {
    return false;
  }

  if (!Array.isArray(allowedExtensions) || allowedExtensions.length === 0) {
    return true; // No restrictions
  }

  const extension = filename.split('.').pop()?.toLowerCase();
  return allowedExtensions.map(ext => ext.toLowerCase()).includes(extension);
}

/**
 * Validate IP address
 * @param {string} ip - IP address to validate
 * @param {string} version - IP version ('v4', 'v6', 'both')
 * @returns {boolean} - True if IP is valid
 */
export function isValidIPAddress(ip, version = 'both') {
  if (!ip || typeof ip !== 'string') {
    return false;
  }

  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

  if (version === 'v4') {
    return ipv4Regex.test(ip);
  } else if (version === 'v6') {
    return ipv6Regex.test(ip);
  } else {
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }
}
