/**
 * Date Manipulation Utilities
 * 
 * This module provides comprehensive date manipulation and formatting
 * utilities for the Americans United Inc application.
 */

/**
 * Get the first day of the previous month
 * @param {Date} referenceDate - Reference date (defaults to now)
 * @returns {Date} - First day of previous month
 */
export function getFirstDayOfPreviousMonth(referenceDate = new Date()) {
  const date = new Date(referenceDate);
  date.setMonth(date.getMonth() - 1);
  date.setDate(1);
  date.setHours(0, 0, 0, 0);
  return date;
}

/**
 * Get the last day of the previous month
 * @param {Date} referenceDate - Reference date (defaults to now)
 * @returns {Date} - Last day of previous month
 */
export function getLastDayOfPreviousMonth(referenceDate = new Date()) {
  const date = new Date(referenceDate);
  date.setDate(0); // This sets to last day of previous month
  date.setHours(23, 59, 59, 999);
  return date;
}

/**
 * Get the first day of the current month
 * @param {Date} referenceDate - Reference date (defaults to now)
 * @returns {Date} - First day of current month
 */
export function getFirstDayOfCurrentMonth(referenceDate = new Date()) {
  const date = new Date(referenceDate);
  date.setDate(1);
  date.setHours(0, 0, 0, 0);
  return date;
}

/**
 * Get the last day of the current month
 * @param {Date} referenceDate - Reference date (defaults to now)
 * @returns {Date} - Last day of current month
 */
export function getLastDayOfCurrentMonth(referenceDate = new Date()) {
  const date = new Date(referenceDate);
  date.setMonth(date.getMonth() + 1);
  date.setDate(0);
  date.setHours(23, 59, 59, 999);
  return date;
}

/**
 * Get date range for a specific month and year
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {object} - Object with startDate and endDate
 */
export function getMonthDateRange(month, year) {
  const startDate = new Date(year, month - 1, 1);
  startDate.setHours(0, 0, 0, 0);
  
  const endDate = new Date(year, month, 0);
  endDate.setHours(23, 59, 59, 999);
  
  return { startDate, endDate };
}

/**
 * Format date to YYYY-MM-DD string
 * @param {Date} date - Date to format
 * @returns {string} - Formatted date string
 */
export function formatDateToString(date) {
  return date.toISOString().split('T')[0];
}

/**
 * Format date to ISO string for API calls
 * @param {Date} date - Date to format
 * @returns {string} - ISO formatted date string
 */
export function formatDateToISO(date) {
  return date.toISOString();
}

/**
 * Parse date string to Date object
 * @param {string} dateString - Date string (YYYY-MM-DD or ISO format)
 * @returns {Date} - Parsed date object
 */
export function parseDateString(dateString) {
  if (!dateString) return null;
  
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    throw new Error(`Invalid date string: ${dateString}`);
  }
  
  return date;
}

/**
 * Check if a date is within a range
 * @param {Date} date - Date to check
 * @param {Date} startDate - Range start date
 * @param {Date} endDate - Range end date
 * @returns {boolean} - True if date is within range
 */
export function isDateInRange(date, startDate, endDate) {
  return date >= startDate && date <= endDate;
}

/**
 * Get the number of days between two dates
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @returns {number} - Number of days
 */
export function getDaysBetween(startDate, endDate) {
  const timeDiff = endDate.getTime() - startDate.getTime();
  return Math.ceil(timeDiff / (1000 * 3600 * 24));
}

/**
 * Add days to a date
 * @param {Date} date - Base date
 * @param {number} days - Number of days to add
 * @returns {Date} - New date with days added
 */
export function addDays(date, days) {
  const newDate = new Date(date);
  newDate.setDate(newDate.getDate() + days);
  return newDate;
}

/**
 * Subtract days from a date
 * @param {Date} date - Base date
 * @param {number} days - Number of days to subtract
 * @returns {Date} - New date with days subtracted
 */
export function subtractDays(date, days) {
  return addDays(date, -days);
}

/**
 * Get the start of day (00:00:00.000)
 * @param {Date} date - Date to modify
 * @returns {Date} - Date at start of day
 */
export function getStartOfDay(date) {
  const newDate = new Date(date);
  newDate.setHours(0, 0, 0, 0);
  return newDate;
}

/**
 * Get the end of day (23:59:59.999)
 * @param {Date} date - Date to modify
 * @returns {Date} - Date at end of day
 */
export function getEndOfDay(date) {
  const newDate = new Date(date);
  newDate.setHours(23, 59, 59, 999);
  return newDate;
}

/**
 * Check if two dates are on the same day
 * @param {Date} date1 - First date
 * @param {Date} date2 - Second date
 * @returns {boolean} - True if dates are on same day
 */
export function isSameDay(date1, date2) {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
}

/**
 * Get relative time string (e.g., "2 hours ago", "in 3 days")
 * @param {Date} date - Date to compare
 * @param {Date} referenceDate - Reference date (defaults to now)
 * @returns {string} - Relative time string
 */
export function getRelativeTime(date, referenceDate = new Date()) {
  const diffMs = date.getTime() - referenceDate.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (Math.abs(diffSeconds) < 60) {
    return diffSeconds >= 0 ? 'in a few seconds' : 'a few seconds ago';
  } else if (Math.abs(diffMinutes) < 60) {
    const minutes = Math.abs(diffMinutes);
    return diffMinutes >= 0 
      ? `in ${minutes} minute${minutes !== 1 ? 's' : ''}`
      : `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
  } else if (Math.abs(diffHours) < 24) {
    const hours = Math.abs(diffHours);
    return diffHours >= 0
      ? `in ${hours} hour${hours !== 1 ? 's' : ''}`
      : `${hours} hour${hours !== 1 ? 's' : ''} ago`;
  } else {
    const days = Math.abs(diffDays);
    return diffDays >= 0
      ? `in ${days} day${days !== 1 ? 's' : ''}`
      : `${days} day${days !== 1 ? 's' : ''} ago`;
  }
}

/**
 * Format date for display in different locales
 * @param {Date} date - Date to format
 * @param {string} locale - Locale string (defaults to 'en-US')
 * @param {object} options - Intl.DateTimeFormat options
 * @returns {string} - Formatted date string
 */
export function formatDateForDisplay(date, locale = 'en-US', options = {}) {
  const defaultOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    ...options,
  };
  
  return new Intl.DateTimeFormat(locale, defaultOptions).format(date);
}

/**
 * Get timezone offset in minutes
 * @param {Date} date - Date to get offset for
 * @returns {number} - Timezone offset in minutes
 */
export function getTimezoneOffset(date = new Date()) {
  return date.getTimezoneOffset();
}

/**
 * Convert date to UTC
 * @param {Date} date - Date to convert
 * @returns {Date} - UTC date
 */
export function toUTC(date) {
  return new Date(date.getTime() + (date.getTimezoneOffset() * 60000));
}

/**
 * Convert UTC date to local time
 * @param {Date} utcDate - UTC date
 * @returns {Date} - Local date
 */
export function fromUTC(utcDate) {
  return new Date(utcDate.getTime() - (utcDate.getTimezoneOffset() * 60000));
}

/**
 * Validate date range
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @param {number} maxDays - Maximum allowed days in range
 * @returns {boolean} - True if range is valid
 */
export function isValidDateRange(startDate, endDate, maxDays = 365) {
  if (startDate >= endDate) return false;
  
  const daysDiff = getDaysBetween(startDate, endDate);
  return daysDiff <= maxDays;
}

/**
 * Get business days between two dates (excludes weekends)
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @returns {number} - Number of business days
 */
export function getBusinessDaysBetween(startDate, endDate) {
  let count = 0;
  const current = new Date(startDate);
  
  while (current <= endDate) {
    const dayOfWeek = current.getDay();
    if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not Sunday (0) or Saturday (6)
      count++;
    }
    current.setDate(current.getDate() + 1);
  }
  
  return count;
}
