/**
 * Formatting Utilities
 * 
 * This module provides comprehensive formatting utilities for currency,
 * numbers, strings, and other data types used throughout the application.
 */

/**
 * Format currency amount
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code (default: 'USD')
 * @param {string} locale - Locale for formatting (default: 'en-US')
 * @returns {string} - Formatted currency string
 */
export function formatCurrency(amount, currency = 'USD', locale = 'en-US') {
  if (typeof amount !== 'number' || isNaN(amount)) {
    return '$0.00';
  }

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}

/**
 * Format number with thousands separators
 * @param {number} number - Number to format
 * @param {string} locale - Locale for formatting (default: 'en-US')
 * @param {object} options - Additional formatting options
 * @returns {string} - Formatted number string
 */
export function formatNumber(number, locale = 'en-US', options = {}) {
  if (typeof number !== 'number' || isNaN(number)) {
    return '0';
  }

  const defaultOptions = {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
    ...options,
  };

  return new Intl.NumberFormat(locale, defaultOptions).format(number);
}

/**
 * Format percentage
 * @param {number} value - Value to format as percentage (0.1 = 10%)
 * @param {number} decimals - Number of decimal places (default: 1)
 * @param {string} locale - Locale for formatting (default: 'en-US')
 * @returns {string} - Formatted percentage string
 */
export function formatPercentage(value, decimals = 1, locale = 'en-US') {
  if (typeof value !== 'number' || isNaN(value)) {
    return '0%';
  }

  return new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
}

/**
 * Format shop domain name for display
 * @param {string} shopDomain - Shop domain (e.g., 'my-shop.myshopify.com')
 * @returns {string} - Formatted shop name (e.g., 'My Shop')
 */
export function formatShopName(shopDomain) {
  if (!shopDomain || typeof shopDomain !== 'string') {
    return 'Unknown Shop';
  }

  return shopDomain
    .replace('.myshopify.com', '') // Remove domain suffix
    .split('-') // Split on hyphens
    .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize each word
    .join(' '); // Join with spaces
}

/**
 * Format file size in human-readable format
 * @param {number} bytes - Size in bytes
 * @param {number} decimals - Number of decimal places (default: 2)
 * @returns {string} - Formatted file size string
 */
export function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Format duration in human-readable format
 * @param {number} milliseconds - Duration in milliseconds
 * @returns {string} - Formatted duration string
 */
export function formatDuration(milliseconds) {
  if (typeof milliseconds !== 'number' || milliseconds < 0) {
    return '0ms';
  }

  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}d ${hours % 24}h ${minutes % 60}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else if (seconds > 0) {
    return `${seconds}s`;
  } else {
    return `${milliseconds}ms`;
  }
}

/**
 * Truncate string with ellipsis
 * @param {string} str - String to truncate
 * @param {number} maxLength - Maximum length
 * @param {string} suffix - Suffix to add (default: '...')
 * @returns {string} - Truncated string
 */
export function truncateString(str, maxLength, suffix = '...') {
  if (!str || typeof str !== 'string') {
    return '';
  }

  if (str.length <= maxLength) {
    return str;
  }

  return str.substring(0, maxLength - suffix.length) + suffix;
}

/**
 * Capitalize first letter of string
 * @param {string} str - String to capitalize
 * @returns {string} - Capitalized string
 */
export function capitalize(str) {
  if (!str || typeof str !== 'string') {
    return '';
  }

  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * Convert string to title case
 * @param {string} str - String to convert
 * @returns {string} - Title case string
 */
export function toTitleCase(str) {
  if (!str || typeof str !== 'string') {
    return '';
  }

  return str.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
}

/**
 * Convert camelCase to kebab-case
 * @param {string} str - CamelCase string
 * @returns {string} - kebab-case string
 */
export function camelToKebab(str) {
  if (!str || typeof str !== 'string') {
    return '';
  }

  return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
}

/**
 * Convert kebab-case to camelCase
 * @param {string} str - kebab-case string
 * @returns {string} - camelCase string
 */
export function kebabToCamel(str) {
  if (!str || typeof str !== 'string') {
    return '';
  }

  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
}

/**
 * Format phone number
 * @param {string} phoneNumber - Raw phone number
 * @param {string} format - Format type ('us', 'international')
 * @returns {string} - Formatted phone number
 */
export function formatPhoneNumber(phoneNumber, format = 'us') {
  if (!phoneNumber || typeof phoneNumber !== 'string') {
    return '';
  }

  // Remove all non-digit characters
  const digits = phoneNumber.replace(/\D/g, '');

  if (format === 'us' && digits.length === 10) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
  } else if (format === 'us' && digits.length === 11 && digits[0] === '1') {
    return `+1 (${digits.slice(1, 4)}) ${digits.slice(4, 7)}-${digits.slice(7)}`;
  }

  return phoneNumber; // Return original if can't format
}

/**
 * Format address for display
 * @param {object} address - Address object
 * @returns {string} - Formatted address string
 */
export function formatAddress(address) {
  if (!address || typeof address !== 'object') {
    return '';
  }

  const parts = [];
  
  if (address.address1) parts.push(address.address1);
  if (address.address2) parts.push(address.address2);
  
  const cityStateZip = [];
  if (address.city) cityStateZip.push(address.city);
  if (address.province || address.state) cityStateZip.push(address.province || address.state);
  if (address.zip || address.postal_code) cityStateZip.push(address.zip || address.postal_code);
  
  if (cityStateZip.length > 0) {
    parts.push(cityStateZip.join(', '));
  }
  
  if (address.country) parts.push(address.country);
  
  return parts.join('\n');
}

/**
 * Format order ID for display
 * @param {string|number} orderId - Order ID
 * @param {string} prefix - Prefix to add (default: '#')
 * @returns {string} - Formatted order ID
 */
export function formatOrderId(orderId, prefix = '#') {
  if (!orderId) {
    return '';
  }

  return `${prefix}${orderId}`;
}

/**
 * Format SKU for display
 * @param {string} sku - SKU string
 * @returns {string} - Formatted SKU
 */
export function formatSKU(sku) {
  if (!sku || typeof sku !== 'string') {
    return '';
  }

  return sku.toUpperCase();
}

/**
 * Format status for display
 * @param {string} status - Status string
 * @returns {string} - Formatted status
 */
export function formatStatus(status) {
  if (!status || typeof status !== 'string') {
    return '';
  }

  return status
    .replace(/_/g, ' ') // Replace underscores with spaces
    .replace(/\b\w/g, l => l.toUpperCase()); // Capitalize first letter of each word
}

/**
 * Format array as comma-separated list
 * @param {Array} array - Array to format
 * @param {string} conjunction - Conjunction for last item (default: 'and')
 * @returns {string} - Formatted list string
 */
export function formatList(array, conjunction = 'and') {
  if (!Array.isArray(array) || array.length === 0) {
    return '';
  }

  if (array.length === 1) {
    return array[0];
  }

  if (array.length === 2) {
    return `${array[0]} ${conjunction} ${array[1]}`;
  }

  const lastItem = array[array.length - 1];
  const otherItems = array.slice(0, -1);
  
  return `${otherItems.join(', ')}, ${conjunction} ${lastItem}`;
}

/**
 * Sanitize string for URL use
 * @param {string} str - String to sanitize
 * @returns {string} - URL-safe string
 */
export function sanitizeForUrl(str) {
  if (!str || typeof str !== 'string') {
    return '';
  }

  return str
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-') // Replace non-alphanumeric with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Format JSON for display with proper indentation
 * @param {*} obj - Object to format
 * @param {number} indent - Indentation spaces (default: 2)
 * @returns {string} - Formatted JSON string
 */
export function formatJSON(obj, indent = 2) {
  try {
    return JSON.stringify(obj, null, indent);
  } catch (error) {
    return 'Invalid JSON';
  }
}
