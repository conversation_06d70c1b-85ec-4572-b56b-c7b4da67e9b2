/**
 * Cryptographic Utilities
 * 
 * This module provides cryptographic functions for secure operations
 * including hashing, encryption, and secure random generation.
 */

import crypto from 'crypto';

/**
 * Generate a secure random string
 * @param {number} length - Length of the string
 * @param {string} charset - Character set to use
 * @returns {string} - Random string
 */
export function generateSecureRandom(length = 32, charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') {
  let result = '';
  const bytes = crypto.randomBytes(length);
  
  for (let i = 0; i < length; i++) {
    result += charset[bytes[i] % charset.length];
  }
  
  return result;
}

/**
 * Generate a secure random hex string
 * @param {number} bytes - Number of bytes
 * @returns {string} - Hex string
 */
export function generateSecureHex(bytes = 16) {
  return crypto.randomBytes(bytes).toString('hex');
}

/**
 * Generate a UUID v4
 * @returns {string} - UUID string
 */
export function generateUUID() {
  return crypto.randomUUID();
}

/**
 * Create SHA-256 hash
 * @param {string} data - Data to hash
 * @param {string} encoding - Output encoding (default: 'hex')
 * @returns {string} - Hash string
 */
export function createSHA256Hash(data, encoding = 'hex') {
  return crypto.createHash('sha256').update(data).digest(encoding);
}

/**
 * Create SHA-512 hash
 * @param {string} data - Data to hash
 * @param {string} encoding - Output encoding (default: 'hex')
 * @returns {string} - Hash string
 */
export function createSHA512Hash(data, encoding = 'hex') {
  return crypto.createHash('sha512').update(data).digest(encoding);
}

/**
 * Create HMAC signature
 * @param {string} data - Data to sign
 * @param {string} secret - Secret key
 * @param {string} algorithm - Hash algorithm (default: 'sha256')
 * @param {string} encoding - Output encoding (default: 'hex')
 * @returns {string} - HMAC signature
 */
export function createHMAC(data, secret, algorithm = 'sha256', encoding = 'hex') {
  return crypto.createHmac(algorithm, secret).update(data).digest(encoding);
}

/**
 * Verify HMAC signature
 * @param {string} data - Original data
 * @param {string} signature - Signature to verify
 * @param {string} secret - Secret key
 * @param {string} algorithm - Hash algorithm (default: 'sha256')
 * @returns {boolean} - True if signature is valid
 */
export function verifyHMAC(data, signature, secret, algorithm = 'sha256') {
  const expectedSignature = createHMAC(data, secret, algorithm);
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  );
}

/**
 * Encrypt data using AES-256-GCM
 * @param {string} plaintext - Data to encrypt
 * @param {string} key - Encryption key (32 bytes)
 * @returns {object} - Encrypted data with IV and auth tag
 */
export function encryptAES256GCM(plaintext, key) {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher('aes-256-gcm', key);
  cipher.setAAD(Buffer.from('additional-data'));
  
  let encrypted = cipher.update(plaintext, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  const authTag = cipher.getAuthTag();
  
  return {
    encrypted,
    iv: iv.toString('hex'),
    authTag: authTag.toString('hex'),
  };
}

/**
 * Decrypt data using AES-256-GCM
 * @param {object} encryptedData - Encrypted data object
 * @param {string} key - Decryption key
 * @returns {string} - Decrypted plaintext
 */
export function decryptAES256GCM(encryptedData, key) {
  const { encrypted, iv, authTag } = encryptedData;
  
  const decipher = crypto.createDecipher('aes-256-gcm', key);
  decipher.setAAD(Buffer.from('additional-data'));
  decipher.setAuthTag(Buffer.from(authTag, 'hex'));
  
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
}

/**
 * Generate a secure password hash using scrypt
 * @param {string} password - Password to hash
 * @param {string} salt - Salt (optional, will generate if not provided)
 * @returns {Promise<object>} - Hash and salt
 */
export async function hashPassword(password, salt = null) {
  if (!salt) {
    salt = crypto.randomBytes(32).toString('hex');
  }
  
  return new Promise((resolve, reject) => {
    crypto.scrypt(password, salt, 64, (err, derivedKey) => {
      if (err) reject(err);
      resolve({
        hash: derivedKey.toString('hex'),
        salt,
      });
    });
  });
}

/**
 * Verify a password against a hash
 * @param {string} password - Password to verify
 * @param {string} hash - Stored hash
 * @param {string} salt - Stored salt
 * @returns {Promise<boolean>} - True if password is valid
 */
export async function verifyPassword(password, hash, salt) {
  const { hash: computedHash } = await hashPassword(password, salt);
  return crypto.timingSafeEqual(
    Buffer.from(hash, 'hex'),
    Buffer.from(computedHash, 'hex')
  );
}

/**
 * Generate a secure API key
 * @param {number} length - Length of the key (default: 32)
 * @returns {string} - API key
 */
export function generateAPIKey(length = 32) {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  return generateSecureRandom(length, charset);
}

/**
 * Generate a secure token with expiration
 * @param {object} payload - Token payload
 * @param {string} secret - Secret key
 * @param {number} expiresIn - Expiration time in seconds
 * @returns {string} - Signed token
 */
export function generateSecureToken(payload, secret, expiresIn = 3600) {
  const tokenData = {
    ...payload,
    exp: Math.floor(Date.now() / 1000) + expiresIn,
    iat: Math.floor(Date.now() / 1000),
  };
  
  const tokenString = JSON.stringify(tokenData);
  const signature = createHMAC(tokenString, secret);
  
  return Buffer.from(JSON.stringify({
    data: tokenString,
    signature,
  })).toString('base64');
}

/**
 * Verify and decode a secure token
 * @param {string} token - Token to verify
 * @param {string} secret - Secret key
 * @returns {object|null} - Decoded payload or null if invalid
 */
export function verifySecureToken(token, secret) {
  try {
    const tokenObj = JSON.parse(Buffer.from(token, 'base64').toString());
    const { data, signature } = tokenObj;
    
    // Verify signature
    if (!verifyHMAC(data, signature, secret)) {
      return null;
    }
    
    const payload = JSON.parse(data);
    
    // Check expiration
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return null;
    }
    
    return payload;
  } catch (error) {
    return null;
  }
}

/**
 * Create a secure hash for webhook verification
 * @param {string} payload - Webhook payload
 * @param {string} secret - Webhook secret
 * @returns {string} - Webhook signature
 */
export function createWebhookSignature(payload, secret) {
  return createHMAC(payload, secret, 'sha256');
}

/**
 * Verify webhook signature
 * @param {string} payload - Webhook payload
 * @param {string} signature - Received signature
 * @param {string} secret - Webhook secret
 * @returns {boolean} - True if signature is valid
 */
export function verifyWebhookSignature(payload, signature, secret) {
  // Remove 'sha256=' prefix if present
  const cleanSignature = signature.replace(/^sha256=/, '');
  return verifyHMAC(payload, cleanSignature, secret, 'sha256');
}

/**
 * Generate a secure session ID
 * @returns {string} - Session ID
 */
export function generateSessionId() {
  return generateSecureHex(32);
}

/**
 * Create a secure hash for data integrity
 * @param {object} data - Data to hash
 * @returns {string} - Integrity hash
 */
export function createIntegrityHash(data) {
  const dataString = JSON.stringify(data, Object.keys(data).sort());
  return createSHA256Hash(dataString);
}

/**
 * Verify data integrity
 * @param {object} data - Data to verify
 * @param {string} expectedHash - Expected hash
 * @returns {boolean} - True if data is intact
 */
export function verifyDataIntegrity(data, expectedHash) {
  const computedHash = createIntegrityHash(data);
  return computedHash === expectedHash;
}

/**
 * Generate a secure nonce
 * @param {number} length - Length in bytes (default: 16)
 * @returns {string} - Nonce string
 */
export function generateNonce(length = 16) {
  return crypto.randomBytes(length).toString('base64');
}

/**
 * Constant-time string comparison
 * @param {string} a - First string
 * @param {string} b - Second string
 * @returns {boolean} - True if strings are equal
 */
export function constantTimeCompare(a, b) {
  if (a.length !== b.length) {
    return false;
  }
  
  return crypto.timingSafeEqual(
    Buffer.from(a),
    Buffer.from(b)
  );
}
