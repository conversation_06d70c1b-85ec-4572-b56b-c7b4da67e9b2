/**
 * memory-cache.js
 * 
 * A simple in-memory cache utility to reduce database lookups and API calls
 */

/**
 * Default cache expiration time in milliseconds (1 hour)
 */
const DEFAULT_EXPIRATION = 60 * 60 * 1000;

/**
 * In-memory cache store
 * Structure: {
 *   [key]: {
 *     value: any,
 *     expiry: number (timestamp)
 *   }
 * }
 */
const cacheStore = new Map();

/**
 * Cache statistics for monitoring
 */
const stats = {
  hits: 0,
  misses: 0,
  sets: 0,
  deletes: 0,
  lastCleanup: Date.now()
};

/**
 * Get a value from the cache
 * 
 * @param {string} key - The cache key
 * @returns {any|null} - The cached value or null if not found or expired
 */
export function get(key) {
  const item = cacheStore.get(key);
  
  if (!item) {
    stats.misses++;
    return null;
  }
  
  // Check if the item has expired
  if (item.expiry < Date.now()) {
    cacheStore.delete(key);
    stats.misses++;
    return null;
  }
  
  stats.hits++;
  return item.value;
}

/**
 * Set a value in the cache
 * 
 * @param {string} key - The cache key
 * @param {any} value - The value to cache
 * @param {number} [expiration=DEFAULT_EXPIRATION] - Expiration time in milliseconds
 */
export function set(key, value, expiration = DEFAULT_EXPIRATION) {
  const expiry = Date.now() + expiration;
  
  cacheStore.set(key, {
    value,
    expiry
  });
  
  stats.sets++;
  
  // Periodically clean up expired items (every 1000 sets)
  if (stats.sets % 1000 === 0) {
    cleanup();
  }
}

/**
 * Delete a value from the cache
 * 
 * @param {string} key - The cache key
 */
export function del(key) {
  cacheStore.delete(key);
  stats.deletes++;
}

/**
 * Check if a key exists in the cache and is not expired
 * 
 * @param {string} key - The cache key
 * @returns {boolean} - Whether the key exists and is not expired
 */
export function has(key) {
  const item = cacheStore.get(key);
  
  if (!item) {
    return false;
  }
  
  // Check if the item has expired
  if (item.expiry < Date.now()) {
    cacheStore.delete(key);
    return false;
  }
  
  return true;
}

/**
 * Get multiple values from the cache
 * 
 * @param {Array<string>} keys - Array of cache keys
 * @returns {Object} - Object mapping keys to values (missing keys are not included)
 */
export function getMany(keys) {
  const result = {};
  
  for (const key of keys) {
    const value = get(key);
    if (value !== null) {
      result[key] = value;
    }
  }
  
  return result;
}

/**
 * Set multiple values in the cache
 * 
 * @param {Object} items - Object mapping keys to values
 * @param {number} [expiration=DEFAULT_EXPIRATION] - Expiration time in milliseconds
 */
export function setMany(items, expiration = DEFAULT_EXPIRATION) {
  for (const [key, value] of Object.entries(items)) {
    set(key, value, expiration);
  }
}

/**
 * Clean up expired items from the cache
 */
export function cleanup() {
  const now = Date.now();
  
  for (const [key, item] of cacheStore.entries()) {
    if (item.expiry < now) {
      cacheStore.delete(key);
    }
  }
  
  stats.lastCleanup = now;
}

/**
 * Get cache statistics
 * 
 * @returns {Object} - Cache statistics
 */
export function getStats() {
  return {
    ...stats,
    size: cacheStore.size,
    hitRate: stats.hits / (stats.hits + stats.misses) || 0
  };
}

/**
 * Reset the cache
 */
export function reset() {
  cacheStore.clear();
  
  // Reset stats
  stats.hits = 0;
  stats.misses = 0;
  stats.sets = 0;
  stats.deletes = 0;
  stats.lastCleanup = Date.now();
}

/**
 * Cache prefix for variant fulfillment services
 */
export const VARIANT_FULFILLMENT_PREFIX = 'vfs:';

/**
 * Get a variant fulfillment service cache key
 * 
 * @param {string} variantId - The variant ID
 * @returns {string} - The cache key
 */
export function getVariantFulfillmentKey(variantId) {
  return `${VARIANT_FULFILLMENT_PREFIX}${variantId}`;
}
