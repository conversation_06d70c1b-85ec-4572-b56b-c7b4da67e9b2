
import db from "../db.server";
import shopify from "../shopify.server";
import { randomUUID } from "crypto";

// Configuration
const INTERVAL_MS = 30000; // 30 seconds between order cycles
const MAX_ITEMS_PER_ORDER = 20; // Maximum number of items per order
const MAX_PRODUCTS_TO_FETCH = 50; // Number of random products to cache for each shop
const FETCH_LIMIT = 250; // Maximum number of products to fetch at once
const CACHE_REFRESH_INTERVAL = 60 * 60 * 1000; // 1 hour - how often to refresh the product cache

// Rate limiting configuration
const DELAY_BETWEEN_SHOPS_MS = 5000; // 5 seconds between processing each shop
const MAX_RETRIES = 3; // Maximum number of retries for a failed API call
const INITIAL_RETRY_DELAY_MS = 1000; // Start with a 1 second delay for retries
const MAX_CALLS_PER_MINUTE = 40; // Shopify's standard rate limit is ~40 calls per minute
const RATE_LIMIT_WINDOW_MS = 60 * 1000; // 1 minute window for rate limiting

// Track API calls to stay under rate limits
const apiCallTracker = {
  calls: [],

  // Add a timestamp for a new API call
  addCall() {
    const now = Date.now();
    this.calls.push(now);
    // Clean up old calls outside the rate limit window
    this.calls = this.calls.filter(time => now - time < RATE_LIMIT_WINDOW_MS);
  },

  // Check if we're approaching the rate limit
  isApproachingLimit() {
    return this.calls.length >= MAX_CALLS_PER_MINUTE * 0.8; // 80% of limit
  },

  // Get recommended delay based on current call volume
  getRecommendedDelay() {
    if (this.calls.length === 0) return 0;

    const callsInWindow = this.calls.length;
    const usagePercentage = callsInWindow / MAX_CALLS_PER_MINUTE;

    if (usagePercentage < 0.5) return 0; // Under 50% usage, no delay
    if (usagePercentage < 0.7) return 500; // 50-70% usage, small delay
    if (usagePercentage < 0.9) return 1000; // 70-90% usage, medium delay
    return 2000; // Over 90% usage, larger delay
  },

  // Reset the call tracker
  reset() {
    this.calls = [];
  }
};

// Cache to store products for each shop
const shopProductsCache = new Map();

// Track running state
let isRunning = false;
let shouldStop = false;

/**
 * Sleep for a specified number of milliseconds
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise<void>}
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Add jitter to a delay value to prevent synchronized retries
 * @param {number} delay - Base delay in milliseconds
 * @param {number} jitterFactor - Factor to determine jitter amount (0-1)
 * @returns {number} - Delay with jitter added
 */
function addJitter(delay, jitterFactor = 0.3) {
  const jitterAmount = delay * jitterFactor;
  return delay + (Math.random() * jitterAmount * 2) - jitterAmount;
}

/**
 * Make a rate-limited GraphQL call with retries and backoff
 * @param {object} shopAdmin - The admin API client
 * @param {string} query - GraphQL query
 * @param {object} variables - Query variables
 * @returns {Promise<object>} - Query response
 */
async function rateLimitedGraphQL(shopAdmin, query, variables = {}) {
  let retries = 0;
  let delay = INITIAL_RETRY_DELAY_MS;

  while (true) {
    try {
      // Check if we're approaching rate limits and add delay if needed
      const recommendedDelay = apiCallTracker.getRecommendedDelay();
      if (recommendedDelay > 0) {
        const delayWithJitter = addJitter(recommendedDelay);
        console.log(`Rate limit approaching, delaying for ${delayWithJitter}ms before API call`);
        await sleep(delayWithJitter);
      }

      // Track this API call
      apiCallTracker.addCall();

      // Make the API call
      const response = await shopAdmin.graphql(query, { variables });
      return response;
    } catch (error) {
      // Check if this is a rate limit error
      const isRateLimitError =
        error.message?.includes('Throttled') ||
        error.message?.includes('rate limit') ||
        error.message?.includes('Too many requests');

      // If we've hit the max retries or it's not a rate limit error, throw
      if (retries >= MAX_RETRIES || !isRateLimitError) {
        throw error;
      }

      // Exponential backoff with jitter
      retries++;
      const backoffDelay = addJitter(delay * Math.pow(2, retries - 1));
      console.log(`Rate limit hit, backing off for ${backoffDelay}ms (retry ${retries}/${MAX_RETRIES})`);
      await sleep(backoffDelay);

      // Reset the API call tracker after a backoff
      apiCallTracker.reset();
    }
  }
}

/**
 * Fetches products from a shop and randomly selects a subset
 * @param {string} shop - The shop domain
 * @param {object} shopAdmin - The admin API client for the shop
 * @returns {Promise<Array>} - Array of random products
 */
async function fetchShopProducts(shop, shopAdmin) {
  try {
    // Check if we already have products cached for this shop
    if (shopProductsCache.has(shop)) {
      return shopProductsCache.get(shop);
    }

    console.log(`Fetching products for shop: ${shop}`);

    // First, get the total count of products
    const countQuery = "query GetProductCount { productsCount { count } }";
    const countResponse = await rateLimitedGraphQL(shopAdmin, countQuery);

    // The response is already parsed by the Shopify client
    if (countResponse.errors) {
      console.error(`Error fetching product count for ${shop}:`, countResponse.errors);
      return [];
    }

    const totalProducts = countResponse.body.data.productsCount.count;

    if (totalProducts === 0) {
      console.log(`Shop ${shop} has no products`);
      return [];
    }

    console.log(`Shop ${shop} has ${totalProducts} total products`);

    // Fetch more products than we need
    const fetchLimit = Math.min(FETCH_LIMIT, MAX_PRODUCTS_TO_FETCH * 5);

    // Since we can't use offset with the GraphQL API, we'll use a random sort key
    // and randomly decide whether to reverse the order to get a more diverse selection
    const sortKeys = ["CREATED_AT", "ID", "INVENTORY_TOTAL", "PRODUCT_TYPE", "PUBLISHED_AT", "TITLE", "UPDATED_AT", "VENDOR"];
    const randomSortKey = sortKeys[Math.floor(Math.random() * sortKeys.length)];
    const randomReverse = Math.random() > 0.5;

    console.log(`Will fetch up to ${fetchLimit} products for shop ${shop} using sort key ${randomSortKey} with reverse=${randomReverse}`);

    // Query products from Shopify using GraphQL with cursor-based pagination and random sorting
    const productsQuery = "query GetProducts($fetchLimit: Int!, $sortKey: ProductSortKeys!, $reverse: Boolean!) { products(first: $fetchLimit, sortKey: $sortKey, reverse: $reverse) { edges { node { id title productType variants(first: 10) { edges { node { id title sku price inventoryQuantity availableForSale } } } } } } }";
    const response = await rateLimitedGraphQL(shopAdmin, productsQuery, {
      fetchLimit,
      sortKey: randomSortKey,
      reverse: randomReverse
    });

    // The response is already parsed by the Shopify client
    if (response.errors) {
      console.error(`Error fetching products for ${shop}:`, response.errors);
      return [];
    }

    // Process and format the products
    const allProducts = response.body.data.products.edges.map(edge => {
      const product = edge.node;

      // Only include variants that have SKUs and are available for sale
      const variants = product.variants.edges
        .map(variantEdge => variantEdge.node)
        .filter(variant => variant.sku && variant.availableForSale);

      if (variants.length === 0) {
        return null; // Skip products with no valid variants
      }

      return {
        id: product.id,
        title: product.title,
        productType: product.productType,
        variants
      };
    }).filter(product => product !== null);

    // If we have fewer products than requested, use all of them
    if (allProducts.length <= MAX_PRODUCTS_TO_FETCH) {
      shopProductsCache.set(shop, allProducts);
      console.log(`Cached ${allProducts.length} products for shop: ${shop}`);
      return allProducts;
    }

    // Randomly select MAX_PRODUCTS_TO_FETCH products
    const randomProducts = [];
    const indices = new Set();

    while (indices.size < MAX_PRODUCTS_TO_FETCH && indices.size < allProducts.length) {
      const randomIndex = Math.floor(Math.random() * allProducts.length);
      if (!indices.has(randomIndex)) {
        indices.add(randomIndex);
        randomProducts.push(allProducts[randomIndex]);
      }
    }

    // Cache the random products for future use
    shopProductsCache.set(shop, randomProducts);

    console.log(`Cached ${randomProducts.length} random products (from ${allProducts.length} total) for shop: ${shop}`);
    return randomProducts;
  } catch (error) {
    console.error(`Error fetching products for ${shop}:`, error.message);
    return [];
  }
}

/**
 * Creates a test order with random products for a shop
 * @param {string} shop - The shop domain
 * @param {object} shopAdmin - The admin API client for the shop
 * @returns {Promise<object|null>} - The created order or null if failed
 */
async function createTestOrder(shop, shopAdmin) {
  try {
    // Fetch products for this shop
    const products = await fetchShopProducts(shop, shopAdmin);

    if (products.length === 0) {
      console.error(`No products available for shop: ${shop}`);
      return null;
    }

    // Determine how many different products to include (1 to 5)
    const numProductTypes = Math.min(Math.floor(Math.random() * 5) + 1, products.length);

    // Randomly select products
    const selectedProducts = [];
    const usedProductIndices = new Set();

    let totalItems = 0;

    // Select random products until we have enough or reach the maximum
    while (selectedProducts.length < numProductTypes && totalItems < MAX_ITEMS_PER_ORDER) {
      // Pick a random product that we haven't used yet
      let productIndex;
      do {
        productIndex = Math.floor(Math.random() * products.length);
      } while (usedProductIndices.has(productIndex) && usedProductIndices.size < products.length);

      // If we've used all products, break
      if (usedProductIndices.size >= products.length) {
        break;
      }

      usedProductIndices.add(productIndex);
      const product = products[productIndex];

      // Pick a random variant
      const variant = product.variants[Math.floor(Math.random() * product.variants.length)];

      // Determine quantity (1 to 5)
      const quantity = Math.floor(Math.random() * 5) + 1;

      // Make sure we don't exceed the maximum items
      const adjustedQuantity = Math.min(quantity, MAX_ITEMS_PER_ORDER - totalItems);
      if (adjustedQuantity <= 0) {
        break;
      }

      totalItems += adjustedQuantity;

      selectedProducts.push({
        variantId: variant.id,
        title: product.title,
        variantTitle: variant.title,
        sku: variant.sku,
        price: variant.price,
        quantity: adjustedQuantity
      });
    }

    if (selectedProducts.length === 0) {
      console.error(`Could not select any valid products for shop: ${shop}`);
      return null;
    }

    // Create a unique order ID for tracking
    const testOrderId = `test-${randomUUID()}`;

    // Prepare line items for the order
    const lineItems = selectedProducts.map(product => ({
      variantId: product.variantId,
      quantity: product.quantity
    }));

    // Create the order using GraphQL mutation
    const orderCreateMutation = "mutation orderCreate($input: OrderInput!) { orderCreate(input: $input, test: true) { order { id legacyResourceId name test totalPriceSet { shopMoney { amount currencyCode } } } userErrors { field message } } }";

    const orderVariables = {
      input: {
        note: "Test order created by automated script - " + testOrderId,
        email: "<EMAIL>",
        phone: "+15551234567",
        shippingAddress: {
          firstName: "Test",
          lastName: "Customer",
          address1: "123 Test St",
          city: "Test City",
          province: "AL",
          zip: "12345",
          country: "US"
        },
        lineItems,
        customAttributes: [
          {
            key: "test_order",
            value: "true"
          }
        ],
        tags: ["test-order", "automated"]
      }
    };

    const response = await rateLimitedGraphQL(shopAdmin, orderCreateMutation, {
      variables: {
        input: orderVariables.input
      }
    });

    if (response.errors) {
      console.error(`Error creating test order for ${shop}:`, response.errors);
      return null;
    }

    if (response.body.data.orderCreate.userErrors && response.body.data.orderCreate.userErrors.length > 0) {
      console.error(`Error creating test order for ${shop}:`, response.body.data.orderCreate.userErrors);
      return null;
    }

    const order = response.body.data.orderCreate.order;
    console.log("Created " + (order.test ? 'TEST ' : '') + "order #" + order.name + " for " + shop + " with " + totalItems + " items, total: " + order.totalPriceSet.shopMoney.amount + " " + order.totalPriceSet.shopMoney.currencyCode);
    return order;
  } catch (error) {
    console.error(`Error creating test order for ${shop}:`, error.message);
    return null;
  }
}

/**
 * Process all shops and create test orders
 * @param {Function} statusCallback - Optional callback to report status updates
 * @returns {Promise<void>}
 */
async function processAllShops(statusCallback = null) {
  const cycleStartTime = new Date().toISOString();
  const startMessage = `Starting order cycle at ${cycleStartTime}`;
  if (statusCallback) statusCallback(startMessage);
  console.log(`[TEST ORDERS] ${startMessage}`);

  try {
    // Get all shops
    console.log(`[TEST ORDERS] Fetching shops from database`);
    let shops;
    try {
      shops = await db.session.findMany({
        select: {
          shop: true
        },
        distinct: ['shop']
      });
      console.log(`[TEST ORDERS] Successfully fetched shops from database`);
    } catch (dbError) {
      console.error(`[TEST ORDERS ERROR] Failed to fetch shops from database: ${dbError.message}`, dbError);
      throw new Error(`Failed to fetch shops: ${dbError.message}`);
    }

    const shopsMessage = `Found ${shops.length} shops to process`;
    if (statusCallback) statusCallback(shopsMessage);
    console.log(`[TEST ORDERS] ${shopsMessage}`);

    if (shops.length === 0) {
      const noShopsMessage = "No shops found. Exiting.";
      if (statusCallback) statusCallback(noShopsMessage);
      console.error(`[TEST ORDERS] ${noShopsMessage}`);
      return;
    }

    // Log all shop names for debugging
    console.log(`[TEST ORDERS] Shop list: ${shops.map(s => s.shop).join(', ')}`);

    for (const shopData of shops) {
      if (shouldStop) {
        const stoppingMessage = "Stopping test order creation as requested";
        if (statusCallback) statusCallback(stoppingMessage);
        console.log(`[TEST ORDERS] ${stoppingMessage}`);
        break;
      }

      try {
        const processingMessage = `Processing shop: ${shopData.shop}`;
        if (statusCallback) statusCallback(processingMessage);
        console.log(`[TEST ORDERS] ${processingMessage}`);

        // Create a Shopify admin client for this shop
        console.log(`[TEST ORDERS] Finding session for shop: ${shopData.shop}`);
        const shopSession = await shopify.sessionStorage.findSessionsByShop(shopData.shop);

        if (!shopSession || shopSession.length === 0) {
          const noSessionError = `No session found for shop: ${shopData.shop}`;
          console.error(`[TEST ORDERS ERROR] ${noSessionError}`);
          throw new Error(noSessionError);
        }

        console.log(`[TEST ORDERS] Found ${shopSession.length} sessions for shop: ${shopData.shop}`);

        // Use the most recent session
        const latestSession = shopSession[0];
        console.log(`[TEST ORDERS] Using session with ID: ${latestSession.id} for shop: ${shopData.shop}`);

        console.log(`[TEST ORDERS] Creating GraphQL client for shop: ${shopData.shop}`);
        let shopAdmin;
        try {
          shopAdmin = await shopify.api.clients.graphql.create({
            session: latestSession
          });
          console.log(`[TEST ORDERS] Successfully created GraphQL client for shop: ${shopData.shop}`);
        } catch (clientError) {
          console.error(`[TEST ORDERS ERROR] Failed to create GraphQL client for shop ${shopData.shop}: ${clientError.message}`, clientError);
          throw new Error(`Failed to create GraphQL client: ${clientError.message}`);
        }

        console.log(`[TEST ORDERS] Creating test order for shop: ${shopData.shop}`);
        try {
          const order = await createTestOrder(shopData.shop, shopAdmin);
          if (order) {
            console.log(`[TEST ORDERS] Successfully created test order #${order.name} for shop: ${shopData.shop}`);
          } else {
            console.log(`[TEST ORDERS] No order was created for shop: ${shopData.shop}`);
          }
        } catch (orderError) {
          console.error(`[TEST ORDERS ERROR] Failed to create test order for shop ${shopData.shop}: ${orderError.message}`, orderError);
          throw new Error(`Failed to create test order: ${orderError.message}`);
        }

        // Add a delay between shops to avoid rate limits
        if (shops.length > 1) {
          const delayMs = addJitter(DELAY_BETWEEN_SHOPS_MS);
          const delayMessage = `Waiting ${delayMs}ms before processing next shop`;
          if (statusCallback) statusCallback(delayMessage);
          console.log(`[TEST ORDERS] ${delayMessage}`);
          await sleep(delayMs);
        }
      } catch (shopError) {
        const errorMessage = `Error creating test order for shop ${shopData.shop}: ${shopError.message}`;
        if (statusCallback) statusCallback(errorMessage);
        console.error(`[TEST ORDERS ERROR] ${errorMessage}`, shopError);
        // Continue with next shop even if this one fails
      }
    }

    const completedMessage = `Completed order cycle at ${new Date().toISOString()}`;
    if (statusCallback) statusCallback(completedMessage);
    console.log(`[TEST ORDERS] ${completedMessage}`);
  } catch (error) {
    const errorMessage = `Error processing shops: ${error.message}`;
    if (statusCallback) statusCallback(errorMessage);
    console.error(`[TEST ORDERS ERROR] ${errorMessage}`, error);
    // Re-throw to let the caller handle it
    throw error;
  }
}

/**
 * Refresh the product cache for all shops
 * @param {Function} statusCallback - Optional callback to report status updates
 * @returns {Promise<void>}
 */
async function refreshProductCache(statusCallback = null) {
  if (statusCallback) statusCallback(`Starting product cache refresh at ${new Date().toISOString()}`);
  console.log(`Starting product cache refresh at ${new Date().toISOString()}`);

  try {
    // Clear the cache
    shopProductsCache.clear();

    // Get all shops
    const shops = await db.session.findMany({
      select: {
        shop: true
      },
      distinct: ['shop']
    });

    // Fetch new random products for all shops sequentially
    for (const shopData of shops) {
      if (shouldStop) {
        if (statusCallback) statusCallback("Stopping cache refresh as requested");
        console.log("Stopping cache refresh as requested");
        break;
      }

      try {
        if (statusCallback) statusCallback(`Refreshing product cache for shop: ${shopData.shop}`);
        console.log(`Refreshing product cache for shop: ${shopData.shop}`);

        // Create a Shopify admin client for this shop
        const shopSession = await shopify.sessionStorage.findSessionsByShop(shopData.shop);
        if (!shopSession || shopSession.length === 0) {
          throw new Error(`No session found for shop: ${shopData.shop}`);
        }

        // Use the most recent session
        const latestSession = shopSession[0];
        const shopAdmin = await shopify.api.clients.graphql.create({
          session: latestSession
        });

        await fetchShopProducts(shopData.shop, shopAdmin);

        // Add a delay between shops
        if (shops.length > 1) {
          await sleep(addJitter(DELAY_BETWEEN_SHOPS_MS));
        }
      } catch (error) {
        if (statusCallback) statusCallback(`Error refreshing products for shop ${shopData.shop}: ${error.message}`);
        console.error(`Error refreshing products for shop ${shopData.shop}:`, error.message);
      }
    }

    if (statusCallback) statusCallback(`Completed product cache refresh at ${new Date().toISOString()}`);
    console.log(`Completed product cache refresh at ${new Date().toISOString()}`);
  } catch (error) {
    if (statusCallback) statusCallback(`Error refreshing product cache: ${error.message}`);
    console.error("Error refreshing product cache:", error.message);
  }
}

/**
 * Start creating test orders at regular intervals
 * @param {number} durationMinutes - How long to run in minutes (0 for indefinite)
 * @param {Function} statusCallback - Optional callback to report status updates
 * @returns {Promise<void>}
 */
export async function startTestOrderCreation(durationMinutes = 0, statusCallback = null) {
  console.log(`[TEST ORDERS] startTestOrderCreation called with duration: ${durationMinutes} minutes`);

  // Don't start if already running
  if (isRunning) {
    const message = "Test order creation is already running";
    if (statusCallback) statusCallback(message);
    console.log(`[TEST ORDERS] ${message}`);
    return;
  }

  isRunning = true;
  shouldStop = false;

  const startMessage = `Starting test order creation for ${durationMinutes > 0 ? durationMinutes + ' minutes' : 'indefinite time'}`;
  if (statusCallback) statusCallback(startMessage);
  console.log(`[TEST ORDERS] ${startMessage}`);

  try {
    // Set up end time if duration is specified
    const endTime = durationMinutes > 0 ? Date.now() + (durationMinutes * 60 * 1000) : 0;
    console.log(`[TEST ORDERS] End time set to: ${endTime > 0 ? new Date(endTime).toISOString() : 'indefinite'}`);

    // Pre-fetch products for all shops
    const prefetchMessage = "Pre-fetching products for all shops...";
    if (statusCallback) statusCallback(prefetchMessage);
    console.log(`[TEST ORDERS] ${prefetchMessage}`);

    try {
      await refreshProductCache((status) => {
        const message = `[Cache] ${status}`;
        console.log(`[TEST ORDERS] ${message}`);
        if (statusCallback) statusCallback(message);
      });
      console.log(`[TEST ORDERS] Product cache refresh completed successfully`);
    } catch (cacheError) {
      console.error(`[TEST ORDERS ERROR] Error refreshing product cache: ${cacheError.message}`, cacheError);
      // Continue even if cache refresh fails
    }

    // Create initial orders immediately
    console.log(`[TEST ORDERS] Starting initial order cycle`);
    try {
      await processAllShops((status) => {
        const message = `[Initial] ${status}`;
        console.log(`[TEST ORDERS] ${message}`);
        if (statusCallback) statusCallback(message);
      });
      console.log(`[TEST ORDERS] Initial order cycle completed successfully`);
    } catch (initialOrderError) {
      console.error(`[TEST ORDERS ERROR] Error in initial order cycle: ${initialOrderError.message}`, initialOrderError);
      // Continue even if initial order creation fails
    }

    // Set up interval for subsequent order cycles
    console.log(`[TEST ORDERS] Setting up order creation interval (${INTERVAL_MS}ms)`);
    const orderInterval = setInterval(async () => {
      console.log(`[TEST ORDERS] Order interval triggered at ${new Date().toISOString()}`);

      if (shouldStop || (endTime > 0 && Date.now() >= endTime)) {
        console.log(`[TEST ORDERS] Stopping order interval - shouldStop: ${shouldStop}, endTime reached: ${endTime > 0 && Date.now() >= endTime}`);
        clearInterval(orderInterval);
        clearInterval(cacheInterval);
        isRunning = false;
        const stopMessage = `Test order creation stopped after ${durationMinutes} minutes`;
        if (statusCallback) statusCallback(stopMessage);
        console.log(`[TEST ORDERS] ${stopMessage}`);
        return;
      }

      try {
        await processAllShops((status) => {
          const message = `[Cycle] ${status}`;
          console.log(`[TEST ORDERS] ${message}`);
          if (statusCallback) statusCallback(message);
        });
        console.log(`[TEST ORDERS] Order cycle completed successfully`);
      } catch (cycleError) {
        console.error(`[TEST ORDERS ERROR] Error in order cycle: ${cycleError.message}`, cycleError);
        // Continue to next cycle even if this one fails
      }
    }, INTERVAL_MS);

    // Set up interval to refresh product cache periodically
    console.log(`[TEST ORDERS] Setting up cache refresh interval (${CACHE_REFRESH_INTERVAL}ms)`);
    const cacheInterval = setInterval(async () => {
      console.log(`[TEST ORDERS] Cache refresh interval triggered at ${new Date().toISOString()}`);

      if (shouldStop || (endTime > 0 && Date.now() >= endTime)) {
        console.log(`[TEST ORDERS] Stopping cache interval - shouldStop: ${shouldStop}, endTime reached: ${endTime > 0 && Date.now() >= endTime}`);
        clearInterval(cacheInterval);
        return;
      }

      try {
        await refreshProductCache((status) => {
          const message = `[Cache Refresh] ${status}`;
          console.log(`[TEST ORDERS] ${message}`);
          if (statusCallback) statusCallback(message);
        });
        console.log(`[TEST ORDERS] Periodic cache refresh completed successfully`);
      } catch (cacheRefreshError) {
        console.error(`[TEST ORDERS ERROR] Error in periodic cache refresh: ${cacheRefreshError.message}`, cacheRefreshError);
        // Continue even if cache refresh fails
      }
    }, CACHE_REFRESH_INTERVAL);

    // If duration is specified, set up a timeout to stop
    if (durationMinutes > 0) {
      console.log(`[TEST ORDERS] Setting up scheduled stop after ${durationMinutes} minutes`);
      setTimeout(() => {
        shouldStop = true;
        const scheduleStopMessage = `Scheduled stop after ${durationMinutes} minutes`;
        if (statusCallback) statusCallback(scheduleStopMessage);
        console.log(`[TEST ORDERS] ${scheduleStopMessage}`);
      }, durationMinutes * 60 * 1000);
    }

    const runningMessage = `Test order creation running. Creating test orders every ${INTERVAL_MS / 1000} seconds.`;
    if (statusCallback) statusCallback(runningMessage);
    console.log(`[TEST ORDERS] ${runningMessage}`);
  } catch (error) {
    isRunning = false;
    const errorMessage = `Error starting test order creation: ${error.message}`;
    if (statusCallback) statusCallback(errorMessage);
    console.error(`[TEST ORDERS ERROR] ${errorMessage}`, error);
    // Re-throw the error so the caller can handle it
    throw error;
  }
}

/**
 * Stop the test order creation process
 * @param {Function} statusCallback - Optional callback to report status updates
 */
export function stopTestOrderCreation(statusCallback = null) {
  if (!isRunning) {
    if (statusCallback) statusCallback("Test order creation is not running");
    console.log("Test order creation is not running");
    return;
  }

  shouldStop = true;
  if (statusCallback) statusCallback("Stopping test order creation...");
  console.log("Stopping test order creation...");
}

/**
 * Check if test order creation is running
 * @returns {boolean} - True if running, false otherwise
 */
export function isTestOrderCreationRunning() {
  return isRunning;
}

/**
 * Create a single test order for a shop
 * @param {string} shop - The shop domain
 * @param {Function} statusCallback - Optional callback to report status updates
 * @returns {Promise<object|null>} - The created order or null if failed
 */
export async function createSingleTestOrder(shop, statusCallback = null) {
  try {
    if (statusCallback) statusCallback(`Creating test order for shop: ${shop}`);
    console.log(`Creating test order for shop: ${shop}`);

    // Create a Shopify admin client for this shop
    const shopSession = await shopify.sessionStorage.findSessionsByShop(shop);
    if (!shopSession || shopSession.length === 0) {
      throw new Error(`No session found for shop: ${shop}`);
    }

    // Use the most recent session
    const latestSession = shopSession[0];
    const shopAdmin = await shopify.api.clients.graphql.create({
      session: latestSession
    });

    const order = await createTestOrder(shop, shopAdmin);

    if (order) {
      if (statusCallback) statusCallback(`Successfully created test order #${order.name} for ${shop}`);
    } else {
      if (statusCallback) statusCallback(`Failed to create test order for ${shop}`);
    }

    return order;
  } catch (error) {
    if (statusCallback) statusCallback(`Error creating test order for ${shop}: ${error.message}`);
    console.error(`Error creating test order for ${shop}:`, error.message);
    return null;
  }
}

/**
 * Create a single test order for a shop using the provided admin object
 * @param {string} shop - The shop domain
 * @param {object} admin - The admin API client from authenticate.admin
 * @param {Function} statusCallback - Optional callback to report status updates
 * @returns {Promise<object|null>} - The created order or null if failed
 */
export async function createSingleTestOrderWithAdmin(shop, admin, statusCallback = null) {
  try {
    if (statusCallback) statusCallback(`Creating test order for shop: ${shop}`);
    console.log(`[TEST ORDERS] Creating test order for shop: ${shop} with provided admin object`);
    console.log(`[TEST ORDERS] Admin object type:`, typeof admin);
    console.log(`[TEST ORDERS] Admin object keys:`, Object.keys(admin || {}));

    if (!admin) {
      console.error(`[TEST ORDERS ERROR] Admin object is undefined for shop: ${shop}`);
      throw new Error("Admin object is undefined");
    }

    if (!admin.graphql) {
      console.error(`[TEST ORDERS ERROR] Admin object has no graphql property for shop: ${shop}`);
      console.error(`[TEST ORDERS ERROR] Admin object properties:`, Object.getOwnPropertyNames(admin));
      throw new Error("Invalid admin object: missing graphql property");
    }

    console.log(`[TEST ORDERS] Using admin.graphql for shop: ${shop}`);
    console.log(`[TEST ORDERS] admin.graphql type:`, typeof admin.graphql);

    // Create a test order using the admin.graphql client
    try {
      const order = await createTestOrderWithAdmin(shop, admin);

      if (order) {
        if (statusCallback) statusCallback(`Successfully created test order #${order.name} for ${shop}`);
        console.log(`[TEST ORDERS] Successfully created test order #${order.name} for ${shop}`);
      } else {
        if (statusCallback) statusCallback(`Failed to create test order for ${shop}`);
        console.log(`[TEST ORDERS] No order was created for shop: ${shop}`);
      }

      return order;
    } catch (orderError) {
      console.error(`[TEST ORDERS ERROR] Failed to create test order for shop ${shop}: ${orderError.message}`, orderError);
      throw new Error(`Failed to create test order: ${orderError.message}`);
    }
  } catch (error) {
    if (statusCallback) statusCallback(`Error creating test order for ${shop}: ${error.message}`);
    console.error(`[TEST ORDERS ERROR] Error creating test order for ${shop}:`, error.message);
    return null;
  }
}

/**
 * Creates a test order with random products for a shop using admin.graphql
 * @param {string} shop - The shop domain
 * @param {object} admin - The admin API client from authenticate.admin
 * @returns {Promise<object|null>} - The created order or null if failed
 */
async function createTestOrderWithAdmin(shop, admin) {
  try {
    console.log(`[TEST ORDERS] Starting test order creation with admin.graphql for shop: ${shop}`);

    // First, get the total count of products
    console.log(`[TEST ORDERS] Fetching product count for shop: ${shop}`);

    const countQuery = `
      query {
        productsCount {
          count
        }
      }
    `;

    console.log(`[TEST ORDERS] Executing product count query for shop: ${shop}`);
    const countResponse = await admin.graphql(countQuery);
    const countData = await countResponse.json();

    if (!countData.data || !countData.data.productsCount || !countData.data.productsCount.count) {
      console.error(`[TEST ORDERS ERROR] Invalid product count response for shop: ${shop}`);
      return null;
    }

    const totalProducts = countData.data.productsCount.count;

    if (totalProducts === 0) {
      console.log(`[TEST ORDERS] Shop ${shop} has no products`);
      return null;
    }

    console.log(`[TEST ORDERS] Shop ${shop} has ${totalProducts} total products`);

    // Determine how many products to fetch (up to 50)
    const fetchLimit = Math.min(50, totalProducts);

    // Since we can't use offset with the GraphQL API, we'll use a random sort key
    // and randomly decide whether to reverse the order to get a more diverse selection
    const sortKeys = ["CREATED_AT", "ID", "INVENTORY_TOTAL", "PRODUCT_TYPE", "PUBLISHED_AT", "TITLE", "UPDATED_AT", "VENDOR"];
    const randomSortKey = sortKeys[Math.floor(Math.random() * sortKeys.length)];
    const randomReverse = Math.random() > 0.5;

    console.log(`[TEST ORDERS] Will fetch up to ${fetchLimit} products for shop ${shop} using sort key ${randomSortKey} with reverse=${randomReverse}`);

    // Query for products using admin.graphql with cursor-based pagination and random sorting
    const productsQuery = `
      query($fetchLimit: Int!, $sortKey: ProductSortKeys!, $reverse: Boolean!) {
        products(first: $fetchLimit, sortKey: $sortKey, reverse: $reverse) {
          edges {
            node {
              id
              title
              variants(first: 5) {
                edges {
                  node {
                    id
                    title
                    price
                    availableForSale
                  }
                }
              }
            }
          }
        }
      }
    `;

    const variables = {
      fetchLimit,
      sortKey: randomSortKey,
      reverse: randomReverse
    };

    console.log(`[TEST ORDERS] Products query with variables:`, { query: productsQuery, variables });

    console.log(`[TEST ORDERS] Executing GraphQL products query for shop: ${shop}`);
    const productsResponse = await admin.graphql(productsQuery, { variables });
    console.log(`[TEST ORDERS] Got GraphQL products response:`, !!productsResponse);
    const productsData = await productsResponse.json();
    console.log(`[TEST ORDERS] Successfully parsed products response JSON`);

    console.log(`[TEST ORDERS] Products query response received for shop: ${shop}`);

    if (!productsData.data || !productsData.data.products || !productsData.data.products.edges) {
      console.error(`[TEST ORDERS ERROR] Invalid products response for shop: ${shop}`);
      return null;
    }

    const products = productsData.data.products.edges.map(edge => {
      const product = edge.node;

      // Only include variants that are available for sale
      const variants = product.variants.edges
        .map(variantEdge => variantEdge.node)
        .filter(variant => variant.availableForSale === true);

      if (variants.length === 0) {
        return null; // Skip products with no available variants
      }

      return {
        id: product.id,
        title: product.title,
        variants
      };
    }).filter(product => product !== null);

    if (products.length === 0) {
      console.error(`[TEST ORDERS ERROR] No available products found for shop: ${shop}`);
      return null;
    }

    console.log(`[TEST ORDERS] Found ${products.length} products for shop: ${shop}`);

    // Shuffle the products array to ensure randomness even if we don't have many products
    for (let i = products.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [products[i], products[j]] = [products[j], products[i]];
    }

    // Select 1-3 random products
    const numProducts = Math.min(Math.floor(Math.random() * 3) + 1, products.length);
    const selectedProducts = [];

    for (let i = 0; i < numProducts; i++) {
      const randomIndex = Math.floor(Math.random() * products.length);
      const product = products[randomIndex];

      // Select a random variant
      const variant = product.variants[Math.floor(Math.random() * product.variants.length)];

      // Random quantity between 1 and 3
      const quantity = Math.floor(Math.random() * 3) + 1;

      selectedProducts.push({
        variantId: variant.id,
        quantity: quantity
      });
    }

    console.log(`[TEST ORDERS] Selected ${selectedProducts.length} products for test order`);

    // Create a unique order ID for tracking
    const testOrderId = `test-${randomUUID()}`;

    // Create the order using GraphQL mutation
    const orderCreateMutation = `
      mutation orderCreate($order: OrderCreateOrderInput!) {
        orderCreate(order: $order) {
          order {
            id
            legacyResourceId
            name
            test
            totalPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const orderInput = {
      note: "Test order created by automated script - " + testOrderId,
      email: "<EMAIL>",
      shippingAddress: {
        firstName: "Test",
        lastName: "Customer",
        address1: "123 Test St",
        city: "Test City",
        province: "AL",
        zip: "12345",
        country: "US"
      },
      lineItems: selectedProducts,
      tags: ["test-order", "automated"],
      customAttributes: [
        {
          key: "test_order",
          value: "true"
        }
      ],
      test: true
    };

    const orderVariables = {
      order: orderInput
    };

    console.log(`[TEST ORDERS] Sending order creation mutation for shop: ${shop}`);

    console.log(`[TEST ORDERS] Executing GraphQL order creation mutation for shop: ${shop}`);
    console.log(`[TEST ORDERS] Order variables:`, JSON.stringify(orderVariables));

    // Execute the GraphQL mutation
    const orderResponse = await admin.graphql(
      orderCreateMutation,
      {
        variables: orderVariables
      }
    );
    console.log(`[TEST ORDERS] Got GraphQL order creation response:`, !!orderResponse);

    const orderData = await orderResponse.json();
    console.log(`[TEST ORDERS] Successfully parsed order creation response JSON`);

    if (orderData.errors) {
      console.error(`[TEST ORDERS ERROR] GraphQL errors creating test order for ${shop}:`, orderData.errors);
      return null;
    }

    if (orderData.data.orderCreate.userErrors && orderData.data.orderCreate.userErrors.length > 0) {
      console.error(`[TEST ORDERS ERROR] User errors creating test order for ${shop}:`, orderData.data.orderCreate.userErrors);
      return null;
    }

    const order = orderData.data.orderCreate.order;
    console.log(`[TEST ORDERS] Created test order #${order.name} for ${shop}`);

    return order;
  } catch (error) {
    console.error(`[TEST ORDERS ERROR] Error creating test order for ${shop}:`, error.message);
    return null;
  }
}

/**
 * Start creating test orders at regular intervals using the admin object
 * @param {string} shop - The shop domain
 * @param {object} admin - The admin API client from authenticate.admin
 * @param {number} durationMinutes - How long to run in minutes (0 for indefinite)
 * @param {Function} statusCallback - Optional callback to report status updates
 * @returns {Promise<void>}
 */
export async function startTestOrderCreationWithAdmin(shop, admin, durationMinutes = 0, statusCallback = null) {
  console.log(`[TEST ORDERS] startTestOrderCreationWithAdmin called for shop: ${shop}, duration: ${durationMinutes} minutes`);

  // Don't start if already running
  if (isRunning) {
    const message = "Test order creation is already running";
    if (statusCallback) statusCallback(message);
    console.log(`[TEST ORDERS] ${message}`);
    return;
  }

  isRunning = true;
  shouldStop = false;

  const startMessage = `Starting test order creation for ${shop} for ${durationMinutes > 0 ? durationMinutes + ' minutes' : 'indefinite time'}`;
  if (statusCallback) statusCallback(startMessage);
  console.log(`[TEST ORDERS] ${startMessage}`);

  try {
    // Set up end time if duration is specified
    const endTime = durationMinutes > 0 ? Date.now() + (durationMinutes * 60 * 1000) : 0;
    console.log(`[TEST ORDERS] End time set to: ${endTime > 0 ? new Date(endTime).toISOString() : 'indefinite'}`);

    // Create initial order immediately
    console.log(`[TEST ORDERS] Creating initial test order for shop: ${shop}`);
    try {
      const initialOrder = await createTestOrderWithAdmin(shop, admin);
      if (initialOrder) {
        console.log(`[TEST ORDERS] Successfully created initial test order #${initialOrder.name} for ${shop}`);
        if (statusCallback) statusCallback(`Created initial test order #${initialOrder.name}`);
      } else {
        console.log(`[TEST ORDERS] Failed to create initial test order for ${shop}`);
        if (statusCallback) statusCallback(`Failed to create initial test order`);
      }
    } catch (initialOrderError) {
      console.error(`[TEST ORDERS ERROR] Error creating initial test order: ${initialOrderError.message}`, initialOrderError);
      if (statusCallback) statusCallback(`Error creating initial test order: ${initialOrderError.message}`);
    }

    // Set up interval for subsequent orders
    console.log(`[TEST ORDERS] Setting up order creation interval (${INTERVAL_MS}ms)`);
    const orderInterval = setInterval(async () => {
      console.log(`[TEST ORDERS] Order interval triggered at ${new Date().toISOString()}`);

      if (shouldStop || (endTime > 0 && Date.now() >= endTime)) {
        console.log(`[TEST ORDERS] Stopping order interval - shouldStop: ${shouldStop}, endTime reached: ${endTime > 0 && Date.now() >= endTime}`);
        clearInterval(orderInterval);
        isRunning = false;
        const stopMessage = `Test order creation stopped ${durationMinutes > 0 ? `after ${durationMinutes} minutes` : ''}`;
        if (statusCallback) statusCallback(stopMessage);
        console.log(`[TEST ORDERS] ${stopMessage}`);
        return;
      }

      try {
        console.log(`[TEST ORDERS] Creating test order for shop: ${shop}`);
        const order = await createTestOrderWithAdmin(shop, admin);
        if (order) {
          console.log(`[TEST ORDERS] Successfully created test order #${order.name} for ${shop}`);
          if (statusCallback) statusCallback(`Created test order #${order.name}`);
        } else {
          console.log(`[TEST ORDERS] Failed to create test order for ${shop}`);
          if (statusCallback) statusCallback(`Failed to create test order`);
        }
      } catch (orderError) {
        console.error(`[TEST ORDERS ERROR] Error creating test order: ${orderError.message}`, orderError);
        if (statusCallback) statusCallback(`Error creating test order: ${orderError.message}`);
      }
    }, INTERVAL_MS);

    // If duration is specified, set up a timeout to stop
    if (durationMinutes > 0) {
      console.log(`[TEST ORDERS] Setting up scheduled stop after ${durationMinutes} minutes`);
      setTimeout(() => {
        shouldStop = true;
        const scheduleStopMessage = `Scheduled stop after ${durationMinutes} minutes`;
        if (statusCallback) statusCallback(scheduleStopMessage);
        console.log(`[TEST ORDERS] ${scheduleStopMessage}`);
      }, durationMinutes * 60 * 1000);
    }

    const runningMessage = `Test order creation running for ${shop}. Creating test orders every ${INTERVAL_MS / 1000} seconds.`;
    if (statusCallback) statusCallback(runningMessage);
    console.log(`[TEST ORDERS] ${runningMessage}`);
  } catch (error) {
    isRunning = false;
    const errorMessage = `Error starting test order creation: ${error.message}`;
    if (statusCallback) statusCallback(errorMessage);
    console.error(`[TEST ORDERS ERROR] ${errorMessage}`, error);
    // Re-throw the error so the caller can handle it
    throw error;
  }
}

// Export all functions as a default export as well
export default {
  startTestOrderCreation,
  stopTestOrderCreation,
  isTestOrderCreationRunning,
  createSingleTestOrder,
  createSingleTestOrderWithAdmin,
  startTestOrderCreationWithAdmin
};
