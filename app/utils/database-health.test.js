/**
 * Database Health Check Tests
 *
 * Tests for the DatabaseHealthChecker utility.
 */

import { jest } from '@jest/globals';
import { DatabaseHealth<PERSON><PERSON><PERSON>, quickHealthCheck, comprehensiveHealthCheck } from './database-health.js';

// Mock Prisma client
const mockPrisma = {
  $queryRaw: jest.fn(),
  session: {
    findFirst: jest.fn(),
    count: jest.fn()
  },
  invoiceBalance: {
    findFirst: jest.fn(),
    findMany: jest.fn(),
    count: jest.fn()
  },
  shippingCost: {
    findFirst: jest.fn(),
    findMany: jest.fn(),
    count: jest.fn()
  },
  reconciliationJob: {
    findFirst: jest.fn(),
    count: jest.fn()
  },
  processedWebhook: {
    findFirst: jest.fn(),
    count: jest.fn()
  },
  // Add missing table models for migration readiness tests
  setupFlag: {
    findFirst: jest.fn()
  },
  balanceGroup: {
    findFirst: jest.fn()
  },
  invoiceTransaction: {
    findFirst: jest.fn()
  },
  shippingTransaction: {
    findFirst: jest.fn()
  },
  shipStationStoreMapping: {
    findFirst: jest.fn()
  },
  shipStationOrderTracking: {
    findFirst: jest.fn()
  },
  price: {
    findFirst: jest.fn()
  },
  unprocessable: {
    findFirst: jest.fn()
  },
  variantFulfillmentService: {
    findFirst: jest.fn()
  }
};

// Mock container
jest.mock('../lib/container/ServiceContainer.js', () => ({
  container: {
    resolve: jest.fn(() => mockPrisma)
  }
}));

describe('DatabaseHealthChecker', () => {
  let healthChecker;

  beforeEach(() => {
    healthChecker = new DatabaseHealthChecker(mockPrisma);
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('checkConnection', () => {
    it('should return healthy status for successful connection', async () => {
      // Mock with a small delay to ensure responseTime > 0
      mockPrisma.$queryRaw.mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve([{ test: 1 }]), 1))
      );

      const result = await healthChecker.checkConnection();

      expect(result.status).toBe('healthy');
      expect(result.responseTime).toBeGreaterThan(0);
      expect(result.message).toBe('Database connection successful');
    });

    it('should return unhealthy status for failed connection', async () => {
      mockPrisma.$queryRaw.mockRejectedValue(new Error('Connection failed'));

      const result = await healthChecker.checkConnection();

      expect(result.status).toBe('unhealthy');
      expect(result.error).toBe('Connection failed');
    });
  });

  describe('checkQueryPerformance', () => {
    it('should check performance of sample queries', async () => {
      mockPrisma.session.findFirst.mockResolvedValue({ id: 'test' });
      mockPrisma.session.count.mockResolvedValue(10);
      mockPrisma.invoiceBalance.findMany.mockResolvedValue([]);

      const result = await healthChecker.checkQueryPerformance();

      expect(result.simple_select).toBeDefined();
      expect(result.count_sessions).toBeDefined();
      expect(result.recent_invoices).toBeDefined();

      expect(result.simple_select.status).toBe('healthy');
      expect(result.count_sessions.status).toBe('healthy');
      expect(result.recent_invoices.status).toBe('healthy');
    });

    it('should mark slow queries appropriately', async () => {
      // Mock slow query
      mockPrisma.session.findFirst.mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve({ id: 'test' }), 1100))
      );
      mockPrisma.session.count.mockResolvedValue(10);
      mockPrisma.invoiceBalance.findMany.mockResolvedValue([]);

      const result = await healthChecker.checkQueryPerformance();

      expect(result.simple_select.status).toBe('slow');
      expect(result.simple_select.responseTime).toBeGreaterThan(1000);
    });

    it('should handle query failures', async () => {
      mockPrisma.session.findFirst.mockRejectedValue(new Error('Query failed'));
      mockPrisma.session.count.mockResolvedValue(10);
      mockPrisma.invoiceBalance.findMany.mockResolvedValue([]);

      const result = await healthChecker.checkQueryPerformance();

      expect(result.simple_select.status).toBe('failed');
      expect(result.simple_select.error).toBe('Query failed');
    });
  });

  describe('checkTableAccess', () => {
    it('should check accessibility of all tables', async () => {
      // Mock all table access methods
      mockPrisma.session.findFirst.mockResolvedValue({ id: 'test' });
      mockPrisma.invoiceBalance.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.shippingCost.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.reconciliationJob.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.processedWebhook.findFirst.mockResolvedValue({ id: 1 });

      const result = await healthChecker.checkTableAccess();

      expect(result.Session.status).toBe('accessible');
      expect(result.InvoiceBalance.status).toBe('accessible');
      expect(result.ShippingCost.status).toBe('accessible');
      expect(result.ReconciliationJob.status).toBe('accessible');
      expect(result.ProcessedWebhook.status).toBe('accessible');
    });

    it('should handle inaccessible tables', async () => {
      mockPrisma.session.findFirst.mockRejectedValue(new Error('Table not found'));

      const result = await healthChecker.checkTableAccess();

      expect(result.Session.status).toBe('inaccessible');
      expect(result.Session.error).toBe('Table not found');
    });
  });

  describe('collectMetrics', () => {
    it('should collect database metrics', async () => {
      // Reset all mocks first
      jest.clearAllMocks();

      // Set up count mocks for basic counts
      mockPrisma.session.count.mockResolvedValue(5);
      mockPrisma.invoiceBalance.count.mockResolvedValue(100);
      mockPrisma.shippingCost.count.mockResolvedValue(50);
      mockPrisma.reconciliationJob.count.mockResolvedValue(10);
      mockPrisma.processedWebhook.count.mockResolvedValue(200);

      // Set up additional count calls for recent activity
      mockPrisma.reconciliationJob.count
        .mockResolvedValueOnce(10) // Total jobs
        .mockResolvedValueOnce(2); // Recent jobs
      mockPrisma.processedWebhook.count
        .mockResolvedValueOnce(200) // Total webhooks
        .mockResolvedValueOnce(15); // Recent webhooks

      const result = await healthChecker.collectMetrics();

      expect(result.tableCounts.sessions).toBe(5);
      expect(result.tableCounts.invoiceBalances).toBe(100);
      expect(result.tableCounts.shippingCosts).toBe(50);
      expect(result.tableCounts.reconciliationJobs).toBe(10);
      expect(result.tableCounts.processedWebhooks).toBe(200);
      expect(result.recentActivity.jobsLast24h).toBe(2);
      expect(result.recentActivity.webhooksLast24h).toBe(15);
      expect(result.totalRecords).toBe(365);
    });

    it('should handle metrics collection errors', async () => {
      mockPrisma.session.count.mockRejectedValue(new Error('Metrics failed'));

      const result = await healthChecker.collectMetrics();

      expect(result.error).toContain('Failed to collect metrics');
    });
  });

  describe('performHealthCheck', () => {
    it('should perform comprehensive health check', async () => {
      // Reset all mocks first
      jest.clearAllMocks();

      // Mock all successful responses
      mockPrisma.$queryRaw.mockResolvedValue([{ test: 1 }]);
      mockPrisma.session.findFirst.mockResolvedValue({ id: 'test' });
      mockPrisma.session.count.mockResolvedValue(5);
      mockPrisma.invoiceBalance.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.invoiceBalance.findMany.mockResolvedValue([]);
      mockPrisma.invoiceBalance.count.mockResolvedValue(100);
      mockPrisma.shippingCost.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.shippingCost.findMany.mockResolvedValue([]);
      mockPrisma.shippingCost.count.mockResolvedValue(50);
      mockPrisma.reconciliationJob.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.reconciliationJob.count.mockResolvedValue(10);
      mockPrisma.processedWebhook.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.processedWebhook.count.mockResolvedValue(200);

      const result = await healthChecker.performHealthCheck();

      expect(result.status).toBe('healthy');
      expect(result.checks.connection).toBeDefined();
      expect(result.checks.queryPerformance).toBeDefined();
      expect(result.checks.tableAccess).toBeDefined();
      expect(result.metrics).toBeDefined();
      expect(result.duration).toBeGreaterThan(0);
    });

    it('should handle health check failures', async () => {
      mockPrisma.$queryRaw.mockRejectedValue(new Error('Health check failed'));

      const result = await healthChecker.performHealthCheck();

      expect(result.status).toBe('unhealthy');
      expect(result.errors).toBeDefined();
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('determineOverallStatus', () => {
    it('should return healthy for all healthy checks', () => {
      const checks = {
        connection: { status: 'healthy' },
        queryPerformance: { simple_select: { status: 'healthy' } }
      };

      const status = healthChecker.determineOverallStatus(checks);

      expect(status).toBe('healthy');
    });

    it('should return unhealthy for any failed checks', () => {
      const checks = {
        connection: { status: 'unhealthy' },
        queryPerformance: { simple_select: { status: 'healthy' } }
      };

      const status = healthChecker.determineOverallStatus(checks);

      expect(status).toBe('unhealthy');
    });

    it('should return degraded for slow performance', () => {
      const checks = {
        connection: { status: 'healthy' },
        queryPerformance: { simple_select: { status: 'slow' } }
      };

      const status = healthChecker.determineOverallStatus(checks);

      expect(status).toBe('degraded');
    });
  });

  describe('testMigrationReadiness', () => {
    it('should test migration readiness', async () => {
      // Mock all tables as accessible
      mockPrisma.session.findFirst.mockResolvedValue({ id: 'test' });
      mockPrisma.setupFlag.findFirst.mockResolvedValue({ shop: 'test' });
      mockPrisma.balanceGroup.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.invoiceBalance.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.invoiceTransaction.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.shippingCost.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.shippingTransaction.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.shipStationStoreMapping.findFirst.mockResolvedValue({ storeId: 'test' });
      mockPrisma.shipStationOrderTracking.findFirst.mockResolvedValue({ storeId: 'test' });
      mockPrisma.price.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.unprocessable.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.processedWebhook.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.reconciliationJob.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.variantFulfillmentService.findFirst.mockResolvedValue({ variantId: 'test' });

      const result = await healthChecker.testMigrationReadiness();

      expect(result.ready).toBe(true);
      expect(result.issues).toHaveLength(0);
    });

    it('should detect missing tables', async () => {
      // Mock missing table
      mockPrisma.session.findFirst.mockResolvedValue({ id: 'test' });
      // Don't mock setupFlag to simulate missing table

      const result = await healthChecker.testMigrationReadiness();

      expect(result.ready).toBe(false);
      expect(result.issues.length).toBeGreaterThan(0);
    });
  });

  describe('quickHealthCheck', () => {
    it('should perform quick health check', async () => {
      // Mock with a small delay to ensure responseTime > 0
      mockPrisma.$queryRaw.mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve([{ test: 1 }]), 1))
      );

      const result = await healthChecker.quickHealthCheck();

      expect(result.status).toBe('healthy');
      expect(result.responseTime).toBeGreaterThan(0);
      expect(result.timestamp).toBeInstanceOf(Date);
    });
  });
});

describe('Standalone functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('quickHealthCheck', () => {
    it('should perform quick health check', async () => {
      // Mock with a small delay to ensure responseTime > 0
      mockPrisma.$queryRaw.mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve([{ test: 1 }]), 1))
      );

      const result = await quickHealthCheck(mockPrisma);

      expect(result.status).toBe('healthy');
    });
  });

  describe('comprehensiveHealthCheck', () => {
    it('should perform comprehensive health check', async () => {
      // Reset all mocks first
      jest.clearAllMocks();

      // Mock successful responses
      mockPrisma.$queryRaw.mockResolvedValue([{ test: 1 }]);
      mockPrisma.session.findFirst.mockResolvedValue({ id: 'test' });
      mockPrisma.session.count.mockResolvedValue(5);
      mockPrisma.invoiceBalance.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.invoiceBalance.findMany.mockResolvedValue([]);
      mockPrisma.invoiceBalance.count.mockResolvedValue(100);
      mockPrisma.shippingCost.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.shippingCost.findMany.mockResolvedValue([]);
      mockPrisma.shippingCost.count.mockResolvedValue(50);
      mockPrisma.reconciliationJob.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.reconciliationJob.count.mockResolvedValue(10);
      mockPrisma.processedWebhook.findFirst.mockResolvedValue({ id: 1 });
      mockPrisma.processedWebhook.count.mockResolvedValue(200);

      const result = await comprehensiveHealthCheck(mockPrisma);

      expect(result.status).toBe('healthy');
      expect(result.checks).toBeDefined();
      expect(result.metrics).toBeDefined();
    });
  });
});
