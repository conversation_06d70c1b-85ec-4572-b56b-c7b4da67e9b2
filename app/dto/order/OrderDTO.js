/**
 * Order Data Transfer Object
 * 
 * This DTO standardizes order data structure across the application.
 */

export class OrderDTO {
  constructor(data = {}) {
    this.id = data.id || null;
    this.shopifyOrderId = data.shopifyOrderId || data.order_id || null;
    this.orderNumber = data.orderNumber || data.order_number || data.name || null;
    this.shop = data.shop || null;
    this.email = data.email || null;
    this.phone = data.phone || null;
    this.createdAt = this.parseDate(data.createdAt || data.created_at);
    this.updatedAt = this.parseDate(data.updatedAt || data.updated_at);
    this.processedAt = this.parseDate(data.processedAt || data.processed_at);
    this.cancelledAt = this.parseDate(data.cancelledAt || data.cancelled_at);
    this.closedAt = this.parseDate(data.closedAt || data.closed_at);
    
    // Financial information
    this.currency = data.currency || 'USD';
    this.totalPrice = this.parseDecimal(data.totalPrice || data.total_price);
    this.subtotalPrice = this.parseDecimal(data.subtotalPrice || data.subtotal_price);
    this.totalTax = this.parseDecimal(data.totalTax || data.total_tax);
    this.totalDiscounts = this.parseDecimal(data.totalDiscounts || data.total_discounts);
    this.totalShipping = this.parseDecimal(data.totalShipping || data.total_shipping);
    
    // Status information
    this.financialStatus = data.financialStatus || data.financial_status || null;
    this.fulfillmentStatus = data.fulfillmentStatus || data.fulfillment_status || null;
    this.orderStatus = data.orderStatus || data.order_status || null;
    this.confirmed = data.confirmed !== undefined ? Boolean(data.confirmed) : true;
    this.test = data.test !== undefined ? Boolean(data.test) : false;
    
    // Customer information
    this.customer = this.parseCustomer(data.customer);
    
    // Shipping information
    this.shippingAddress = this.parseAddress(data.shippingAddress || data.shipping_address);
    this.billingAddress = this.parseAddress(data.billingAddress || data.billing_address);
    
    // Line items
    this.lineItems = this.parseLineItems(data.lineItems || data.line_items || []);
    
    // Fulfillments
    this.fulfillments = this.parseFulfillments(data.fulfillments || []);
    
    // Tags and notes
    this.tags = data.tags || '';
    this.note = data.note || '';
    this.noteAttributes = data.noteAttributes || data.note_attributes || [];
    
    // Source information
    this.sourceName = data.sourceName || data.source_name || null;
    this.referringSite = data.referringSite || data.referring_site || null;
    this.landingSite = data.landingSite || data.landing_site || null;
    
    // Processing metadata
    this.processingNotes = data.processingNotes || [];
    this.errors = data.errors || [];
    this.warnings = data.warnings || [];
  }

  /**
   * Parse date string or object to Date
   * @param {string|Date|null} dateValue - Date value to parse
   * @returns {Date|null} - Parsed date or null
   */
  parseDate(dateValue) {
    if (!dateValue) return null;
    if (dateValue instanceof Date) return dateValue;
    
    try {
      const parsed = new Date(dateValue);
      return isNaN(parsed.getTime()) ? null : parsed;
    } catch {
      return null;
    }
  }

  /**
   * Parse decimal value
   * @param {string|number|null} value - Value to parse
   * @returns {number} - Parsed decimal value
   */
  parseDecimal(value) {
    if (value === null || value === undefined || value === '') return 0;
    const parsed = parseFloat(value);
    return isNaN(parsed) ? 0 : parsed;
  }

  /**
   * Parse customer information
   * @param {object|null} customer - Customer data
   * @returns {object|null} - Parsed customer object
   */
  parseCustomer(customer) {
    if (!customer) return null;
    
    return {
      id: customer.id || null,
      email: customer.email || null,
      firstName: customer.firstName || customer.first_name || '',
      lastName: customer.lastName || customer.last_name || '',
      phone: customer.phone || null,
      acceptsMarketing: Boolean(customer.acceptsMarketing || customer.accepts_marketing),
      createdAt: this.parseDate(customer.createdAt || customer.created_at),
      updatedAt: this.parseDate(customer.updatedAt || customer.updated_at)
    };
  }

  /**
   * Parse address information
   * @param {object|null} address - Address data
   * @returns {object|null} - Parsed address object
   */
  parseAddress(address) {
    if (!address) return null;
    
    return {
      firstName: address.firstName || address.first_name || '',
      lastName: address.lastName || address.last_name || '',
      company: address.company || '',
      address1: address.address1 || address.address_1 || '',
      address2: address.address2 || address.address_2 || '',
      city: address.city || '',
      province: address.province || address.province_code || '',
      country: address.country || address.country_code || '',
      zip: address.zip || address.postal_code || '',
      phone: address.phone || ''
    };
  }

  /**
   * Parse line items
   * @param {Array} lineItems - Line items array
   * @returns {Array} - Parsed line items
   */
  parseLineItems(lineItems) {
    if (!Array.isArray(lineItems)) return [];
    
    return lineItems.map(item => ({
      id: item.id || null,
      variantId: item.variantId || item.variant_id || null,
      productId: item.productId || item.product_id || null,
      title: item.title || '',
      variantTitle: item.variantTitle || item.variant_title || '',
      sku: item.sku || '',
      vendor: item.vendor || '',
      productType: item.productType || item.product_type || '',
      quantity: parseInt(item.quantity) || 0,
      price: this.parseDecimal(item.price),
      totalDiscount: this.parseDecimal(item.totalDiscount || item.total_discount),
      fulfillmentService: item.fulfillmentService || item.fulfillment_service || 'manual',
      fulfillmentStatus: item.fulfillmentStatus || item.fulfillment_status || null,
      requiresShipping: Boolean(item.requiresShipping || item.requires_shipping),
      taxable: Boolean(item.taxable),
      grams: parseInt(item.grams) || 0,
      properties: item.properties || []
    }));
  }

  /**
   * Parse fulfillments
   * @param {Array} fulfillments - Fulfillments array
   * @returns {Array} - Parsed fulfillments
   */
  parseFulfillments(fulfillments) {
    if (!Array.isArray(fulfillments)) return [];
    
    return fulfillments.map(fulfillment => ({
      id: fulfillment.id || null,
      status: fulfillment.status || '',
      createdAt: this.parseDate(fulfillment.createdAt || fulfillment.created_at),
      updatedAt: this.parseDate(fulfillment.updatedAt || fulfillment.updated_at),
      trackingCompany: fulfillment.trackingCompany || fulfillment.tracking_company || '',
      trackingNumber: fulfillment.trackingNumber || fulfillment.tracking_number || '',
      trackingUrl: fulfillment.trackingUrl || fulfillment.tracking_url || '',
      lineItems: this.parseLineItems(fulfillment.lineItems || fulfillment.line_items || [])
    }));
  }

  /**
   * Check if order is test order
   * @returns {boolean} - True if test order
   */
  isTestOrder() {
    return this.test === true;
  }

  /**
   * Check if order is cancelled
   * @returns {boolean} - True if cancelled
   */
  isCancelled() {
    return this.cancelledAt !== null;
  }

  /**
   * Check if order is closed
   * @returns {boolean} - True if closed
   */
  isClosed() {
    return this.closedAt !== null;
  }

  /**
   * Check if order is paid
   * @returns {boolean} - True if paid
   */
  isPaid() {
    return this.financialStatus === 'paid';
  }

  /**
   * Check if order is fulfilled
   * @returns {boolean} - True if fulfilled
   */
  isFulfilled() {
    return this.fulfillmentStatus === 'fulfilled';
  }

  /**
   * Get total quantity of items
   * @returns {number} - Total quantity
   */
  getTotalQuantity() {
    return this.lineItems.reduce((total, item) => total + item.quantity, 0);
  }

  /**
   * Get line items that require shipping
   * @returns {Array} - Line items requiring shipping
   */
  getShippableItems() {
    return this.lineItems.filter(item => item.requiresShipping);
  }

  /**
   * Get line items by fulfillment service
   * @param {string} service - Fulfillment service name
   * @returns {Array} - Line items with specified fulfillment service
   */
  getItemsByFulfillmentService(service) {
    return this.lineItems.filter(item => item.fulfillmentService === service);
  }

  /**
   * Get manual fulfillment items
   * @returns {Array} - Line items with manual fulfillment
   */
  getManualFulfillmentItems() {
    return this.getItemsByFulfillmentService('manual');
  }

  /**
   * Add processing note
   * @param {string} note - Processing note
   * @param {string} type - Note type (info, warning, error)
   */
  addProcessingNote(note, type = 'info') {
    this.processingNotes.push({
      note,
      type,
      timestamp: new Date()
    });
  }

  /**
   * Add error
   * @param {string} error - Error message
   * @param {string} field - Field that caused the error
   */
  addError(error, field = null) {
    this.errors.push({
      message: error,
      field,
      timestamp: new Date()
    });
  }

  /**
   * Add warning
   * @param {string} warning - Warning message
   * @param {string} field - Field that caused the warning
   */
  addWarning(warning, field = null) {
    this.warnings.push({
      message: warning,
      field,
      timestamp: new Date()
    });
  }

  /**
   * Check if order has errors
   * @returns {boolean} - True if order has errors
   */
  hasErrors() {
    return this.errors.length > 0;
  }

  /**
   * Check if order has warnings
   * @returns {boolean} - True if order has warnings
   */
  hasWarnings() {
    return this.warnings.length > 0;
  }

  /**
   * Convert to plain object
   * @returns {object} - Plain object representation
   */
  toObject() {
    return {
      id: this.id,
      shopifyOrderId: this.shopifyOrderId,
      orderNumber: this.orderNumber,
      shop: this.shop,
      email: this.email,
      phone: this.phone,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      processedAt: this.processedAt,
      cancelledAt: this.cancelledAt,
      closedAt: this.closedAt,
      currency: this.currency,
      totalPrice: this.totalPrice,
      subtotalPrice: this.subtotalPrice,
      totalTax: this.totalTax,
      totalDiscounts: this.totalDiscounts,
      totalShipping: this.totalShipping,
      financialStatus: this.financialStatus,
      fulfillmentStatus: this.fulfillmentStatus,
      orderStatus: this.orderStatus,
      confirmed: this.confirmed,
      test: this.test,
      customer: this.customer,
      shippingAddress: this.shippingAddress,
      billingAddress: this.billingAddress,
      lineItems: this.lineItems,
      fulfillments: this.fulfillments,
      tags: this.tags,
      note: this.note,
      noteAttributes: this.noteAttributes,
      sourceName: this.sourceName,
      referringSite: this.referringSite,
      landingSite: this.landingSite,
      processingNotes: this.processingNotes,
      errors: this.errors,
      warnings: this.warnings
    };
  }

  /**
   * Create OrderDTO from Shopify order data
   * @param {object} shopifyOrder - Raw Shopify order data
   * @param {string} shop - Shop domain
   * @returns {OrderDTO} - OrderDTO instance
   */
  static fromShopifyOrder(shopifyOrder, shop) {
    return new OrderDTO({
      ...shopifyOrder,
      shop,
      shopifyOrderId: shopifyOrder.id,
      orderNumber: shopifyOrder.name || shopifyOrder.order_number
    });
  }
}
