/**
 * Line Item Data Transfer Object
 * 
 * This DTO standardizes line item data structure across the application.
 */

export class LineItemDTO {
  constructor(data = {}) {
    this.id = data.id || null;
    this.variantId = data.variantId || data.variant_id || null;
    this.productId = data.productId || data.product_id || null;
    this.orderId = data.orderId || data.order_id || null;
    
    // Product information
    this.title = data.title || '';
    this.variantTitle = data.variantTitle || data.variant_title || '';
    this.sku = data.sku || '';
    this.vendor = data.vendor || '';
    this.productType = data.productType || data.product_type || '';
    
    // Quantity and pricing
    this.quantity = parseInt(data.quantity) || 0;
    this.price = this.parseDecimal(data.price);
    this.compareAtPrice = this.parseDecimal(data.compareAtPrice || data.compare_at_price);
    this.totalDiscount = this.parseDecimal(data.totalDiscount || data.total_discount);
    this.linePrice = this.parseDecimal(data.linePrice || data.line_price || (this.price * this.quantity));
    
    // Fulfillment information
    this.fulfillmentService = data.fulfillmentService || data.fulfillment_service || 'manual';
    this.fulfillmentStatus = data.fulfillmentStatus || data.fulfillment_status || null;
    this.requiresShipping = Boolean(data.requiresShipping || data.requires_shipping);
    
    // Tax information
    this.taxable = Boolean(data.taxable);
    this.taxLines = this.parseTaxLines(data.taxLines || data.tax_lines || []);
    
    // Physical properties
    this.grams = parseInt(data.grams) || 0;
    this.weight = this.parseDecimal(data.weight);
    this.weightUnit = data.weightUnit || data.weight_unit || 'kg';
    
    // Custom properties and attributes
    this.properties = this.parseProperties(data.properties || []);
    this.customAttributes = data.customAttributes || data.custom_attributes || [];
    
    // Gift card information
    this.giftCard = Boolean(data.giftCard || data.gift_card);
    
    // Inventory information
    this.inventoryQuantity = parseInt(data.inventoryQuantity || data.inventory_quantity) || 0;
    this.inventoryPolicy = data.inventoryPolicy || data.inventory_policy || 'deny';
    this.inventoryManagement = data.inventoryManagement || data.inventory_management || null;
    
    // Processing metadata
    this.category = data.category || this.determineCategory();
    this.processingNotes = data.processingNotes || [];
    this.errors = data.errors || [];
    this.warnings = data.warnings || [];
    this.skipped = Boolean(data.skipped);
    this.skipReason = data.skipReason || null;
  }

  /**
   * Parse decimal value
   * @param {string|number|null} value - Value to parse
   * @returns {number} - Parsed decimal value
   */
  parseDecimal(value) {
    if (value === null || value === undefined || value === '') return 0;
    const parsed = parseFloat(value);
    return isNaN(parsed) ? 0 : parsed;
  }

  /**
   * Parse tax lines
   * @param {Array} taxLines - Tax lines array
   * @returns {Array} - Parsed tax lines
   */
  parseTaxLines(taxLines) {
    if (!Array.isArray(taxLines)) return [];
    
    return taxLines.map(taxLine => ({
      title: taxLine.title || '',
      price: this.parseDecimal(taxLine.price),
      rate: this.parseDecimal(taxLine.rate),
      priceSet: taxLine.priceSet || taxLine.price_set || null
    }));
  }

  /**
   * Parse properties
   * @param {Array} properties - Properties array
   * @returns {Array} - Parsed properties
   */
  parseProperties(properties) {
    if (!Array.isArray(properties)) return [];
    
    return properties.map(property => ({
      name: property.name || '',
      value: property.value || ''
    }));
  }

  /**
   * Determine product category based on product type and title
   * @returns {string} - Determined category
   */
  determineCategory() {
    const productType = this.productType.toLowerCase();
    const title = this.title.toLowerCase();
    const sku = this.sku.toLowerCase();
    
    // Category determination logic
    if (productType.includes('shirt') || title.includes('shirt') || title.includes('tee')) {
      return 'Shirts';
    }
    
    if (productType.includes('hoodie') || productType.includes('sweatshirt') || 
        title.includes('hoodie') || title.includes('sweatshirt')) {
      return 'Outerwear';
    }
    
    if (productType.includes('sticker') || title.includes('sticker')) {
      return 'Stickers';
    }
    
    if (productType.includes('hat') || productType.includes('cap') || 
        title.includes('hat') || title.includes('cap')) {
      return 'Hats';
    }
    
    if (productType.includes('flag') || title.includes('flag') || sku.startsWith('f')) {
      return 'Flags';
    }
    
    if (productType.includes('patch') || title.includes('patch')) {
      return 'Patches';
    }
    
    if (productType.includes('accessory') || productType.includes('accessories')) {
      return 'Accessories';
    }
    
    // Default category
    return 'Other';
  }

  /**
   * Check if item requires manual fulfillment
   * @returns {boolean} - True if manual fulfillment
   */
  isManualFulfillment() {
    return this.fulfillmentService === 'manual';
  }

  /**
   * Check if item is fulfilled
   * @returns {boolean} - True if fulfilled
   */
  isFulfilled() {
    return this.fulfillmentStatus === 'fulfilled';
  }

  /**
   * Check if item is a gift card
   * @returns {boolean} - True if gift card
   */
  isGiftCard() {
    return this.giftCard;
  }

  /**
   * Check if item is taxable
   * @returns {boolean} - True if taxable
   */
  isTaxable() {
    return this.taxable;
  }

  /**
   * Check if item has been skipped during processing
   * @returns {boolean} - True if skipped
   */
  isSkipped() {
    return this.skipped;
  }

  /**
   * Get total tax amount for this line item
   * @returns {number} - Total tax amount
   */
  getTotalTax() {
    return this.taxLines.reduce((total, taxLine) => total + taxLine.price, 0);
  }

  /**
   * Get line total including tax
   * @returns {number} - Line total with tax
   */
  getLineTotalWithTax() {
    return this.linePrice + this.getTotalTax();
  }

  /**
   * Get unit price after discounts
   * @returns {number} - Unit price after discounts
   */
  getDiscountedUnitPrice() {
    if (this.quantity === 0) return 0;
    return (this.linePrice - this.totalDiscount) / this.quantity;
  }

  /**
   * Get property value by name
   * @param {string} name - Property name
   * @returns {string|null} - Property value or null
   */
  getPropertyValue(name) {
    const property = this.properties.find(prop => prop.name === name);
    return property ? property.value : null;
  }

  /**
   * Check if item has specific property
   * @param {string} name - Property name
   * @returns {boolean} - True if property exists
   */
  hasProperty(name) {
    return this.properties.some(prop => prop.name === name);
  }

  /**
   * Add processing note
   * @param {string} note - Processing note
   * @param {string} type - Note type (info, warning, error)
   */
  addProcessingNote(note, type = 'info') {
    this.processingNotes.push({
      note,
      type,
      timestamp: new Date()
    });
  }

  /**
   * Add error
   * @param {string} error - Error message
   * @param {string} field - Field that caused the error
   */
  addError(error, field = null) {
    this.errors.push({
      message: error,
      field,
      timestamp: new Date()
    });
  }

  /**
   * Add warning
   * @param {string} warning - Warning message
   * @param {string} field - Field that caused the warning
   */
  addWarning(warning, field = null) {
    this.warnings.push({
      message: warning,
      field,
      timestamp: new Date()
    });
  }

  /**
   * Mark item as skipped
   * @param {string} reason - Reason for skipping
   */
  markAsSkipped(reason) {
    this.skipped = true;
    this.skipReason = reason;
    this.addProcessingNote(`Item skipped: ${reason}`, 'warning');
  }

  /**
   * Check if item has errors
   * @returns {boolean} - True if item has errors
   */
  hasErrors() {
    return this.errors.length > 0;
  }

  /**
   * Check if item has warnings
   * @returns {boolean} - True if item has warnings
   */
  hasWarnings() {
    return this.warnings.length > 0;
  }

  /**
   * Check if SKU matches pattern
   * @param {string|RegExp} pattern - Pattern to match
   * @returns {boolean} - True if SKU matches pattern
   */
  skuMatches(pattern) {
    if (pattern instanceof RegExp) {
      return pattern.test(this.sku);
    }
    return this.sku.includes(pattern);
  }

  /**
   * Check if item should be excluded from processing
   * @param {Array} excludePatterns - Array of patterns to exclude
   * @returns {boolean} - True if item should be excluded
   */
  shouldExclude(excludePatterns = []) {
    return excludePatterns.some(pattern => this.skuMatches(pattern));
  }

  /**
   * Convert to plain object
   * @returns {object} - Plain object representation
   */
  toObject() {
    return {
      id: this.id,
      variantId: this.variantId,
      productId: this.productId,
      orderId: this.orderId,
      title: this.title,
      variantTitle: this.variantTitle,
      sku: this.sku,
      vendor: this.vendor,
      productType: this.productType,
      quantity: this.quantity,
      price: this.price,
      compareAtPrice: this.compareAtPrice,
      totalDiscount: this.totalDiscount,
      linePrice: this.linePrice,
      fulfillmentService: this.fulfillmentService,
      fulfillmentStatus: this.fulfillmentStatus,
      requiresShipping: this.requiresShipping,
      taxable: this.taxable,
      taxLines: this.taxLines,
      grams: this.grams,
      weight: this.weight,
      weightUnit: this.weightUnit,
      properties: this.properties,
      customAttributes: this.customAttributes,
      giftCard: this.giftCard,
      inventoryQuantity: this.inventoryQuantity,
      inventoryPolicy: this.inventoryPolicy,
      inventoryManagement: this.inventoryManagement,
      category: this.category,
      processingNotes: this.processingNotes,
      errors: this.errors,
      warnings: this.warnings,
      skipped: this.skipped,
      skipReason: this.skipReason
    };
  }

  /**
   * Create LineItemDTO from Shopify line item data
   * @param {object} shopifyLineItem - Raw Shopify line item data
   * @param {string} orderId - Order ID
   * @returns {LineItemDTO} - LineItemDTO instance
   */
  static fromShopifyLineItem(shopifyLineItem, orderId = null) {
    return new LineItemDTO({
      ...shopifyLineItem,
      orderId
    });
  }
}
