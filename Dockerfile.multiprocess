# Multi-process Dockerfile for Americans United Inc Shopify App
# Supports both Remix and Express servers in production

FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    openssl \
    dumb-init \
    curl \
    bash

# Set working directory
WORKDIR /app

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000
ENV EXPRESS_PORT=4000
ENV INTERNAL_PORT=8080

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Copy package files
COPY package.json package-lock.json* ./

# Install dependencies
RUN npm ci --omit=dev --legacy-peer-deps && npm cache clean --force

# Remove CLI packages since we don't need them in production
RUN npm remove @shopify/cli || true

# Copy application code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build the application
RUN npm run build

# Create necessary directories
RUN mkdir -p /app/logs /app/tmp && \
    chown -R nextjs:nodejs /app

# Copy start script
COPY start.sh /app/start.sh
RUN chmod +x /app/start.sh

# Health check script
COPY infrastructure/scripts/health-check.sh /app/health-check.sh
RUN chmod +x /app/health-check.sh

# Switch to non-root user
USER nextjs

# Expose ports
EXPOSE 3000 4000 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD /app/health-check.sh

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Default command (can be overridden by fly.toml processes)
CMD ["/app/start.sh"]
