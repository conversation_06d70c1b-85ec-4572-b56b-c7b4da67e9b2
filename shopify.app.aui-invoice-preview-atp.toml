# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "566e2a972b89ccf6da72959fcdd53084"
application_url = "https://americans-united-inc.fly.dev"
embedded = true
name = "Americans United Inc."
handle = "americans-united-inc"

[build]
automatically_update_urls_on_dev = true
include_config_on_deploy = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled", "orders/create" ]
  uri = "/webhooks/app"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_orders,read_products"

[auth]
redirect_urls = [
  "https://americans-united-inc.fly.dev/auth/callback",
  "https://americans-united-inc.fly.dev/auth/shopify/callback",
  "https://americans-united-inc.fly.dev/api/auth/callback"
]

[pos]
embedded = false
