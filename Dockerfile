FROM node:18-alpine
RUN apk add --no-cache openssl

EXPOSE 3000

WORKDIR /app

ENV NODE_ENV=production

COPY package.json package-lock.json* ./

RUN npm ci --omit=dev && npm cache clean --force
# Remove CLI packages since we don't need them in production by default.
# Remove this line if you want to run CLI commands in your container.
RUN npm remove @shopify/cli

COPY . .

#! Uncomment these lines to setup your reconciliation cronjobs
# RUN apt-get update
RUN apt-get install -y cron

COPY etc/crontab /app/etc/crontab

COPY /app/utils/scheduled-reconciliation.js /app/utils/scheduled-reconciliation.js

RUN npm run build

CMD ["npm", "run", "docker-start"]
