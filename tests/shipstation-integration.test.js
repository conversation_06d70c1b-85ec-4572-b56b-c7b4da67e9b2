import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { action } from '../app/routes/api.shipstation-webhook';
import db from '../app/db.server';
import axios from 'axios';
import { getStoreMappingByStoreId } from '../app/models/ShipStationStoreMapping.server';

// Keep using the mocks for these modules
// jest.unmock('../app/models/ProcessedWebhook.server');
// jest.unmock('../app/models/ShippingCost.server');

describe('ShipStation Integration Tests', () => {
  // Create a mock request
  const createMockRequest = (payload, headers = {}) => {
    return {
      method: 'POST',
      headers: {
        get: (name) => headers[name] || null,
      },
      json: () => Promise.resolve(payload),
    };
  };

  beforeEach(() => {
    // Reset all mocks before each test
    jest.resetAllMocks();

    // Mock getStoreMappingByStoreId
    getStoreMappingByStoreId.mockResolvedValue({ shop: 'test-shop.myshopify.com' });

    // Mock axios.get to return a successful response with labels
    axios.get.mockResolvedValue({
      data: {
        labels: [
          {
            label_id: '123',
            shipment_cost: {
              amount: '10.50'
            }
          }
        ]
      }
    });

    // Mock database operations
    db.processedWebhook.findFirst.mockResolvedValue(null);
    db.processedWebhook.create.mockResolvedValue({ id: 1 });
    db.shippingCost.findFirst.mockResolvedValue(null);
    db.shippingCost.create.mockResolvedValue({ id: 1 });
    db.shippingTransaction.create.mockResolvedValue({ id: 1 });
    db.invoiceBalance.findFirst.mockResolvedValue(null);
    db.invoiceBalance.create.mockResolvedValue({ id: 1 });
    db.invoiceTransaction.create.mockResolvedValue({ id: 1 });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should process a webhook only once when the same webhook is received multiple times', async () => {
    // Create a webhook payload with direct data to avoid axios mock issues
    const payload = {
      resource_type: 'SHIP_NOTIFY',
      resource_data: {
        shipmentId: '12345',
        storeId: '67890',
        shipmentCost: 15.75
      }
    };

    // First request - should be processed
    const request1 = createMockRequest(payload);
    const response1 = await action({ request: request1 });
    const data1 = await response1.json();

    expect(data1.success).toBe(true);

    // Reset create mocks but make findFirst return a record to simulate already processed
    db.processedWebhook.create.mockClear();
    db.shippingCost.create.mockClear();
    db.invoiceBalance.create.mockClear();
    db.processedWebhook.findFirst.mockResolvedValue({ id: 1 });

    // Second request with the same payload - should be skipped
    const request2 = createMockRequest(payload);
    const response2 = await action({ request: request2 });
    const data2 = await response2.json();

    expect(data2.message).toBe('Webhook already processed');
    expect(db.processedWebhook.create).not.toHaveBeenCalled();
    expect(db.shippingCost.create).not.toHaveBeenCalled();
    expect(db.invoiceBalance.create).not.toHaveBeenCalled();
  });

  it('should handle different webhooks for different shipments', async () => {
    // First webhook with direct data
    const payload1 = {
      resource_type: 'SHIP_NOTIFY',
      resource_data: {
        shipmentId: '12345',
        storeId: '67890',
        shipmentCost: 15.75
      }
    };

    const request1 = createMockRequest(payload1);
    await action({ request: request1 });

    // Reset mocks for the second webhook
    jest.clearAllMocks();
    db.processedWebhook.findFirst.mockResolvedValue(null);

    // Second webhook with different shipment
    const payload2 = {
      resource_type: 'SHIP_NOTIFY',
      resource_data: {
        shipmentId: '67890',
        storeId: '67890',
        shipmentCost: 20.50
      }
    };

    const request2 = createMockRequest(payload2);
    await action({ request: request2 });

    // Verify the second webhook was processed
    expect(db.processedWebhook.findFirst).toHaveBeenCalled();
  });

  it('should handle direct payload processing and still deduplicate', async () => {
    // First webhook with direct payload
    const payload = {
      resource_type: 'SHIP_NOTIFY',
      resource_data: {
        shipmentId: '12345',
        storeId: '67890',
        shipmentCost: 15.75
      }
    };

    // First request - should be processed
    const request1 = createMockRequest(payload);
    await action({ request: request1 });

    expect(db.processedWebhook.create).toHaveBeenCalledTimes(1);
    expect(db.shippingCost.create).toHaveBeenCalledTimes(1);
    expect(db.invoiceBalance.create).toHaveBeenCalledTimes(1);

    // Reset create mocks but make findFirst return a record to simulate already processed
    db.processedWebhook.create.mockClear();
    db.shippingCost.create.mockClear();
    db.invoiceBalance.create.mockClear();
    db.processedWebhook.findFirst.mockResolvedValue({ id: 1 });

    // Second request with the same payload - should be skipped
    const request2 = createMockRequest(payload);
    const response2 = await action({ request: request2 });
    const data2 = await response2.json();

    expect(data2.message).toBe('Webhook already processed');
    expect(db.processedWebhook.create).not.toHaveBeenCalled();
    expect(db.shippingCost.create).not.toHaveBeenCalled();
    expect(db.invoiceBalance.create).not.toHaveBeenCalled();
  });
});
