import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { updateShippingCost, updateFulfillmentCost } from '../app/models/ShippingCost.server';
import db from '../app/db.server';

describe('ShippingCost Model', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.resetAllMocks();

    // Mock Date.now() to return a consistent date for testing
    const mockDate = new Date('2025-05-01T12:00:00Z');
    jest.spyOn(global, 'Date').mockImplementation(() => mockDate);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('updateShippingCost', () => {
    it('should create a new record if none exists', async () => {
      // Mock findFirst to return null (no existing record)
      db.shippingCost.findFirst.mockResolvedValue(null);

      // Mock create to return a new record
      const mockNewRecord = {
        id: 1,
        shop: 'test-shop',
        storeId: '12345',
        month: 4, // May (0-indexed)
        year: 2025,
        quantity: 1,
        shippingCost: 10,
        markupAmount: 1,
        totalAmount: 11,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      db.shippingCost.create.mockResolvedValue(mockNewRecord);

      // Mock transaction creation
      db.shippingTransaction.create.mockResolvedValue({ id: 1 });

      const result = await updateShippingCost('test-shop', '12345', 10, 10, null);

      expect(db.shippingCost.findFirst).toHaveBeenCalledWith({
        where: {
          shop: 'test-shop',
          storeId: '12345',
          month: 4,
          year: 2025
        }
      });

      expect(db.shippingCost.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            shop: 'test-shop',
            storeId: '12345',
            month: 4,
            year: 2025,
            quantity: 1,
            shippingCost: 10,
            markupAmount: 1,
            totalAmount: 11
          })
        })
      );

      expect(db.shippingTransaction.create).toHaveBeenCalled();
      expect(result).toEqual(mockNewRecord);
    });

    it('should update an existing record if one exists', async () => {
      // Mock findFirst to return an existing record
      const mockExistingRecord = {
        id: 1,
        shop: 'test-shop',
        storeId: '12345',
        month: 4,
        year: 2025,
        quantity: 5,
        shippingCost: 50,
        markupAmount: 5,
        totalAmount: 55
      };
      db.shippingCost.findFirst.mockResolvedValue(mockExistingRecord);

      // Mock update to return the updated record
      const mockUpdatedRecord = {
        ...mockExistingRecord,
        quantity: 6,
        shippingCost: 60,
        markupAmount: 6,
        totalAmount: 66,
        updatedAt: new Date()
      };
      db.shippingCost.update.mockResolvedValue(mockUpdatedRecord);

      // Mock transaction creation
      db.shippingTransaction.create.mockResolvedValue({ id: 2 });

      const result = await updateShippingCost('test-shop', '12345', 10, 10, null);

      expect(db.shippingCost.findFirst).toHaveBeenCalled();
      expect(db.shippingCost.update).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { id: 1 },
          data: expect.objectContaining({
            shippingCost: { increment: 10 },
            markupAmount: { increment: 1 },
            totalAmount: { increment: 11 }
          })
        })
      );

      expect(db.shippingTransaction.create).toHaveBeenCalled();
      expect(result).toEqual(mockUpdatedRecord);
    });

    it('should handle invalid inputs', async () => {
      await expect(updateShippingCost(null, '12345', 10, 10, null)).rejects.toThrow();
      await expect(updateShippingCost('test-shop', null, 10, 10, null)).rejects.toThrow();
      await expect(updateShippingCost('test-shop', '12345', 0, 10, null)).rejects.toThrow();
      await expect(updateShippingCost('test-shop', '12345', -10, 10, null)).rejects.toThrow();
      await expect(updateShippingCost('test-shop', '12345', 'not-a-number', 10, null)).rejects.toThrow();
    });
  });

  describe('updateFulfillmentCost', () => {
    it('should create a new record if none exists', async () => {
      // Mock findFirst to return null (no existing record)
      db.invoiceBalance.findFirst.mockResolvedValue(null);

      // Mock create to return a new record
      const mockNewRecord = {
        id: 1,
        shop: 'test-shop',
        category: 'Fulfillment Costs',
        month: 4,
        year: 2025,
        quantity: 1,
        balance: 1.5
      };
      db.invoiceBalance.create.mockResolvedValue(mockNewRecord);

      // Mock transaction creation
      db.invoiceTransaction.create.mockResolvedValue({ id: 1 });

      const result = await updateFulfillmentCost('test-shop', 1.50, null);

      expect(db.invoiceBalance.findFirst).toHaveBeenCalledWith({
        where: {
          shop: 'test-shop',
          category: 'Fulfillment Costs',
          month: 4,
          year: 2025
        }
      });

      expect(db.invoiceBalance.create).toHaveBeenCalledWith({
        data: {
          shop: 'test-shop',
          category: 'Fulfillment Costs',
          month: 4,
          year: 2025,
          quantity: 1,
          balance: 1.5
        }
      });

      expect(db.invoiceTransaction.create).toHaveBeenCalled();
      expect(result).toEqual(mockNewRecord);
    });

    it('should update an existing record if one exists', async () => {
      // Mock findFirst to return an existing record
      const mockExistingRecord = {
        id: 1,
        shop: 'test-shop',
        category: 'Fulfillment Costs',
        month: 4,
        year: 2025,
        quantity: 5,
        balance: 7.5
      };
      db.invoiceBalance.findFirst.mockResolvedValue(mockExistingRecord);

      // Mock update to return the updated record
      const mockUpdatedRecord = {
        ...mockExistingRecord,
        quantity: 6,
        balance: 9
      };
      db.invoiceBalance.update.mockResolvedValue(mockUpdatedRecord);

      // Mock transaction creation
      db.invoiceTransaction.create.mockResolvedValue({ id: 2 });

      const result = await updateFulfillmentCost('test-shop', 1.50, null);

      expect(db.invoiceBalance.findFirst).toHaveBeenCalled();
      expect(db.invoiceBalance.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          quantity: { increment: 1 },
          balance: { increment: 1.5 }
        }
      });

      expect(db.invoiceTransaction.create).toHaveBeenCalled();
      expect(result).toEqual(mockUpdatedRecord);
    });

    it('should handle custom fulfillment cost amount', async () => {
      db.invoiceBalance.findFirst.mockResolvedValue(null);
      db.invoiceBalance.create.mockResolvedValue({
        id: 1,
        shop: 'test-shop',
        category: 'Fulfillment Costs',
        month: 4,
        year: 2025,
        quantity: 1,
        balance: 2.0
      });

      // Mock the transaction creation to avoid the error
      db.invoiceTransaction.create.mockResolvedValue({ id: 2 });

      await updateFulfillmentCost('test-shop', 2.0, null);

      expect(db.invoiceBalance.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            balance: 2.0
          })
        })
      );
    });

    it('should handle invalid inputs', async () => {
      await expect(updateFulfillmentCost(null, 1.50, null)).rejects.toThrow();
      await expect(updateFulfillmentCost('test-shop', 0, null)).rejects.toThrow();
      await expect(updateFulfillmentCost('test-shop', -1.5, null)).rejects.toThrow();
      await expect(updateFulfillmentCost('test-shop', 'not-a-number', null)).rejects.toThrow();
    });
  });
});
