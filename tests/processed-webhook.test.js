import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  hasProcessedWebhook,
  markWebhookProcessed,
  checkAndMarkEventProcessed
} from '../app/models/ProcessedWebhook.server';
import db from '../app/db.server';

describe('ProcessedWebhook Model', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.resetAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('hasProcessedWebhook', () => {
    it('should return false if shop or webhookId is missing', async () => {
      const result1 = await hasProcessedWebhook(null, 'webhook-123');
      const result2 = await hasProcessedWebhook('test-shop', null);
      
      expect(result1).toBe(false);
      expect(result2).toBe(false);
      expect(db.processedWebhook.findFirst).not.toHaveBeenCalled();
    });

    it('should return false if no matching webhook is found', async () => {
      db.processedWebhook.findFirst.mockResolvedValue(null);
      
      const result = await hasProcessedWebhook('test-shop', 'webhook-123');
      
      expect(result).toBe(false);
      expect(db.processedWebhook.findFirst).toHaveBeenCalledWith({
        where: {
          shop: 'test-shop',
          webhookId: 'webhook-123'
        }
      });
    });

    it('should return true if a matching webhook is found', async () => {
      db.processedWebhook.findFirst.mockResolvedValue({
        id: 1,
        shop: 'test-shop',
        webhookId: 'webhook-123',
        processedAt: new Date()
      });
      
      const result = await hasProcessedWebhook('test-shop', 'webhook-123');
      
      expect(result).toBe(true);
      expect(db.processedWebhook.findFirst).toHaveBeenCalledWith({
        where: {
          shop: 'test-shop',
          webhookId: 'webhook-123'
        }
      });
    });

    it('should handle errors and return false', async () => {
      db.processedWebhook.findFirst.mockRejectedValue(new Error('Database error'));
      
      const result = await hasProcessedWebhook('test-shop', 'webhook-123');
      
      expect(result).toBe(false);
    });
  });

  describe('markWebhookProcessed', () => {
    it('should return null if required parameters are missing', async () => {
      const result1 = await markWebhookProcessed(null, 'TOPIC', 'webhook-123');
      const result2 = await markWebhookProcessed('test-shop', null, 'webhook-123');
      const result3 = await markWebhookProcessed('test-shop', 'TOPIC', null);
      
      expect(result1).toBeNull();
      expect(result2).toBeNull();
      expect(result3).toBeNull();
      expect(db.processedWebhook.create).not.toHaveBeenCalled();
    });

    it('should create a processed webhook record', async () => {
      const mockRecord = {
        id: 1,
        shop: 'test-shop',
        topic: 'SHIP_NOTIFY',
        webhookId: 'webhook-123',
        processedAt: new Date()
      };
      
      db.processedWebhook.create.mockResolvedValue(mockRecord);
      
      const result = await markWebhookProcessed('test-shop', 'SHIP_NOTIFY', 'webhook-123');
      
      expect(result).toEqual(mockRecord);
      expect(db.processedWebhook.create).toHaveBeenCalledWith({
        data: {
          shop: 'test-shop',
          topic: 'SHIP_NOTIFY',
          webhookId: 'webhook-123',
          eventId: null,
          orderId: null,
          subscriptionId: null,
          processedAt: expect.any(Date)
        }
      });
    });

    it('should handle unique constraint errors', async () => {
      const error = new Error('Unique constraint failed');
      error.code = 'P2002';
      
      db.processedWebhook.create.mockRejectedValue(error);
      
      const result = await markWebhookProcessed('test-shop', 'SHIP_NOTIFY', 'webhook-123');
      
      expect(result).toBeNull();
    });

    it('should throw other errors', async () => {
      const error = new Error('Database error');
      
      db.processedWebhook.create.mockRejectedValue(error);
      
      await expect(markWebhookProcessed('test-shop', 'SHIP_NOTIFY', 'webhook-123')).rejects.toThrow('Database error');
    });
  });

  describe('checkAndMarkEventProcessed', () => {
    it('should return false if shop or eventId is missing', async () => {
      const result1 = await checkAndMarkEventProcessed(null, 'event-123', 'TOPIC');
      const result2 = await checkAndMarkEventProcessed('test-shop', null, 'TOPIC');
      
      expect(result1).toBe(false);
      expect(result2).toBe(false);
      expect(db.processedWebhook.create).not.toHaveBeenCalled();
    });

    it('should create a record and return false if event not previously processed', async () => {
      db.processedWebhook.create.mockResolvedValue({
        id: 1,
        shop: 'test-shop',
        topic: 'SHIP_NOTIFY',
        webhookId: 'event-event-123',
        eventId: 'event-123',
        processedAt: new Date()
      });
      
      const result = await checkAndMarkEventProcessed('test-shop', 'event-123', 'SHIP_NOTIFY', '12345');
      
      expect(result).toBe(false);
      expect(db.processedWebhook.create).toHaveBeenCalledWith({
        data: {
          shop: 'test-shop',
          topic: 'SHIP_NOTIFY',
          webhookId: 'event-event-123',
          eventId: 'event-123',
          orderId: '12345',
          processedAt: expect.any(Date)
        }
      });
    });

    it('should return true if event was already processed (unique constraint error)', async () => {
      const error = new Error('Unique constraint failed');
      error.code = 'P2002';
      
      db.processedWebhook.create.mockRejectedValue(error);
      
      const result = await checkAndMarkEventProcessed('test-shop', 'event-123', 'SHIP_NOTIFY');
      
      expect(result).toBe(true);
    });

    it('should handle other errors and return false', async () => {
      db.processedWebhook.create.mockRejectedValue(new Error('Database error'));
      
      const result = await checkAndMarkEventProcessed('test-shop', 'event-123', 'SHIP_NOTIFY');
      
      expect(result).toBe(false);
    });
  });
});
