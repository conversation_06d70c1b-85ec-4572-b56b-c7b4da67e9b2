/**
 * Unit Tests for ProcessorFactory
 */

import { jest } from '@jest/globals';
import { ProcessorFactory } from '../../../app/services/order/processors/ProcessorFactory.js';
import { StandardProductProcessor } from '../../../app/services/order/processors/StandardProductProcessor.js';
import { FlagPatchProcessor } from '../../../app/services/order/processors/FlagPatchProcessor.js';
import { CustomProductProcessor } from '../../../app/services/order/processors/CustomProductProcessor.js';
import { BusinessLogicError } from '../../../app/lib/errors/AppError.js';

describe('ProcessorFactory', () => {
  let processorFactory;

  beforeEach(() => {
    processorFactory = new ProcessorFactory();
  });

  describe('constructor', () => {
    it('should initialize with default processors', () => {
      expect(processorFactory.processors.size).toBe(3);
      expect(processorFactory.processors.has('standard')).toBe(true);
      expect(processorFactory.processors.has('flag_patch')).toBe(true);
      expect(processorFactory.processors.has('custom')).toBe(true);
    });
  });

  describe('registerProcessor', () => {
    it('should register a custom processor', () => {
      class TestProcessor {}
      
      processorFactory.registerProcessor('test', TestProcessor);
      
      expect(processorFactory.processors.has('test')).toBe(true);
      expect(processorFactory.processors.get('test')).toBe(TestProcessor);
    });

    it('should override existing processor', () => {
      class NewStandardProcessor {}
      
      processorFactory.registerProcessor('standard', NewStandardProcessor);
      
      expect(processorFactory.processors.get('standard')).toBe(NewStandardProcessor);
    });
  });

  describe('getProcessor', () => {
    it('should return StandardProductProcessor for standard line items', () => {
      const lineItem = {
        sku: 'STANDARD-SKU-001',
        title: 'Standard Product',
        variant_title: 'Size: M',
      };

      const processor = processorFactory.getProcessor(lineItem);
      
      expect(processor).toBeInstanceOf(StandardProductProcessor);
    });

    it('should return FlagPatchProcessor for flag/patch products', () => {
      const lineItem = {
        sku: '*F-FLAG-001',
        title: 'Flag Product',
        variant_title: 'Size: 3x5',
      };

      const processor = processorFactory.getProcessor(lineItem);
      
      expect(processor).toBeInstanceOf(FlagPatchProcessor);
    });

    it('should return CustomProductProcessor for custom products by title', () => {
      const lineItem = {
        sku: 'CUSTOM-001',
        title: 'Custom Embroidered Shirt',
        variant_title: 'Size: L',
      };

      const processor = processorFactory.getProcessor(lineItem);
      
      expect(processor).toBeInstanceOf(CustomProductProcessor);
    });

    it('should return CustomProductProcessor for custom products by variant title', () => {
      const lineItem = {
        sku: 'SHIRT-001',
        title: 'Basic Shirt',
        variant_title: 'Custom Design / Size: L',
      };

      const processor = processorFactory.getProcessor(lineItem);
      
      expect(processor).toBeInstanceOf(CustomProductProcessor);
    });

    it('should return CustomProductProcessor for special SKU patterns', () => {
      const lineItem = {
        sku: 'SPECIAL-CUSTOM-001',
        title: 'Special Product',
        variant_title: 'Size: M',
      };

      // Mock isSpecialSKU to return true
      jest.spyOn(processorFactory, 'isSpecialSKU').mockReturnValue(true);

      const processor = processorFactory.getProcessor(lineItem);
      
      expect(processor).toBeInstanceOf(CustomProductProcessor);
    });

    it('should throw error for unknown processor type', () => {
      // Mock determineProcessorType to return unknown type
      jest.spyOn(processorFactory, 'determineProcessorType').mockReturnValue('unknown');

      const lineItem = {
        sku: 'UNKNOWN-001',
        title: 'Unknown Product',
      };

      expect(() => {
        processorFactory.getProcessor(lineItem);
      }).toThrow(BusinessLogicError);
    });
  });

  describe('determineProcessorType', () => {
    it('should return "flag_patch" for SKUs starting with *F', () => {
      const lineItem = { sku: '*F-FLAG-001' };
      
      const type = processorFactory.determineProcessorType(lineItem);
      
      expect(type).toBe('flag_patch');
    });

    it('should return "custom" for products with "custom" in title', () => {
      const lineItem = { 
        sku: 'SHIRT-001',
        title: 'Custom Embroidered Shirt',
      };
      
      const type = processorFactory.determineProcessorType(lineItem);
      
      expect(type).toBe('custom');
    });

    it('should return "custom" for products with "custom" in variant title', () => {
      const lineItem = { 
        sku: 'SHIRT-001',
        title: 'Basic Shirt',
        variant_title: 'Custom Design',
      };
      
      const type = processorFactory.determineProcessorType(lineItem);
      
      expect(type).toBe('custom');
    });

    it('should return "custom" for special SKU patterns', () => {
      const lineItem = { sku: 'SPECIAL-001' };
      
      jest.spyOn(processorFactory, 'isSpecialSKU').mockReturnValue(true);
      
      const type = processorFactory.determineProcessorType(lineItem);
      
      expect(type).toBe('custom');
    });

    it('should return "standard" for regular products', () => {
      const lineItem = { 
        sku: 'REGULAR-001',
        title: 'Regular Product',
        variant_title: 'Size: M',
      };
      
      const type = processorFactory.determineProcessorType(lineItem);
      
      expect(type).toBe('standard');
    });

    it('should handle missing SKU', () => {
      const lineItem = { 
        title: 'Product without SKU',
      };
      
      const type = processorFactory.determineProcessorType(lineItem);
      
      expect(type).toBe('standard');
    });

    it('should handle missing titles', () => {
      const lineItem = { 
        sku: 'MINIMAL-001',
      };
      
      const type = processorFactory.determineProcessorType(lineItem);
      
      expect(type).toBe('standard');
    });
  });

  describe('isSpecialSKU', () => {
    it('should identify special SKU patterns', () => {
      const specialSKUs = [
        'CUSTOM-DESIGN-001',
        'PERSONALIZED-002',
        'BESPOKE-003',
        'MADE-TO-ORDER-004',
      ];

      specialSKUs.forEach(sku => {
        // This would need to be implemented based on actual business logic
        // For now, we'll test the method exists
        expect(typeof processorFactory.isSpecialSKU).toBe('function');
      });
    });

    it('should return false for regular SKUs', () => {
      const regularSKUs = [
        'SHIRT-001',
        'HAT-002',
        'STICKER-003',
      ];

      regularSKUs.forEach(sku => {
        const result = processorFactory.isSpecialSKU(sku);
        expect(typeof result).toBe('boolean');
      });
    });
  });

  describe('createProcessor', () => {
    it('should create processor by type', () => {
      const processor = processorFactory.createProcessor('standard');
      
      expect(processor).toBeInstanceOf(StandardProductProcessor);
    });

    it('should throw error for unknown type', () => {
      expect(() => {
        processorFactory.createProcessor('unknown');
      }).toThrow(BusinessLogicError);
    });
  });

  describe('getProcessorTypes', () => {
    it('should return list of available processor types', () => {
      const types = processorFactory.getProcessorTypes();
      
      expect(types).toContain('standard');
      expect(types).toContain('flag_patch');
      expect(types).toContain('custom');
      expect(types.length).toBe(3);
    });
  });

  describe('getStatistics', () => {
    it('should return processor statistics', () => {
      const stats = processorFactory.getStatistics();
      
      expect(stats.totalProcessors).toBe(3);
      expect(stats.processorTypes).toEqual(['standard', 'flag_patch', 'custom']);
    });

    it('should update statistics when processors are added', () => {
      class TestProcessor {}
      processorFactory.registerProcessor('test', TestProcessor);
      
      const stats = processorFactory.getStatistics();
      
      expect(stats.totalProcessors).toBe(4);
      expect(stats.processorTypes).toContain('test');
    });
  });

  describe('edge cases', () => {
    it('should handle null line item', () => {
      expect(() => {
        processorFactory.getProcessor(null);
      }).toThrow();
    });

    it('should handle undefined line item', () => {
      expect(() => {
        processorFactory.getProcessor(undefined);
      }).toThrow();
    });

    it('should handle empty line item object', () => {
      const processor = processorFactory.getProcessor({});
      
      expect(processor).toBeInstanceOf(StandardProductProcessor);
    });

    it('should handle case-insensitive custom detection', () => {
      const lineItems = [
        { title: 'CUSTOM Product' },
        { title: 'Custom Product' },
        { title: 'custom product' },
        { variant_title: 'CUSTOM Design' },
        { variant_title: 'Custom Design' },
        { variant_title: 'custom design' },
      ];

      lineItems.forEach(lineItem => {
        const processor = processorFactory.getProcessor(lineItem);
        expect(processor).toBeInstanceOf(CustomProductProcessor);
      });
    });
  });
});
