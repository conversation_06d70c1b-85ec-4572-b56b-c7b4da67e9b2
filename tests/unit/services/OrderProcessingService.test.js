/**
 * Comprehensive Unit Tests for OrderProcessingService
 */

import { jest } from '@jest/globals';
import { OrderProcessingService } from '../../../app/services/order/OrderProcessingService.js';
import { TransactionManager } from '../../../app/lib/database/TransactionManager.js';
import { ProcessorFactory } from '../../../app/services/order/processors/ProcessorFactory.js';
import { FulfillmentValidator } from '../../../app/services/order/validators/FulfillmentValidator.js';
import { PriceCalculator } from '../../../app/services/order/calculators/PriceCalculator.js';
import { BusinessLogicError, ValidationError } from '../../../app/lib/errors/AppError.js';
import { createMockDatabase, createMockApiClient, createMockLogger } from '../../helpers/testHelpers.js';
import { mockOrderData, mockShopData } from '../../fixtures/testData.js';

describe('OrderProcessingService', () => {
  let orderProcessingService;
  let mockPrisma;
  let mockShopifyClient;
  let mockTransactionManager;
  let mockProcessorFactory;
  let mockFulfillmentValidator;
  let mockPriceCalculator;
  let mockProcessor;

  beforeEach(() => {
    // Create mocks
    mockPrisma = createMockDatabase();
    mockShopifyClient = createMockApiClient();
    mockTransactionManager = {
      executeTransaction: jest.fn(),
    };
    mockProcessorFactory = {
      getProcessor: jest.fn(),
    };
    mockFulfillmentValidator = {
      validateFulfillmentService: jest.fn(),
    };
    mockPriceCalculator = {
      calculateLineItemTotal: jest.fn(),
    };
    mockProcessor = {
      processLineItem: jest.fn(),
    };

    // Setup default mock returns
    mockProcessorFactory.getProcessor.mockReturnValue(mockProcessor);
    mockProcessor.processLineItem.mockResolvedValue({
      id: 67890,
      sku: 'TEST-SKU-001',
      title: 'Test Product',
      quantity: 1,
      price: 24.99,
      subtotal: 24.99,
      tax: 5.00,
      total: 29.99,
      costOfGoods: 15.00,
      profit: 9.99,
    });

    mockTransactionManager.executeTransaction.mockImplementation(async (callback) => {
      return await callback(mockPrisma);
    });

    mockShopifyClient.setStoreContext = jest.fn();
    mockShopifyClient.graphql = jest.fn().mockResolvedValue({
      data: {
        order: mockOrderData,
      },
    });

    // Create service instance
    orderProcessingService = new OrderProcessingService({
      prisma: mockPrisma,
      shopifyClient: mockShopifyClient,
      transactionManager: mockTransactionManager,
      processorFactory: mockProcessorFactory,
      fulfillmentValidator: mockFulfillmentValidator,
      priceCalculator: mockPriceCalculator,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with provided dependencies', () => {
      expect(orderProcessingService.prisma).toBe(mockPrisma);
      expect(orderProcessingService.shopifyClient).toBe(mockShopifyClient);
      expect(orderProcessingService.transactionManager).toBe(mockTransactionManager);
      expect(orderProcessingService.processorFactory).toBe(mockProcessorFactory);
      expect(orderProcessingService.fulfillmentValidator).toBe(mockFulfillmentValidator);
      expect(orderProcessingService.priceCalculator).toBe(mockPriceCalculator);
    });

    it('should use default dependencies when not provided', () => {
      const service = new OrderProcessingService();
      expect(service.prisma).toBeDefined();
      expect(service.shopifyClient).toBeDefined();
      expect(service.transactionManager).toBeInstanceOf(TransactionManager);
      expect(service.processorFactory).toBeInstanceOf(ProcessorFactory);
      expect(service.fulfillmentValidator).toBeInstanceOf(FulfillmentValidator);
      expect(service.priceCalculator).toBeInstanceOf(PriceCalculator);
    });
  });

  describe('processOrder', () => {
    const orderId = '12345';
    const shopDomain = 'test-shop.myshopify.com';

    beforeEach(() => {
      // Mock checkExistingProcessing to return null (not processed)
      jest.spyOn(orderProcessingService, 'checkExistingProcessing').mockResolvedValue(null);
      jest.spyOn(orderProcessingService, 'fetchOrderData').mockResolvedValue(mockOrderData);
      jest.spyOn(orderProcessingService, 'validateOrderForProcessing').mockResolvedValue();
      jest.spyOn(orderProcessingService, 'processOrderInternal').mockResolvedValue({
        orderId: '12345',
        shopDomain: 'test-shop.myshopify.com',
        lineItems: [],
        totals: { total: 29.99 },
      });
      jest.spyOn(orderProcessingService, 'logProcessingFailure').mockResolvedValue();
    });

    it('should process order successfully', async () => {
      const result = await orderProcessingService.processOrder(orderId, shopDomain);

      expect(result.success).toBe(true);
      expect(result.orderId).toBe('12345');
      expect(result.shopDomain).toBe('test-shop.myshopify.com');
      expect(mockShopifyClient.setStoreContext).toHaveBeenCalledWith('test-shop.myshopify.com');
      expect(mockTransactionManager.executeTransaction).toHaveBeenCalled();
    });

    it('should skip processing if order already processed', async () => {
      const existingResult = { processed: true };
      orderProcessingService.checkExistingProcessing.mockResolvedValue(existingResult);

      const result = await orderProcessingService.processOrder(orderId, shopDomain);

      expect(result.success).toBe(true);
      expect(result.skipped).toBe(true);
      expect(result.reason).toBe('Order already processed');
      expect(result.existingResult).toBe(existingResult);
    });

    it('should force reprocess when forceReprocess is true', async () => {
      const existingResult = { processed: true };
      orderProcessingService.checkExistingProcessing.mockResolvedValue(existingResult);

      const result = await orderProcessingService.processOrder(orderId, shopDomain, {
        forceReprocess: true,
      });

      expect(result.success).toBe(true);
      expect(result.skipped).toBeUndefined();
      expect(mockTransactionManager.executeTransaction).toHaveBeenCalled();
    });

    it('should skip validation when skipValidation is true', async () => {
      await orderProcessingService.processOrder(orderId, shopDomain, {
        skipValidation: true,
      });

      expect(orderProcessingService.validateOrderForProcessing).not.toHaveBeenCalled();
    });

    it('should handle processing errors', async () => {
      const error = new Error('Processing failed');
      orderProcessingService.fetchOrderData.mockRejectedValue(error);

      await expect(
        orderProcessingService.processOrder(orderId, shopDomain)
      ).rejects.toThrow(BusinessLogicError);

      expect(orderProcessingService.logProcessingFailure).toHaveBeenCalledWith(
        orderId,
        shopDomain,
        error
      );
    });

    it('should perform dry run without saving', async () => {
      const result = await orderProcessingService.processOrder(orderId, shopDomain, {
        dryRun: true,
      });

      expect(result.success).toBe(true);
      expect(orderProcessingService.processOrderInternal).toHaveBeenCalledWith(
        mockOrderData,
        'test-shop.myshopify.com',
        expect.objectContaining({ dryRun: true })
      );
    });
  });

  describe('processBatch', () => {
    const orders = [
      { orderId: '1', shopDomain: 'shop1.myshopify.com' },
      { orderId: '2', shopDomain: 'shop1.myshopify.com' },
      { orderId: '3', shopDomain: 'shop2.myshopify.com' },
    ];

    beforeEach(() => {
      jest.spyOn(orderProcessingService, 'processOrder').mockResolvedValue({
        success: true,
        orderId: '1',
        shopDomain: 'shop1.myshopify.com',
      });
    });

    it('should process multiple orders in batches', async () => {
      const result = await orderProcessingService.processBatch(orders, {
        batchSize: 2,
      });

      expect(result.totalProcessed).toBe(3);
      expect(result.successful).toBe(3);
      expect(result.failed).toBe(0);
      expect(orderProcessingService.processOrder).toHaveBeenCalledTimes(3);
    });

    it('should continue on error when continueOnError is true', async () => {
      orderProcessingService.processOrder
        .mockResolvedValueOnce({ success: true })
        .mockRejectedValueOnce(new Error('Processing failed'))
        .mockResolvedValueOnce({ success: true });

      const result = await orderProcessingService.processBatch(orders, {
        continueOnError: true,
      });

      expect(result.totalProcessed).toBe(3);
      expect(result.successful).toBe(2);
      expect(result.failed).toBe(1);
      expect(result.errors).toHaveLength(1);
    });

    it('should stop on first error when continueOnError is false', async () => {
      orderProcessingService.processOrder
        .mockResolvedValueOnce({ success: true })
        .mockRejectedValueOnce(new Error('Processing failed'));

      await expect(
        orderProcessingService.processBatch(orders, {
          continueOnError: false,
        })
      ).rejects.toThrow('Processing failed');
    });

    it('should call progress callback if provided', async () => {
      const progressCallback = jest.fn();

      await orderProcessingService.processBatch(orders, {
        progressCallback,
      });

      expect(progressCallback).toHaveBeenCalled();
    });
  });

  describe('validateOrderForProcessing', () => {
    it('should validate order successfully', async () => {
      const validOrder = {
        ...mockOrderData,
        financial_status: 'paid',
        line_items: [
          {
            ...mockOrderData.line_items[0],
            fulfillment_service: 'manual',
          },
        ],
      };

      mockFulfillmentValidator.validateFulfillmentService.mockResolvedValue(true);

      await expect(
        orderProcessingService.validateOrderForProcessing(validOrder, 'test-shop.myshopify.com')
      ).resolves.not.toThrow();
    });

    it('should throw error for unpaid order', async () => {
      const unpaidOrder = {
        ...mockOrderData,
        financial_status: 'pending',
      };

      await expect(
        orderProcessingService.validateOrderForProcessing(unpaidOrder, 'test-shop.myshopify.com')
      ).rejects.toThrow(ValidationError);
    });

    it('should throw error for order with no line items', async () => {
      const emptyOrder = {
        ...mockOrderData,
        line_items: [],
      };

      await expect(
        orderProcessingService.validateOrderForProcessing(emptyOrder, 'test-shop.myshopify.com')
      ).rejects.toThrow(ValidationError);
    });
  });

  describe('processOrderInternal', () => {
    it('should process order and calculate totals', async () => {
      const result = await orderProcessingService.processOrderInternal(
        mockOrderData,
        'test-shop.myshopify.com'
      );

      expect(result.orderId).toBe(mockOrderData.id);
      expect(result.shopDomain).toBe('test-shop.myshopify.com');
      expect(result.lineItems).toHaveLength(1);
      expect(result.totals.total).toBe(29.99);
      expect(mockProcessorFactory.getProcessor).toHaveBeenCalledWith(mockOrderData.line_items[0]);
      expect(mockProcessor.processLineItem).toHaveBeenCalled();
    });

    it('should handle dry run mode', async () => {
      jest.spyOn(orderProcessingService, 'saveProcessingResult').mockResolvedValue();

      const result = await orderProcessingService.processOrderInternal(
        mockOrderData,
        'test-shop.myshopify.com',
        { dryRun: true }
      );

      expect(result.metadata.dryRun).toBe(true);
      expect(orderProcessingService.saveProcessingResult).not.toHaveBeenCalled();
    });

    it('should save processing result when not dry run', async () => {
      jest.spyOn(orderProcessingService, 'saveProcessingResult').mockResolvedValue();

      await orderProcessingService.processOrderInternal(
        mockOrderData,
        'test-shop.myshopify.com',
        { dryRun: false }
      );

      expect(orderProcessingService.saveProcessingResult).toHaveBeenCalled();
    });
  });
});
