import { vi } from 'vitest';
import { installGlobals } from '@remix-run/node';

// Install Remix globals
installGlobals();

// Mock environment variables
process.env.SHIPSTATION_V1_KEY = 'test_key';
process.env.SHIPSTATION_V1_SECRET = 'test_secret';
process.env.SHIPSTATION_WEBHOOK_KEY = 'test_webhook_key';
process.env.DEFAULT_SHOP = 'test-shop.myshopify.com';
process.env.DEFAULT_STORE_ID = '12345';

// Mock the database
vi.mock('../app/db.server.js', () => {
  return {
    default: {
      processedWebhook: {
        findFirst: vi.fn(),
        create: vi.fn(),
      },
      shippingCost: {
        findFirst: vi.fn(),
        create: vi.fn(),
        update: vi.fn(),
      },
      shippingTransaction: {
        create: vi.fn(),
      },
      invoiceBalance: {
        findFirst: vi.fn(),
        create: vi.fn(),
        update: vi.fn(),
      },
      invoiceTransaction: {
        create: vi.fn(),
      },
    },
  };
});

// Mock axios
vi.mock('axios', () => {
  return {
    default: {
      get: vi.fn(),
    },
  };
});

// Mock the ShipStation store mapping
vi.mock('../app/models/ShipStationStoreMapping.server', () => {
  return {
    getStoreMappingByStoreId: vi.fn(),
  };
});
