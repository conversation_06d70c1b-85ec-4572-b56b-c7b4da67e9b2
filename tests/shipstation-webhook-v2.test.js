import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import axios from 'axios';

// Mock the webhook action
const mockProcessShipment = vi.fn();

// Mock axios
vi.mock('axios');

describe('ShipStation V2 Webhook Handler', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock environment variables
    process.env.SHIPSTATION_API_KEY = 'test-api-key';
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Label Fetching for Webhooks', () => {
    it('should fetch labels using V2 API with shipment_id parameter', async () => {
      // Mock the labels response from V2 API
      const mockLabelsResponse = {
        data: {
          labels: [
            {
              label_id: 'se-12345',
              shipment_id: 'se-67890',
              shipment_cost: {
                amount: 12.50,
                currency: 'USD'
              },
              voided: false,
              created_at: '2024-01-15T10:30:00Z'
            },
            {
              label_id: 'se-12346',
              shipment_id: 'se-67890',
              shipment_cost: {
                amount: 3.25,
                currency: 'USD'
              },
              voided: false,
              created_at: '2024-01-15T10:30:00Z'
            }
          ]
        }
      };

      axios.get.mockResolvedValueOnce(mockLabelsResponse);

      // Simulate webhook payload
      const shipmentPayload = {
        shipment_id: 'se-67890',
        created_at: '2024-01-15T10:30:00Z'
      };

      // Test the label fetching logic
      const response = await axios.get('https://api.shipstation.com/v2/labels', {
        headers: {
          'API-Key': 'test-api-key',
          'Content-Type': 'application/json'
        },
        params: {
          shipment_id: shipmentPayload.shipment_id
        }
      });

      // Verify the API call was made correctly
      expect(axios.get).toHaveBeenCalledWith(
        'https://api.shipstation.com/v2/labels',
        {
          headers: {
            'API-Key': 'test-api-key',
            'Content-Type': 'application/json'
          },
          params: {
            shipment_id: 'se-67890'
          }
        }
      );

      // Verify the response structure
      expect(response.data.labels).toHaveLength(2);
      expect(response.data.labels[0].shipment_cost.amount).toBe(12.50);
      expect(response.data.labels[1].shipment_cost.amount).toBe(3.25);
    });

    it('should handle voided labels correctly', async () => {
      // Mock response with voided label
      const mockLabelsResponse = {
        data: {
          labels: [
            {
              label_id: 'se-12345',
              shipment_id: 'se-67890',
              shipment_cost: {
                amount: 12.50,
                currency: 'USD'
              },
              voided: true,
              created_at: '2024-01-15T10:30:00Z'
            },
            {
              label_id: 'se-12346',
              shipment_id: 'se-67890',
              shipment_cost: {
                amount: 8.75,
                currency: 'USD'
              },
              voided: false,
              created_at: '2024-01-15T10:30:00Z'
            }
          ]
        }
      };

      axios.get.mockResolvedValueOnce(mockLabelsResponse);

      const response = await axios.get('https://api.shipstation.com/v2/labels', {
        params: { shipment_id: 'se-67890' }
      });

      // Calculate total cost excluding voided labels
      const totalCost = response.data.labels
        .filter(label => !label.voided)
        .reduce((sum, label) => sum + label.shipment_cost.amount, 0);

      expect(totalCost).toBe(8.75); // Only non-voided label
    });

    it('should extract shipment_cost correctly from different formats', async () => {
      const testCases = [
        {
          description: 'shipment_cost as object with amount',
          shipment_cost: { amount: 15.25, currency: 'USD' },
          expected: 15.25
        },
        {
          description: 'shipment_cost as number',
          shipment_cost: 10.50,
          expected: 10.50
        },
        {
          description: 'shipment_cost as string',
          shipment_cost: '7.75',
          expected: 7.75
        }
      ];

      testCases.forEach(testCase => {
        let extractedCost = 0;

        if (testCase.shipment_cost && testCase.shipment_cost.amount) {
          extractedCost = parseFloat(testCase.shipment_cost.amount);
        } else if (typeof testCase.shipment_cost === 'number' || typeof testCase.shipment_cost === 'string') {
          extractedCost = parseFloat(testCase.shipment_cost);
        }

        expect(extractedCost).toBe(testCase.expected);
      });
    });
  });

  describe('Direct Label Fetching for Mapping', () => {
    it('should fetch labels directly by date range using V2 API', async () => {
      // Mock the labels response for date range query
      const mockLabelsResponse = {
        data: {
          labels: [
            {
              label_id: 'se-12345',
              shipment_id: 'se-67890',
              shipment_cost: {
                amount: 12.50,
                currency: 'USD'
              },
              created_at: '2024-01-15T10:30:00Z',
              voided: false
            }
          ]
        }
      };

      axios.get.mockResolvedValueOnce(mockLabelsResponse);

      // Test direct label fetching by date range
      const response = await axios.get('https://api.shipstation.com/v2/labels', {
        headers: {
          'API-Key': 'test-api-key',
          'Content-Type': 'application/json'
        },
        params: {
          created_at_start: '2024-01-01T00:00:00Z',
          created_at_end: '2024-01-31T23:59:59Z',
          page_size: 100,
          page: 1
        }
      });

      // Verify the API call was made correctly
      expect(axios.get).toHaveBeenCalledWith(
        'https://api.shipstation.com/v2/labels',
        {
          headers: {
            'API-Key': 'test-api-key',
            'Content-Type': 'application/json'
          },
          params: {
            created_at_start: '2024-01-01T00:00:00Z',
            created_at_end: '2024-01-31T23:59:59Z',
            page_size: 100,
            page: 1
          }
        }
      );

      // Verify the response
      expect(response.data.labels).toHaveLength(1);
      expect(response.data.labels[0].shipment_cost.amount).toBe(12.50);
    });
  });
});
