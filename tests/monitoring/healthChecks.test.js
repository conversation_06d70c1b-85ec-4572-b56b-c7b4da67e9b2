/**
 * Monitoring and Health Check Tests
 */

import { jest } from '@jest/globals';
import request from 'supertest';
import { createMockDatabase, createMockApiClient, measurePerformance } from '../helpers/testHelpers.js';

// Mock the database health check utility
jest.mock('../../app/utils/database-health.js', () => ({
  checkDatabaseHealth: jest.fn(),
  checkDatabaseConnectivity: jest.fn(),
  getDatabaseMetrics: jest.fn(),
}));

describe('Health Check and Monitoring Tests', () => {
  let app;
  let mockPrisma;
  let mockDatabaseHealth;

  beforeEach(async () => {
    // Setup mocks
    mockPrisma = createMockDatabase();
    
    // Import mocked database health functions
    mockDatabaseHealth = await import('../../app/utils/database-health.js');
    
    // Setup default healthy responses
    mockDatabaseHealth.checkDatabaseHealth.mockResolvedValue({
      status: 'healthy',
      responseTime: 45,
      connections: {
        active: 5,
        idle: 10,
        total: 15,
      },
    });

    mockDatabaseHealth.checkDatabaseConnectivity.mockResolvedValue(true);
    
    mockDatabaseHealth.getDatabaseMetrics.mockResolvedValue({
      queryCount: 1250,
      averageQueryTime: 23.5,
      slowQueries: 2,
      errorRate: 0.001,
    });

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('Application Health Endpoints', () => {
    it('should return healthy status for Remix health endpoint', async () => {
      // Mock the Remix app
      const mockRemixApp = {
        get: jest.fn().mockImplementation((path, handler) => {
          if (path === '/healthz') {
            return {
              status: 200,
              json: () => ({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                services: {
                  database: 'healthy',
                  shopify: 'healthy',
                  shipstation: 'healthy',
                },
              }),
            };
          }
        }),
      };

      const response = mockRemixApp.get('/healthz');
      const healthData = response.json();

      expect(response.status).toBe(200);
      expect(healthData.status).toBe('healthy');
      expect(healthData.services.database).toBe('healthy');
    });

    it('should return healthy status for Express health endpoint', async () => {
      // Mock Express server response
      const mockExpressResponse = {
        status: 200,
        body: {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          uptime: 3600,
          memory: {
            used: 125.5,
            total: 512.0,
            percentage: 24.5,
          },
          services: {
            multistore: 'healthy',
            authentication: 'healthy',
          },
        },
      };

      expect(mockExpressResponse.status).toBe(200);
      expect(mockExpressResponse.body.status).toBe('healthy');
      expect(mockExpressResponse.body.services.multistore).toBe('healthy');
    });

    it('should detect unhealthy database status', async () => {
      // Mock unhealthy database
      mockDatabaseHealth.checkDatabaseHealth.mockResolvedValue({
        status: 'unhealthy',
        responseTime: 5000,
        error: 'Connection timeout',
        connections: {
          active: 0,
          idle: 0,
          total: 0,
        },
      });

      const healthCheck = await mockDatabaseHealth.checkDatabaseHealth();

      expect(healthCheck.status).toBe('unhealthy');
      expect(healthCheck.responseTime).toBeGreaterThan(1000);
      expect(healthCheck.error).toBeDefined();
    });

    it('should measure health check response time', async () => {
      const result = await measurePerformance(async () => {
        return await mockDatabaseHealth.checkDatabaseHealth();
      });

      expect(result.result.status).toBe('healthy');
      expect(result.duration).toBeLessThan(1000); // Health check should be fast
    });
  });

  describe('Database Health Monitoring', () => {
    it('should validate database connectivity', async () => {
      const isConnected = await mockDatabaseHealth.checkDatabaseConnectivity();
      
      expect(isConnected).toBe(true);
      expect(mockDatabaseHealth.checkDatabaseConnectivity).toHaveBeenCalled();
    });

    it('should collect database performance metrics', async () => {
      const metrics = await mockDatabaseHealth.getDatabaseMetrics();

      expect(metrics).toHaveProperty('queryCount');
      expect(metrics).toHaveProperty('averageQueryTime');
      expect(metrics).toHaveProperty('slowQueries');
      expect(metrics).toHaveProperty('errorRate');
      
      expect(metrics.queryCount).toBeGreaterThan(0);
      expect(metrics.averageQueryTime).toBeGreaterThan(0);
      expect(metrics.errorRate).toBeLessThan(0.01); // Less than 1% error rate
    });

    it('should detect slow database queries', async () => {
      mockDatabaseHealth.getDatabaseMetrics.mockResolvedValue({
        queryCount: 1000,
        averageQueryTime: 150.5, // Slow queries
        slowQueries: 25,
        errorRate: 0.002,
      });

      const metrics = await mockDatabaseHealth.getDatabaseMetrics();

      expect(metrics.averageQueryTime).toBeGreaterThan(100);
      expect(metrics.slowQueries).toBeGreaterThan(0);
    });

    it('should monitor database connection pool', async () => {
      const healthCheck = await mockDatabaseHealth.checkDatabaseHealth();

      expect(healthCheck.connections).toHaveProperty('active');
      expect(healthCheck.connections).toHaveProperty('idle');
      expect(healthCheck.connections).toHaveProperty('total');
      
      expect(healthCheck.connections.total).toBe(
        healthCheck.connections.active + healthCheck.connections.idle
      );
    });
  });

  describe('External Service Health Monitoring', () => {
    it('should check Shopify API connectivity', async () => {
      const mockShopifyClient = createMockApiClient();
      
      // Mock successful Shopify API call
      mockShopifyClient.graphql.mockResolvedValue({
        data: {
          shop: {
            id: 'gid://shopify/Shop/12345',
            name: 'Test Shop',
          },
        },
      });

      const result = await measurePerformance(async () => {
        return await mockShopifyClient.graphql('{ shop { id name } }');
      });

      expect(result.result.data.shop).toBeDefined();
      expect(result.duration).toBeLessThan(2000); // Should respond within 2 seconds
    });

    it('should check ShipStation API connectivity', async () => {
      const mockShipStationClient = createMockApiClient();
      
      // Mock successful ShipStation API call
      mockShipStationClient.get.mockResolvedValue({
        stores: [
          {
            storeId: 12345,
            storeName: 'Test Store',
            active: true,
          },
        ],
      });

      const result = await measurePerformance(async () => {
        return await mockShipStationClient.get('/stores');
      });

      expect(result.result.stores).toBeDefined();
      expect(result.result.stores.length).toBeGreaterThan(0);
      expect(result.duration).toBeLessThan(3000); // Should respond within 3 seconds
    });

    it('should detect API rate limiting', async () => {
      const mockApiClient = createMockApiClient();
      
      // Mock rate limit response
      mockApiClient.get.mockRejectedValue({
        response: {
          status: 429,
          headers: {
            'x-rate-limit-remaining': '0',
            'x-rate-limit-reset': '60',
          },
        },
      });

      try {
        await mockApiClient.get('/test');
      } catch (error) {
        expect(error.response.status).toBe(429);
        expect(error.response.headers['x-rate-limit-remaining']).toBe('0');
      }
    });
  });

  describe('Application Metrics Monitoring', () => {
    it('should track memory usage', async () => {
      const memoryUsage = process.memoryUsage();
      
      expect(memoryUsage).toHaveProperty('rss');
      expect(memoryUsage).toHaveProperty('heapTotal');
      expect(memoryUsage).toHaveProperty('heapUsed');
      expect(memoryUsage).toHaveProperty('external');
      
      // Memory usage should be reasonable
      expect(memoryUsage.heapUsed).toBeLessThan(memoryUsage.heapTotal);
      expect(memoryUsage.heapUsed).toBeLessThan(500 * 1024 * 1024); // Less than 500MB
    });

    it('should track CPU usage patterns', async () => {
      const startTime = process.hrtime();
      
      // Simulate some CPU work
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const endTime = process.hrtime(startTime);
      const executionTime = endTime[0] * 1000 + endTime[1] / 1000000; // Convert to milliseconds
      
      expect(executionTime).toBeGreaterThan(90); // Should be close to 100ms
      expect(executionTime).toBeLessThan(200); // But not too much overhead
    });

    it('should monitor event loop lag', async () => {
      const start = process.hrtime.bigint();
      
      await new Promise(resolve => setImmediate(resolve));
      
      const end = process.hrtime.bigint();
      const lag = Number(end - start) / 1000000; // Convert to milliseconds
      
      // Event loop lag should be minimal in tests
      expect(lag).toBeLessThan(50); // Less than 50ms lag
    });
  });

  describe('Error Rate Monitoring', () => {
    it('should track error rates within acceptable limits', async () => {
      const totalRequests = 1000;
      const errorRequests = 5;
      const errorRate = errorRequests / totalRequests;
      
      expect(errorRate).toBeLessThan(0.01); // Less than 1% error rate
    });

    it('should detect high error rates', async () => {
      const totalRequests = 100;
      const errorRequests = 15;
      const errorRate = errorRequests / totalRequests;
      
      expect(errorRate).toBeGreaterThan(0.1); // More than 10% error rate (unhealthy)
    });

    it('should categorize error types', async () => {
      const errorCategories = {
        '4xx': 5,  // Client errors
        '5xx': 2,  // Server errors
        timeout: 1,
        network: 0,
      };
      
      const totalErrors = Object.values(errorCategories).reduce((sum, count) => sum + count, 0);
      
      expect(totalErrors).toBe(8);
      expect(errorCategories['4xx']).toBeGreaterThan(errorCategories['5xx']); // More client errors than server errors
    });
  });

  describe('Performance Threshold Monitoring', () => {
    it('should validate API response times', async () => {
      const responseTime = 150; // milliseconds
      
      expect(responseTime).toBeLessThan(2000); // Less than 2 seconds
      expect(responseTime).toBeGreaterThan(0);
    });

    it('should validate database query performance', async () => {
      const queryTime = 45; // milliseconds
      
      expect(queryTime).toBeLessThan(500); // Less than 500ms
      expect(queryTime).toBeGreaterThan(0);
    });

    it('should validate throughput metrics', async () => {
      const requestsPerSecond = 25;
      const ordersPerMinute = 150;
      
      expect(requestsPerSecond).toBeGreaterThan(10); // At least 10 RPS
      expect(ordersPerMinute).toBeGreaterThan(50); // At least 50 orders per minute
    });
  });

  describe('Alerting Thresholds', () => {
    it('should trigger alerts for critical thresholds', async () => {
      const metrics = {
        memoryUsage: 85, // 85% memory usage
        cpuUsage: 90,    // 90% CPU usage
        errorRate: 0.05, // 5% error rate
        responseTime: 3000, // 3 second response time
      };
      
      // These should trigger alerts
      expect(metrics.memoryUsage).toBeGreaterThan(80);
      expect(metrics.cpuUsage).toBeGreaterThan(80);
      expect(metrics.errorRate).toBeGreaterThan(0.02);
      expect(metrics.responseTime).toBeGreaterThan(2000);
    });

    it('should not trigger alerts for normal metrics', async () => {
      const metrics = {
        memoryUsage: 45, // 45% memory usage
        cpuUsage: 30,    // 30% CPU usage
        errorRate: 0.001, // 0.1% error rate
        responseTime: 150, // 150ms response time
      };
      
      // These should not trigger alerts
      expect(metrics.memoryUsage).toBeLessThan(80);
      expect(metrics.cpuUsage).toBeLessThan(80);
      expect(metrics.errorRate).toBeLessThan(0.02);
      expect(metrics.responseTime).toBeLessThan(2000);
    });
  });
});
