/**
 * Mock implementations for external services
 */

import { jest } from '@jest/globals';
import { 
  mockShopData, 
  mockOrderData, 
  mockShipStationOrderData, 
  mockShipStationStoreData,
  mockShipStationShipmentData 
} from '../fixtures/testData.js';

/**
 * Mock Shopify API client
 */
export class MockShopifyClient {
  constructor() {
    this.graphql = jest.fn();
    this.rest = jest.fn();
  }

  async graphql(query, variables = {}) {
    // Mock different GraphQL queries
    if (query.includes('orders')) {
      return {
        data: {
          orders: {
            edges: [
              {
                node: mockOrderData,
              },
            ],
            pageInfo: {
              hasNextPage: false,
              endCursor: 'cursor123',
            },
          },
        },
      };
    }

    if (query.includes('shop')) {
      return {
        data: {
          shop: mockShopData,
        },
      };
    }

    return { data: {} };
  }

  async rest(resource, method = 'GET', data = {}) {
    switch (resource) {
      case 'orders':
        return { orders: [mockOrderData] };
      case 'shop':
        return { shop: mockShopData };
      default:
        return {};
    }
  }
}

/**
 * Mock ShipStation API client
 */
export class MockShipStationClient {
  constructor() {
    this.get = jest.fn();
    this.post = jest.fn();
    this.put = jest.fn();
    this.delete = jest.fn();
  }

  async get(endpoint, params = {}) {
    if (endpoint.includes('/stores')) {
      return [mockShipStationStoreData];
    }

    if (endpoint.includes('/orders')) {
      return {
        orders: [mockShipStationOrderData],
        total: 1,
        page: 1,
        pages: 1,
      };
    }

    if (endpoint.includes('/shipments')) {
      return {
        shipments: [mockShipStationShipmentData],
        total: 1,
        page: 1,
        pages: 1,
      };
    }

    if (endpoint.includes('/labels')) {
      return {
        labels: [
          {
            shipmentId: 123456789,
            trackingNumber: 'TEST123456789',
            shipmentCost: 5.99,
            shipDate: '2024-01-02T00:00:00.0000000',
            carrierCode: 'usps',
            serviceCode: 'usps_priority_mail',
          },
        ],
        total: 1,
        page: 1,
        pages: 1,
      };
    }

    return {};
  }

  async post(endpoint, data = {}) {
    if (endpoint.includes('/orders')) {
      return { ...mockShipStationOrderData, ...data };
    }

    if (endpoint.includes('/shipments')) {
      return { ...mockShipStationShipmentData, ...data };
    }

    return { success: true };
  }

  async put(endpoint, data = {}) {
    return { success: true, ...data };
  }

  async delete(endpoint) {
    return { success: true };
  }
}

/**
 * Mock database client
 */
export class MockDatabaseClient {
  constructor() {
    this.shop = {
      findFirst: jest.fn().mockResolvedValue(mockShopData),
      findMany: jest.fn().mockResolvedValue([mockShopData]),
      create: jest.fn().mockResolvedValue(mockShopData),
      update: jest.fn().mockResolvedValue(mockShopData),
      delete: jest.fn().mockResolvedValue(mockShopData),
    };

    this.invoiceBalance = {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };

    this.shippingCost = {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };

    this.processedWebhook = {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };

    this.reconciliationJob = {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };

    this.$transaction = jest.fn((callback) => callback(this));
    this.$disconnect = jest.fn();
  }

  reset() {
    Object.values(this).forEach(table => {
      if (typeof table === 'object' && table !== null) {
        Object.values(table).forEach(method => {
          if (typeof method === 'function' && method.mockReset) {
            method.mockReset();
          }
        });
      }
    });
  }
}

/**
 * Mock HTTP client for external API calls
 */
export class MockHttpClient {
  constructor() {
    this.get = jest.fn();
    this.post = jest.fn();
    this.put = jest.fn();
    this.delete = jest.fn();
    this.request = jest.fn();
  }

  async get(url, config = {}) {
    // Simulate different responses based on URL
    if (url.includes('shopify')) {
      return { data: mockOrderData, status: 200 };
    }

    if (url.includes('shipstation')) {
      return { data: mockShipStationOrderData, status: 200 };
    }

    return { data: {}, status: 200 };
  }

  async post(url, data = {}, config = {}) {
    return { data: { success: true, ...data }, status: 201 };
  }

  async put(url, data = {}, config = {}) {
    return { data: { success: true, ...data }, status: 200 };
  }

  async delete(url, config = {}) {
    return { data: { success: true }, status: 204 };
  }

  async request(config) {
    const method = config.method || 'GET';
    const url = config.url || '';

    switch (method.toUpperCase()) {
      case 'GET':
        return this.get(url, config);
      case 'POST':
        return this.post(url, config.data, config);
      case 'PUT':
        return this.put(url, config.data, config);
      case 'DELETE':
        return this.delete(url, config);
      default:
        return { data: {}, status: 200 };
    }
  }

  reset() {
    this.get.mockReset();
    this.post.mockReset();
    this.put.mockReset();
    this.delete.mockReset();
    this.request.mockReset();
  }
}

/**
 * Mock Redis client for caching
 */
export class MockRedisClient {
  constructor() {
    this.data = new Map();
    this.get = jest.fn((key) => Promise.resolve(this.data.get(key) || null));
    this.set = jest.fn((key, value, options) => {
      this.data.set(key, value);
      return Promise.resolve('OK');
    });
    this.del = jest.fn((key) => {
      const existed = this.data.has(key);
      this.data.delete(key);
      return Promise.resolve(existed ? 1 : 0);
    });
    this.exists = jest.fn((key) => Promise.resolve(this.data.has(key) ? 1 : 0));
    this.expire = jest.fn(() => Promise.resolve(1));
    this.ttl = jest.fn(() => Promise.resolve(-1));
    this.flushall = jest.fn(() => {
      this.data.clear();
      return Promise.resolve('OK');
    });
  }

  reset() {
    this.data.clear();
    this.get.mockReset();
    this.set.mockReset();
    this.del.mockReset();
    this.exists.mockReset();
    this.expire.mockReset();
    this.ttl.mockReset();
    this.flushall.mockReset();
  }
}

/**
 * Mock file system operations
 */
export class MockFileSystem {
  constructor() {
    this.files = new Map();
    this.readFile = jest.fn((path) => {
      const content = this.files.get(path);
      if (content === undefined) {
        throw new Error(`File not found: ${path}`);
      }
      return Promise.resolve(content);
    });
    this.writeFile = jest.fn((path, content) => {
      this.files.set(path, content);
      return Promise.resolve();
    });
    this.exists = jest.fn((path) => Promise.resolve(this.files.has(path)));
    this.unlink = jest.fn((path) => {
      const existed = this.files.has(path);
      this.files.delete(path);
      if (!existed) {
        throw new Error(`File not found: ${path}`);
      }
      return Promise.resolve();
    });
  }

  reset() {
    this.files.clear();
    this.readFile.mockReset();
    this.writeFile.mockReset();
    this.exists.mockReset();
    this.unlink.mockReset();
  }
}

/**
 * Factory function to create all mocks
 */
export function createAllMocks() {
  return {
    shopifyClient: new MockShopifyClient(),
    shipstationClient: new MockShipStationClient(),
    databaseClient: new MockDatabaseClient(),
    httpClient: new MockHttpClient(),
    redisClient: new MockRedisClient(),
    fileSystem: new MockFileSystem(),
  };
}

/**
 * Reset all mocks
 */
export function resetAllMocks(mocks) {
  Object.values(mocks).forEach(mock => {
    if (mock.reset) {
      mock.reset();
    }
  });
}
