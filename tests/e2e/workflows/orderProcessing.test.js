/**
 * End-to-End Tests for Order Processing Workflow
 */

import { jest } from '@jest/globals';
import { OrderProcessingService } from '../../../app/services/order/OrderProcessingService.js';
import { ReconciliationOrchestrator } from '../../../app/services/reconciliation/ReconciliationOrchestrator.js';
import { ShippingCostService } from '../../../app/services/shipping/ShippingCostService.js';
import { createMockDatabase, createMockApiClient, measurePerformance, trackMemoryUsage } from '../../helpers/testHelpers.js';
import { mockOrderData, mockShopData, mockShipStationOrderData } from '../../fixtures/testData.js';

describe('Order Processing Workflow E2E Tests', () => {
  let orderProcessingService;
  let reconciliationOrchestrator;
  let shippingCostService;
  let mockPrisma;
  let mockShopifyClient;
  let mockShipStationClient;
  let memoryTracker;

  beforeEach(() => {
    // Create mock dependencies
    mockPrisma = createMockDatabase();
    mockShopifyClient = createMockApiClient();
    mockShipStationClient = createMockApiClient();
    
    // Initialize services
    orderProcessingService = new OrderProcessingService({
      prisma: mockPrisma,
      shopifyClient: mockShopifyClient,
    });
    
    reconciliationOrchestrator = new ReconciliationOrchestrator({
      prisma: mockPrisma,
    });
    
    shippingCostService = new ShippingCostService({
      prisma: mockPrisma,
      shipstationClient: mockShipStationClient,
    });

    // Setup memory tracking
    memoryTracker = trackMemoryUsage();

    // Setup default mock responses
    mockShopifyClient.graphql.mockResolvedValue({
      data: {
        order: mockOrderData,
      },
    });

    mockShipStationClient.get.mockResolvedValue({
      shipments: [
        {
          shipmentId: 123456789,
          orderNumber: '12345',
          shipmentCost: 5.99,
          trackingNumber: 'TEST123456789',
          carrierCode: 'usps',
          serviceCode: 'usps_priority_mail',
          shipDate: '2024-01-02T00:00:00.0000000',
        },
      ],
    });

    // Setup database mocks
    mockPrisma.processedWebhook.findFirst.mockResolvedValue(null);
    mockPrisma.processedWebhook.create.mockResolvedValue({ id: 1 });
    mockPrisma.invoiceBalance.upsert.mockResolvedValue({ id: 1 });
    mockPrisma.shippingCost.upsert.mockResolvedValue({ id: 1 });
    mockPrisma.shipStationStoreMapping.findFirst.mockResolvedValue({
      storeId: 67890,
      shop: 'test-shop.myshopify.com',
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
    
    // Log memory usage
    const memoryUsage = memoryTracker.getUsage();
    if (memoryUsage.heapUsed > 50 * 1024 * 1024) { // 50MB threshold
      console.warn('High memory usage detected:', memoryUsage);
    }
  });

  describe('Complete Order Lifecycle', () => {
    it('should process order from creation to fulfillment', async () => {
      const orderId = '12345';
      const shopDomain = 'test-shop.myshopify.com';

      // Step 1: Process order creation
      const processingResult = await measurePerformance(async () => {
        return await orderProcessingService.processOrder(orderId, shopDomain);
      });

      expect(processingResult.result.success).toBe(true);
      expect(processingResult.duration).toBeLessThan(5000); // Should complete within 5 seconds
      expect(mockPrisma.processedWebhook.create).toHaveBeenCalled();
      expect(mockPrisma.invoiceBalance.upsert).toHaveBeenCalled();

      // Step 2: Process order fulfillment
      const fulfillmentResult = await shippingCostService.processFulfilledOrder(orderId, shopDomain);

      expect(fulfillmentResult.success).toBe(true);
      expect(mockPrisma.shippingCost.upsert).toHaveBeenCalled();

      // Step 3: Verify data consistency
      const invoiceCall = mockPrisma.invoiceBalance.upsert.mock.calls[0];
      const shippingCall = mockPrisma.shippingCost.upsert.mock.calls[0];

      expect(invoiceCall[0].where.shop_orderId.orderId).toBe(orderId);
      expect(shippingCall[0].where.shop_orderId.orderId).toBe(orderId);
    });

    it('should handle order processing with multiple line items', async () => {
      const complexOrder = {
        ...mockOrderData,
        line_items: [
          {
            id: '67890',
            variant_id: '11111',
            title: 'Test Shirt',
            sku: 'SHIRT-001',
            quantity: 2,
            price: '24.99',
            fulfillment_service: 'manual',
          },
          {
            id: '67891',
            variant_id: '11112',
            title: 'Test Hat',
            sku: 'HAT-001',
            quantity: 1,
            price: '19.99',
            fulfillment_service: 'manual',
          },
          {
            id: '67892',
            variant_id: '11113',
            title: 'Custom Flag',
            sku: '*F-FLAG-001',
            quantity: 1,
            price: '39.99',
            fulfillment_service: 'manual',
          },
        ],
      };

      mockShopifyClient.graphql.mockResolvedValue({
        data: {
          order: complexOrder,
        },
      });

      const result = await orderProcessingService.processOrder('12345', 'test-shop.myshopify.com');

      expect(result.success).toBe(true);
      expect(result.result.lineItems).toHaveLength(3);
      
      // Verify different product types were processed
      const lineItems = result.result.lineItems;
      expect(lineItems.some(item => item.sku === 'SHIRT-001')).toBe(true);
      expect(lineItems.some(item => item.sku === 'HAT-001')).toBe(true);
      expect(lineItems.some(item => item.sku === '*F-FLAG-001')).toBe(true);
    });

    it('should handle order processing errors gracefully', async () => {
      // Mock Shopify API error
      mockShopifyClient.graphql.mockRejectedValue(new Error('Shopify API error'));

      const result = await orderProcessingService.processOrder('12345', 'test-shop.myshopify.com')
        .catch(error => error);

      expect(result).toBeInstanceOf(Error);
      expect(result.message).toContain('Failed to process order');
    });

    it('should prevent duplicate order processing', async () => {
      const orderId = '12345';
      const shopDomain = 'test-shop.myshopify.com';

      // Mock that order was already processed
      mockPrisma.processedWebhook.findFirst.mockResolvedValue({
        id: 1,
        orderId: orderId,
        shop: shopDomain,
        processedAt: new Date(),
      });

      const result = await orderProcessingService.processOrder(orderId, shopDomain);

      expect(result.success).toBe(true);
      expect(result.skipped).toBe(true);
      expect(result.reason).toBe('Order already processed');
      
      // Should not create new records
      expect(mockPrisma.invoiceBalance.upsert).not.toHaveBeenCalled();
    });
  });

  describe('Reconciliation Workflow', () => {
    it('should reconcile missing orders successfully', async () => {
      // Mock missing orders scenario
      mockShopifyClient.graphql.mockResolvedValue({
        data: {
          orders: {
            edges: [
              {
                node: mockOrderData,
                cursor: 'cursor123',
              },
            ],
            pageInfo: {
              hasNextPage: false,
              endCursor: 'cursor123',
            },
          },
        },
      });

      mockPrisma.processedWebhook.findFirst.mockResolvedValue(null); // Order not processed

      const result = await reconciliationOrchestrator.executeReconciliation('missing_orders', {
        shopDomain: 'test-shop.myshopify.com',
        daysToLookBack: 7,
      });

      expect(result.success).toBe(true);
      expect(result.jobId).toBeDefined();
    });

    it('should handle reconciliation with large datasets', async () => {
      // Mock large dataset
      const largeOrderSet = Array.from({ length: 100 }, (_, i) => ({
        node: {
          ...mockOrderData,
          id: `order-${i}`,
        },
        cursor: `cursor-${i}`,
      }));

      mockShopifyClient.graphql.mockResolvedValue({
        data: {
          orders: {
            edges: largeOrderSet,
            pageInfo: {
              hasNextPage: false,
              endCursor: 'final-cursor',
            },
          },
        },
      });

      const result = await measurePerformance(async () => {
        return await reconciliationOrchestrator.executeReconciliation('scheduled', {
          shopDomain: 'test-shop.myshopify.com',
          ordersPerShop: 100,
        });
      });

      expect(result.result.success).toBe(true);
      expect(result.duration).toBeLessThan(30000); // Should complete within 30 seconds
    });
  });

  describe('ShipStation Integration Workflow', () => {
    it('should sync shipping costs from ShipStation', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      const result = await shippingCostService.syncShippingCosts(
        'test-shop.myshopify.com',
        startDate,
        endDate
      );

      expect(result.success).toBe(true);
      expect(result.syncedCount).toBeGreaterThan(0);
      expect(mockShipStationClient.get).toHaveBeenCalledWith('/shipments', expect.objectContaining({
        storeId: 67890,
        createDateStart: startDate.toISOString(),
        createDateEnd: endDate.toISOString(),
      }));
    });

    it('should handle ShipStation API rate limits', async () => {
      // Mock rate limit response
      mockShipStationClient.get.mockRejectedValue({
        response: {
          status: 429,
          headers: {
            'x-rate-limit-reset': '60',
          },
        },
      });

      const result = await shippingCostService.syncShippingCosts(
        'test-shop.myshopify.com',
        new Date('2024-01-01'),
        new Date('2024-01-31')
      ).catch(error => error);

      expect(result).toBeInstanceOf(Error);
    });
  });

  describe('Performance and Memory Tests', () => {
    it('should process orders within performance thresholds', async () => {
      const orders = Array.from({ length: 10 }, (_, i) => ({
        orderId: `order-${i}`,
        shopDomain: 'test-shop.myshopify.com',
      }));

      const result = await measurePerformance(async () => {
        return await orderProcessingService.processBatch(orders, {
          batchSize: 5,
          continueOnError: true,
        });
      });

      expect(result.result.totalProcessed).toBe(10);
      expect(result.duration).toBeLessThan(10000); // Should complete within 10 seconds
    });

    it('should not leak memory during batch processing', async () => {
      const initialMemory = memoryTracker.getUsage();
      
      // Process multiple batches
      for (let i = 0; i < 5; i++) {
        const orders = Array.from({ length: 20 }, (_, j) => ({
          orderId: `batch-${i}-order-${j}`,
          shopDomain: 'test-shop.myshopify.com',
        }));

        await orderProcessingService.processBatch(orders, {
          batchSize: 10,
        });

        // Force garbage collection if available
        if (global.gc) {
          global.gc();
        }
      }

      const finalMemory = memoryTracker.getUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      // Memory increase should be reasonable (less than 100MB)
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024);
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should recover from transient database errors', async () => {
      // Mock database error on first call, success on retry
      mockPrisma.invoiceBalance.upsert
        .mockRejectedValueOnce(new Error('Database connection lost'))
        .mockResolvedValueOnce({ id: 1 });

      const result = await orderProcessingService.processOrder('12345', 'test-shop.myshopify.com');

      expect(result.success).toBe(true);
    });

    it('should handle partial batch failures', async () => {
      const orders = [
        { orderId: 'success-1', shopDomain: 'test-shop.myshopify.com' },
        { orderId: 'fail-1', shopDomain: 'test-shop.myshopify.com' },
        { orderId: 'success-2', shopDomain: 'test-shop.myshopify.com' },
      ];

      // Mock failure for specific order
      jest.spyOn(orderProcessingService, 'processOrder')
        .mockResolvedValueOnce({ success: true })
        .mockRejectedValueOnce(new Error('Processing failed'))
        .mockResolvedValueOnce({ success: true });

      const result = await orderProcessingService.processBatch(orders, {
        continueOnError: true,
      });

      expect(result.successful).toBe(2);
      expect(result.failed).toBe(1);
      expect(result.errors).toHaveLength(1);
    });
  });
});
