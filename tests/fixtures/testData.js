/**
 * Test data fixtures for the application test suite
 */

export const mockShopData = {
  id: 1,
  domain: 'test-shop.myshopify.com',
  name: 'Test Shop',
  email: '<EMAIL>',
  currency: 'USD',
  timezone: 'America/New_York',
  createdAt: new Date('2024-01-01T00:00:00Z'),
  updatedAt: new Date('2024-01-01T00:00:00Z'),
};

export const mockOrderData = {
  id: 12345,
  name: '#1001',
  email: '<EMAIL>',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  total_price: '29.99',
  subtotal_price: '24.99',
  total_tax: '5.00',
  currency: 'USD',
  financial_status: 'paid',
  fulfillment_status: null,
  line_items: [
    {
      id: 67890,
      variant_id: 11111,
      title: 'Test Product',
      quantity: 1,
      price: '24.99',
      sku: 'TEST-SKU-001',
      fulfillment_service: 'manual',
      product_id: 22222,
    },
  ],
  shipping_address: {
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    address1: '123 Test St',
    city: 'Test City',
    province: 'Test State',
    country: 'United States',
    zip: '12345',
  },
  billing_address: {
    first_name: 'John',
    last_name: 'Doe',
    address1: '123 Test St',
    city: 'Test City',
    province: 'Test State',
    country: 'United States',
    zip: '12345',
  },
};

export const mockShipStationOrderData = {
  orderId: 12345,
  orderNumber: '1001',
  orderKey: 'test-order-key',
  orderDate: '2024-01-01T00:00:00.0000000',
  createDate: '2024-01-01T00:00:00.0000000',
  modifyDate: '2024-01-01T00:00:00.0000000',
  paymentDate: '2024-01-01T00:00:00.0000000',
  shipByDate: '2024-01-02T00:00:00.0000000',
  orderStatus: 'awaiting_shipment',
  customerId: 54321,
  customerUsername: '<EMAIL>',
  customerEmail: '<EMAIL>',
  billTo: {
    name: 'John Doe',
    company: '',
    street1: '123 Test St',
    street2: '',
    street3: '',
    city: 'Test City',
    state: 'Test State',
    postalCode: '12345',
    country: 'US',
    phone: '',
    residential: true,
  },
  shipTo: {
    name: 'John Doe',
    company: '',
    street1: '123 Test St',
    street2: '',
    street3: '',
    city: 'Test City',
    state: 'Test State',
    postalCode: '12345',
    country: 'US',
    phone: '',
    residential: true,
  },
  items: [
    {
      orderItemId: 98765,
      lineItemKey: 'test-line-item-key',
      sku: 'TEST-SKU-001',
      name: 'Test Product',
      imageUrl: '',
      weight: {
        value: 1.0,
        units: 'ounces',
      },
      quantity: 1,
      unitPrice: 24.99,
      taxAmount: 5.00,
      shippingAmount: 0.00,
      warehouseLocation: '',
      options: [],
      productId: 22222,
      fulfillmentSku: '',
      adjustment: false,
      upc: '',
      createDate: '2024-01-01T00:00:00.0000000',
      modifyDate: '2024-01-01T00:00:00.0000000',
    },
  ],
  orderTotal: 29.99,
  amountPaid: 29.99,
  taxAmount: 5.00,
  shippingAmount: 0.00,
  customerNotes: '',
  internalNotes: '',
  gift: false,
  giftMessage: '',
  paymentMethod: 'Credit Card',
  requestedShippingService: 'Standard',
  carrierCode: '',
  serviceCode: '',
  packageCode: '',
  confirmation: 'none',
  shipDate: null,
  holdUntilDate: null,
  weight: {
    value: 1.0,
    units: 'ounces',
  },
  dimensions: null,
  insuranceOptions: {
    provider: 'none',
    insureShipment: false,
    insuredValue: 0.0,
  },
  internationalOptions: {
    contents: null,
    customsItems: null,
    nonDelivery: null,
  },
  advancedOptions: {
    warehouseId: 12345,
    nonMachinable: false,
    saturdayDelivery: false,
    containsAlcohol: false,
    mergedOrSplit: false,
    mergedIds: [],
    parentId: null,
    storeId: 67890,
    customField1: '',
    customField2: '',
    customField3: '',
    source: 'Shopify',
    billToParty: null,
    billToAccount: null,
    billToPostalCode: null,
    billToCountryCode: null,
    billToMyOtherAccount: null,
  },
  tagIds: [],
  userId: null,
  externallyFulfilled: false,
  externallyFulfilledBy: '',
};

export const mockShippingCostData = {
  id: 1,
  shop: 'test-shop.myshopify.com',
  orderId: '12345',
  orderName: '#1001',
  shipmentCost: 5.99,
  trackingNumber: 'TEST*********',
  carrierCode: 'usps',
  serviceCode: 'usps_priority_mail',
  shipDate: '2024-01-02',
  createdAt: new Date('2024-01-02T00:00:00Z'),
  updatedAt: new Date('2024-01-02T00:00:00Z'),
};

export const mockInvoiceBalanceData = {
  id: 1,
  shop: 'test-shop.myshopify.com',
  orderId: '12345',
  orderName: '#1001',
  totalBalance: 24.99,
  costOfGoods: 15.00,
  createdAt: new Date('2024-01-01T00:00:00Z'),
  updatedAt: new Date('2024-01-01T00:00:00Z'),
};

export const mockWebhookData = {
  id: 1,
  webhookId: 'webhook-123',
  topic: 'orders/create',
  shop: 'test-shop.myshopify.com',
  payload: JSON.stringify(mockOrderData),
  processedAt: new Date('2024-01-01T00:00:00Z'),
  createdAt: new Date('2024-01-01T00:00:00Z'),
};

export const mockReconciliationJobData = {
  id: 1,
  type: 'scheduled',
  status: 'completed',
  shop: 'test-shop.myshopify.com',
  startDate: '2024-01-01',
  endDate: '2024-01-31',
  totalOrders: 100,
  processedOrders: 100,
  errorCount: 0,
  createdAt: new Date('2024-01-01T00:00:00Z'),
  updatedAt: new Date('2024-01-01T00:00:00Z'),
  completedAt: new Date('2024-01-01T01:00:00Z'),
};

export const mockShipStationStoreData = {
  storeId: 67890,
  storeName: 'Test Store',
  marketplaceId: 1,
  marketplaceName: 'Shopify',
  accountName: 'test-account',
  email: '<EMAIL>',
  integrationUrl: 'https://test-shop.myshopify.com',
  active: true,
  companyName: 'Test Company',
  phone: '************',
  publicEmail: '<EMAIL>',
  website: 'https://test-shop.myshopify.com',
  refreshDate: '2024-01-01T00:00:00.0000000',
  lastRefreshAttempt: '2024-01-01T00:00:00.0000000',
  createDate: '2024-01-01T00:00:00.0000000',
  modifyDate: '2024-01-01T00:00:00.0000000',
  autoRefresh: true,
};

export const mockShipStationShipmentData = {
  shipmentId: *********,
  orderId: 12345,
  orderKey: 'test-order-key',
  userId: 'test-user-id',
  customerEmail: '<EMAIL>',
  orderNumber: '1001',
  createDate: '2024-01-02T00:00:00.0000000',
  shipDate: '2024-01-02T00:00:00.0000000',
  shipmentCost: 5.99,
  insuranceCost: 0.00,
  trackingNumber: 'TEST*********',
  isReturnLabel: false,
  batchNumber: 'BATCH001',
  carrierCode: 'usps',
  serviceCode: 'usps_priority_mail',
  packageCode: 'package',
  confirmation: 'delivery',
  warehouseId: 12345,
  voided: false,
  voidDate: null,
  marketplaceNotified: true,
  notifyErrorMessage: null,
  shipTo: {
    name: 'John Doe',
    company: '',
    street1: '123 Test St',
    street2: '',
    street3: '',
    city: 'Test City',
    state: 'Test State',
    postalCode: '12345',
    country: 'US',
    phone: '',
    residential: true,
  },
  weight: {
    value: 1.0,
    units: 'ounces',
  },
  dimensions: {
    units: 'inches',
    length: 6.0,
    width: 4.0,
    height: 2.0,
  },
  insuranceOptions: {
    provider: 'none',
    insureShipment: false,
    insuredValue: 0.0,
  },
  advancedOptions: {
    warehouseId: 12345,
    nonMachinable: false,
    saturdayDelivery: false,
    containsAlcohol: false,
    storeId: 67890,
    customField1: '',
    customField2: '',
    customField3: '',
    source: 'Shopify',
    mergedOrSplit: false,
    mergedIds: [],
    parentId: null,
    billToParty: null,
    billToAccount: null,
    billToPostalCode: null,
    billToCountryCode: null,
    billToMyOtherAccount: null,
  },
  shipmentItems: [
    {
      orderItemId: 98765,
      lineItemKey: 'test-line-item-key',
      sku: 'TEST-SKU-001',
      name: 'Test Product',
      imageUrl: '',
      weight: {
        value: 1.0,
        units: 'ounces',
      },
      quantity: 1,
      unitPrice: 24.99,
      warehouseLocation: '',
      options: [],
      productId: 22222,
      fulfillmentSku: '',
    },
  ],
  labelData: null,
  formData: null,
};

// Environment-specific test data
export const testEnvironments = {
  development: {
    database: {
      url: 'postgresql://test:test@localhost:5432/test_dev',
    },
    shopify: {
      apiKey: 'dev-api-key',
      apiSecret: 'dev-api-secret',
    },
  },
  test: {
    database: {
      url: 'postgresql://test:test@localhost:5432/test',
    },
    shopify: {
      apiKey: 'test-api-key',
      apiSecret: 'test-api-secret',
    },
  },
  production: {
    database: {
      url: 'postgresql://prod:prod@localhost:5432/prod',
    },
    shopify: {
      apiKey: 'prod-api-key',
      apiSecret: 'prod-api-secret',
    },
  },
};
