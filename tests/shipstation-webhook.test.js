import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { action } from '../app/routes/api.shipstation-webhook';
import axios from 'axios';
import { hasProcessedWebhook, markWebhookProcessed } from '../app/models/ProcessedWebhook.server';
import { getStoreMappingByStoreId } from '../app/models/ShipStationStoreMapping.server';
import { updateShippingCost, updateFulfillmentCost } from '../app/models/ShippingCost.server';

// Mock the ProcessedWebhook.server functions
jest.mock('../app/models/ProcessedWebhook.server', () => ({
  hasProcessedWebhook: jest.fn(),
  markWebhookProcessed: jest.fn(),
}));

// Mock the ShippingCost.server functions
jest.mock('../app/models/ShippingCost.server', () => ({
  updateShippingCost: jest.fn(),
  updateFulfillmentCost: jest.fn(),
}));

describe('ShipStation Webhook Handler', () => {
  // Create a mock request
  const createMockRequest = (payload, headers = {}) => {
    return {
      method: 'POST',
      headers: {
        get: (name) => headers[name] || null,
      },
      json: () => Promise.resolve(payload),
    };
  };

  beforeEach(() => {
    // Reset all mocks before each test
    jest.resetAllMocks();

    // Set up default mock implementations
    hasProcessedWebhook.mockResolvedValue(false);
    markWebhookProcessed.mockResolvedValue({ id: 1 });
    getStoreMappingByStoreId.mockResolvedValue({ shop: 'test-shop.myshopify.com' });
    updateShippingCost.mockResolvedValue({ id: 1 });
    updateFulfillmentCost.mockResolvedValue({ id: 1 });

    // Mock axios.get to return a successful response with labels
    axios.get.mockResolvedValue({
      data: {
        labels: [
          {
            label_id: '123',
            shipment_cost: {
              amount: '10.50'
            }
          }
        ]
      }
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should reject non-POST requests', async () => {
    const request = {
      method: 'GET',
    };

    const response = await action({ request });
    const data = await response.json();

    expect(response.status).toBe(405);
    expect(data.error).toBe('Method not allowed');
  });

  it('should check for duplicate webhooks and skip processing if already processed', async () => {
    // Mock hasProcessedWebhook to return true (webhook already processed)
    hasProcessedWebhook.mockResolvedValue(true);

    const payload = {
      resource_type: 'SHIP_NOTIFY',
      resource_url: 'https://api.shipstation.com/shipments/1?storeId=12345'
    };

    const request = createMockRequest(payload);
    const response = await action({ request });
    const data = await response.json();

    expect(hasProcessedWebhook).toHaveBeenCalled();
    expect(data.message).toBe('Webhook already processed');
    expect(updateShippingCost).not.toHaveBeenCalled();
    expect(updateFulfillmentCost).not.toHaveBeenCalled();
  });

  it('should process a new webhook and mark it as processed', async () => {
    // Mock hasProcessedWebhook to return false (webhook not processed yet)
    hasProcessedWebhook.mockResolvedValue(false);

    // Mock axios to return a response with shipping cost
    axios.get.mockResolvedValue({
      data: {
        shipmentCost: 15.75,
        storeId: '12345',
        labels: [
          {
            label_id: '123',
            shipment_cost: {
              amount: '10.50'
            }
          }
        ]
      }
    });

    const payload = {
      resource_type: 'SHIP_NOTIFY',
      resource_data: {
        shipmentId: '12345',
        storeId: '67890',
        shipmentCost: 15.75
      }
    };

    const request = createMockRequest(payload);
    const response = await action({ request });
    const data = await response.json();

    expect(hasProcessedWebhook).toHaveBeenCalled();
    expect(markWebhookProcessed).toHaveBeenCalled();
    expect(data.success).toBe(true);
  });

  it('should handle direct payload processing without resource_url', async () => {
    const payload = {
      resource_type: 'SHIP_NOTIFY',
      resource_data: {
        shipmentId: '12345',
        storeId: '67890',
        shipmentCost: 15.75
      }
    };

    const request = createMockRequest(payload);
    const response = await action({ request });
    const data = await response.json();

    expect(hasProcessedWebhook).toHaveBeenCalled();
    expect(updateShippingCost).toHaveBeenCalledWith(
      expect.any(String),
      expect.any(String),
      expect.any(Number),
      expect.any(Number),
      expect.any(Object)
    );
    expect(updateFulfillmentCost).toHaveBeenCalledWith(
      expect.any(String),
      1.50,
      expect.any(Object)
    );
    expect(markWebhookProcessed).toHaveBeenCalled();
    expect(data.success).toBe(true);
  });

  it('should ignore non-shipment notifications', async () => {
    const payload = {
      resource_type: 'SOME_OTHER_TYPE',
    };

    const request = createMockRequest(payload);
    const response = await action({ request });
    const data = await response.json();

    expect(hasProcessedWebhook).toHaveBeenCalled();
    expect(data.message).toBe('Ignored non-shipment webhook');
    expect(updateShippingCost).not.toHaveBeenCalled();
    expect(updateFulfillmentCost).not.toHaveBeenCalled();
  });

  it('should validate API key if provided', async () => {
    const payload = {
      resource_type: 'SHIP_NOTIFY',
    };

    // Invalid API key
    const requestWithInvalidKey = createMockRequest(payload, {
      'X-API-Key': 'invalid_key'
    });

    const responseWithInvalidKey = await action({ request: requestWithInvalidKey });
    const dataWithInvalidKey = await responseWithInvalidKey.json();

    expect(responseWithInvalidKey.status).toBe(401);
    expect(dataWithInvalidKey.error).toBe('Invalid API key');

    // Valid API key
    const requestWithValidKey = createMockRequest(payload, {
      'X-API-Key': 'test_webhook_key'
    });

    await action({ request: requestWithValidKey });

    // Should proceed with processing
    expect(hasProcessedWebhook).toHaveBeenCalled();
  });
});
