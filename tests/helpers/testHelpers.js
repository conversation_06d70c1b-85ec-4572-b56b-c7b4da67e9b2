/**
 * Test helper functions for the application test suite
 */

import { jest } from '@jest/globals';

/**
 * Creates a mock database instance with common methods
 */
export function createMockDatabase() {
  return {
    shop: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    invoiceBalance: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    shippingCost: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    processedWebhook: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    reconciliationJob: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    $transaction: jest.fn((callback) => callback(this)),
    $disconnect: jest.fn(),
  };
}

/**
 * Creates a mock API client with common methods
 */
export function createMockApiClient() {
  return {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    request: jest.fn(),
  };
}

/**
 * Creates a mock service container
 */
export function createMockServiceContainer() {
  return {
    get: jest.fn(),
    register: jest.fn(),
    has: jest.fn(),
  };
}

/**
 * Creates a mock Express request object
 */
export function createMockRequest(overrides = {}) {
  return {
    method: 'GET',
    url: '/',
    headers: {},
    query: {},
    params: {},
    body: {},
    ...overrides,
  };
}

/**
 * Creates a mock Express response object
 */
export function createMockResponse() {
  const res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    end: jest.fn().mockReturnThis(),
    setHeader: jest.fn().mockReturnThis(),
    locals: {},
  };
  return res;
}

/**
 * Creates a mock next function for middleware testing
 */
export function createMockNext() {
  return jest.fn();
}

/**
 * Waits for a specified amount of time
 */
export function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Creates a test timeout wrapper
 */
export function withTimeout(fn, timeout = 5000) {
  return async (...args) => {
    return Promise.race([
      fn(...args),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Test timeout')), timeout)
      )
    ]);
  };
}

/**
 * Asserts that a function throws an error with a specific message
 */
export async function expectToThrow(fn, expectedMessage) {
  try {
    await fn();
    throw new Error('Expected function to throw');
  } catch (error) {
    if (expectedMessage && !error.message.includes(expectedMessage)) {
      throw new Error(`Expected error message to contain "${expectedMessage}", got "${error.message}"`);
    }
  }
}

/**
 * Creates a mock logger
 */
export function createMockLogger() {
  return {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  };
}

/**
 * Resets all mocks in an object
 */
export function resetMocks(mockObject) {
  Object.values(mockObject).forEach(mock => {
    if (typeof mock === 'function' && mock.mockReset) {
      mock.mockReset();
    } else if (typeof mock === 'object' && mock !== null) {
      resetMocks(mock);
    }
  });
}

/**
 * Creates a mock configuration object
 */
export function createMockConfig(overrides = {}) {
  return {
    database: {
      url: 'postgresql://test:test@localhost:5432/test',
    },
    shopify: {
      apiKey: 'test-api-key',
      apiSecret: 'test-api-secret',
    },
    shipstation: {
      apiKey: 'test-shipstation-key',
      apiSecret: 'test-shipstation-secret',
    },
    app: {
      url: 'https://test-app.example.com',
      port: 3000,
    },
    ...overrides,
  };
}

/**
 * Creates a performance measurement wrapper
 */
export function measurePerformance(fn) {
  return async (...args) => {
    const start = process.hrtime.bigint();
    const result = await fn(...args);
    const end = process.hrtime.bigint();
    const duration = Number(end - start) / 1000000; // Convert to milliseconds
    
    return {
      result,
      duration,
    };
  };
}

/**
 * Creates a memory usage tracker
 */
export function trackMemoryUsage() {
  const initial = process.memoryUsage();
  
  return {
    getUsage: () => {
      const current = process.memoryUsage();
      return {
        heapUsed: current.heapUsed - initial.heapUsed,
        heapTotal: current.heapTotal - initial.heapTotal,
        external: current.external - initial.external,
        rss: current.rss - initial.rss,
      };
    },
    reset: () => {
      Object.assign(initial, process.memoryUsage());
    },
  };
}
