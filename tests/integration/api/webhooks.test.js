/**
 * Integration Tests for Webhook API Endpoints
 */

import { jest } from '@jest/globals';
import request from 'supertest';
import { createMockDatabase, createMockApiClient } from '../../helpers/testHelpers.js';
import { mockOrderData, mockShopData } from '../../fixtures/testData.js';

// Mock the OrderProcessingService
jest.mock('../../../app/services/order/OrderProcessingService.js', () => ({
  OrderProcessingService: jest.fn().mockImplementation(() => ({
    processOrder: jest.fn().mockResolvedValue({
      success: true,
      orderId: '12345',
      shopDomain: 'test-shop.myshopify.com',
      result: {
        orderId: '12345',
        lineItems: [],
        totals: { total: 29.99 },
      },
    }),
  })),
}));

// Mock the database
jest.mock('../../../app/db.server.js', () => ({
  default: createMockDatabase(),
}));

// Mock Shopify authentication
jest.mock('../../../app/shopify.server.js', () => ({
  authenticate: {
    webhook: jest.fn().mockImplementation(() => ({
      admin: {
        graphql: jest.fn().mockResolvedValue({
          json: () => Promise.resolve({
            data: {
              order: mockOrderData,
            },
          }),
        }),
      },
      session: {
        shop: 'test-shop.myshopify.com',
      },
      payload: mockOrderData,
      topic: 'ORDERS_CREATE',
    })),
  },
}));

describe('Webhook API Integration Tests', () => {
  let app;
  let mockPrisma;

  beforeEach(async () => {
    // Import the app after mocks are set up
    const { default: createApp } = await import('../../../app/entry.server.jsx');
    app = createApp();
    
    mockPrisma = createMockDatabase();
    
    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('POST /webhooks/app', () => {
    it('should process order creation webhook successfully', async () => {
      const webhookPayload = {
        id: '12345',
        name: '#1001',
        line_items: [
          {
            id: '67890',
            product_id: '22222',
            variant_id: '11111',
            title: 'Test Product',
            sku: 'TEST-SKU-001',
            quantity: 1,
            price: '24.99',
          },
        ],
        financial_status: 'paid',
        fulfillment_status: null,
      };

      const response = await request(app)
        .post('/webhooks/app')
        .set('X-Shopify-Topic', 'orders/create')
        .set('X-Shopify-Shop-Domain', 'test-shop.myshopify.com')
        .set('X-Shopify-Webhook-Id', 'webhook-123')
        .send(webhookPayload);

      expect(response.status).toBe(200);
    });

    it('should handle webhook authentication errors', async () => {
      // Mock authentication failure
      const { authenticate } = await import('../../../app/shopify.server.js');
      authenticate.webhook.mockImplementationOnce(() => {
        throw new Error('Authentication failed');
      });

      const response = await request(app)
        .post('/webhooks/app')
        .set('X-Shopify-Topic', 'orders/create')
        .set('X-Shopify-Shop-Domain', 'test-shop.myshopify.com')
        .send({});

      expect(response.status).toBe(401);
    });

    it('should handle order processing errors gracefully', async () => {
      // Mock OrderProcessingService to throw an error
      const { OrderProcessingService } = await import('../../../app/services/order/OrderProcessingService.js');
      const mockInstance = new OrderProcessingService();
      mockInstance.processOrder.mockRejectedValueOnce(new Error('Processing failed'));

      const webhookPayload = {
        id: '12345',
        line_items: [
          {
            id: '67890',
            title: 'Test Product',
            sku: 'TEST-SKU-001',
            quantity: 1,
          },
        ],
      };

      const response = await request(app)
        .post('/webhooks/app')
        .set('X-Shopify-Topic', 'orders/create')
        .set('X-Shopify-Shop-Domain', 'test-shop.myshopify.com')
        .send(webhookPayload);

      // Should still return 200 to avoid webhook retries
      expect(response.status).toBe(200);
    });

    it('should skip processing for duplicate webhooks', async () => {
      // Mock that webhook was already processed
      mockPrisma.processedWebhook.findFirst.mockResolvedValueOnce({
        id: 1,
        webhookId: 'webhook-123',
        processedAt: new Date(),
      });

      const webhookPayload = {
        id: '12345',
        line_items: [],
      };

      const response = await request(app)
        .post('/webhooks/app')
        .set('X-Shopify-Topic', 'orders/create')
        .set('X-Shopify-Shop-Domain', 'test-shop.myshopify.com')
        .set('X-Shopify-Webhook-Id', 'webhook-123')
        .send(webhookPayload);

      expect(response.status).toBe(200);
      
      // Should not call OrderProcessingService
      const { OrderProcessingService } = await import('../../../app/services/order/OrderProcessingService.js');
      const mockInstance = new OrderProcessingService();
      expect(mockInstance.processOrder).not.toHaveBeenCalled();
    });

    it('should handle app uninstalled webhook', async () => {
      const response = await request(app)
        .post('/webhooks/app')
        .set('X-Shopify-Topic', 'app/uninstalled')
        .set('X-Shopify-Shop-Domain', 'test-shop.myshopify.com')
        .send({});

      expect(response.status).toBe(200);
    });

    it('should handle unsupported webhook topics', async () => {
      const response = await request(app)
        .post('/webhooks/app')
        .set('X-Shopify-Topic', 'unsupported/topic')
        .set('X-Shopify-Shop-Domain', 'test-shop.myshopify.com')
        .send({});

      expect(response.status).toBe(404);
    });
  });

  describe('POST /api/shipstation-webhook', () => {
    it('should process ShipStation webhook successfully', async () => {
      const shipstationPayload = {
        resource_type: 'ORDER',
        resource_id: '12345',
        resource_url: 'https://ssapi.shipstation.com/orders/12345',
      };

      const response = await request(app)
        .post('/api/shipstation-webhook')
        .set('X-API-Key', process.env.SHIPSTATION_WEBHOOK_KEY)
        .send(shipstationPayload);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should reject invalid API key', async () => {
      const response = await request(app)
        .post('/api/shipstation-webhook')
        .set('X-API-Key', 'invalid-key')
        .send({});

      expect(response.status).toBe(401);
    });

    it('should ignore non-order webhooks', async () => {
      const shipstationPayload = {
        resource_type: 'SHIPMENT',
        resource_id: '12345',
      };

      const response = await request(app)
        .post('/api/shipstation-webhook')
        .set('X-API-Key', process.env.SHIPSTATION_WEBHOOK_KEY)
        .send(shipstationPayload);

      expect(response.status).toBe(200);
      expect(response.body.message).toContain('Ignoring non-order webhook');
    });

    it('should handle duplicate ShipStation webhooks', async () => {
      // Mock that webhook was already processed
      mockPrisma.processedWebhook.findFirst.mockResolvedValueOnce({
        id: 1,
        webhookId: '12345',
        processedAt: new Date(),
      });

      const shipstationPayload = {
        resource_type: 'ORDER',
        resource_id: '12345',
        resource_url: 'https://ssapi.shipstation.com/orders/12345',
      };

      const response = await request(app)
        .post('/api/shipstation-webhook')
        .set('X-API-Key', process.env.SHIPSTATION_WEBHOOK_KEY)
        .send(shipstationPayload);

      expect(response.status).toBe(200);
      expect(response.body.message).toContain('already processed');
    });
  });

  describe('POST /api/scheduled-reconciliation', () => {
    beforeEach(() => {
      // Mock ReconciliationOrchestrator
      jest.mock('../../../app/services/reconciliation/ReconciliationOrchestrator.js', () => ({
        ReconciliationOrchestrator: jest.fn().mockImplementation(() => ({
          executeReconciliation: jest.fn().mockResolvedValue({
            success: true,
            jobId: 123,
            result: {
              processedOrders: 10,
              skippedOrders: 2,
              failedOrders: 0,
            },
          }),
        })),
      }));
    });

    it('should trigger scheduled reconciliation successfully', async () => {
      const reconciliationPayload = {
        type: 'standard',
        daysToLookBack: 7,
        ordersPerShop: 50,
      };

      const response = await request(app)
        .post('/api/scheduled-reconciliation')
        .set('X-API-Key', process.env.RECONCILIATION_API_KEY)
        .send(reconciliationPayload);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.results).toBeDefined();
    });

    it('should handle missing order reconciliation', async () => {
      const reconciliationPayload = {
        type: 'check-missing',
        daysToLookBack: 30,
      };

      const response = await request(app)
        .post('/api/scheduled-reconciliation')
        .set('X-API-Key', process.env.RECONCILIATION_API_KEY)
        .send(reconciliationPayload);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should handle ShipStation sync reconciliation', async () => {
      const reconciliationPayload = {
        type: 'shipstation-sync',
        daysToLookBack: 7,
      };

      const response = await request(app)
        .post('/api/scheduled-reconciliation')
        .set('X-API-Key', process.env.RECONCILIATION_API_KEY)
        .send(reconciliationPayload);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should reject unauthorized requests', async () => {
      const response = await request(app)
        .post('/api/scheduled-reconciliation')
        .send({ type: 'standard' });

      expect(response.status).toBe(401);
    });

    it('should handle reconciliation errors', async () => {
      // Mock ReconciliationOrchestrator to throw an error
      const { ReconciliationOrchestrator } = await import('../../../app/services/reconciliation/ReconciliationOrchestrator.js');
      const mockInstance = new ReconciliationOrchestrator();
      mockInstance.executeReconciliation.mockRejectedValueOnce(new Error('Reconciliation failed'));

      const response = await request(app)
        .post('/api/scheduled-reconciliation')
        .set('X-API-Key', process.env.RECONCILIATION_API_KEY)
        .send({ type: 'standard' });

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/scheduled-reconciliation', () => {
    it('should return API status and running jobs', async () => {
      // Mock running jobs
      mockPrisma.reconciliationJob.findMany.mockResolvedValueOnce([
        {
          id: 1,
          type: 'scheduled',
          status: 'running',
          createdAt: new Date(),
        },
      ]);

      const response = await request(app)
        .get('/api/scheduled-reconciliation');

      expect(response.status).toBe(200);
      expect(response.body.status).toContain('available');
      expect(response.body.types).toBeDefined();
      expect(Array.isArray(response.body.types)).toBe(true);
    });
  });
});
