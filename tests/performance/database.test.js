/**
 * Performance Tests for Database Operations
 */

import { jest } from '@jest/globals';
import { measurePerformance, trackMemoryUsage, createMockDatabase } from '../helpers/testHelpers.js';
import { OrderProcessingService } from '../../app/services/order/OrderProcessingService.js';
import { ShopRepository } from '../../app/repositories/ShopRepository.js';
import { InvoiceBalanceRepository } from '../../app/repositories/InvoiceBalanceRepository.js';

describe('Database Performance Tests', () => {
  let mockPrisma;
  let shopRepository;
  let invoiceBalanceRepository;
  let orderProcessingService;
  let memoryTracker;

  beforeEach(() => {
    mockPrisma = createMockDatabase();
    shopRepository = new ShopRepository({ prisma: mockPrisma });
    invoiceBalanceRepository = new InvoiceBalanceRepository({ prisma: mockPrisma });
    orderProcessingService = new OrderProcessingService({ prisma: mockPrisma });
    memoryTracker = trackMemoryUsage();

    // Setup performance-oriented mocks
    mockPrisma.shop.findMany.mockImplementation(async (query) => {
      // Simulate database query time based on limit
      const limit = query?.take || 100;
      const delay = Math.min(limit * 2, 1000); // Max 1 second delay
      await new Promise(resolve => setTimeout(resolve, delay));
      
      return Array.from({ length: limit }, (_, i) => ({
        id: i + 1,
        shop: `shop-${i}.myshopify.com`,
        name: `Shop ${i}`,
        createdAt: new Date(),
        updatedAt: new Date(),
      }));
    });

    mockPrisma.invoiceBalance.findMany.mockImplementation(async (query) => {
      const limit = query?.take || 100;
      const delay = Math.min(limit * 1, 500); // Max 500ms delay
      await new Promise(resolve => setTimeout(resolve, delay));
      
      return Array.from({ length: limit }, (_, i) => ({
        id: i + 1,
        shop: 'test-shop.myshopify.com',
        orderId: `order-${i}`,
        totalBalance: 29.99,
        createdAt: new Date(),
        updatedAt: new Date(),
      }));
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
    
    // Log memory usage if excessive
    const memoryUsage = memoryTracker.getUsage();
    if (memoryUsage.heapUsed > 100 * 1024 * 1024) { // 100MB threshold
      console.warn('High memory usage in performance test:', memoryUsage);
    }
  });

  describe('Query Performance', () => {
    it('should retrieve shops within acceptable time limits', async () => {
      const result = await measurePerformance(async () => {
        return await shopRepository.findMany({
          limit: 100,
        });
      });

      expect(result.result).toHaveLength(100);
      expect(result.duration).toBeLessThan(2000); // Should complete within 2 seconds
    });

    it('should handle large shop datasets efficiently', async () => {
      const result = await measurePerformance(async () => {
        return await shopRepository.findMany({
          limit: 1000,
        });
      });

      expect(result.result).toHaveLength(1000);
      expect(result.duration).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should retrieve invoice balances with pagination efficiently', async () => {
      const pageSize = 50;
      const totalPages = 10;
      let totalRecords = 0;

      const result = await measurePerformance(async () => {
        for (let page = 0; page < totalPages; page++) {
          const records = await invoiceBalanceRepository.findMany({
            limit: pageSize,
            offset: page * pageSize,
          });
          totalRecords += records.length;
        }
        return totalRecords;
      });

      expect(result.result).toBe(pageSize * totalPages);
      expect(result.duration).toBeLessThan(10000); // Should complete within 10 seconds
    });

    it('should perform complex queries within time limits', async () => {
      // Mock complex aggregation query
      mockPrisma.invoiceBalance.aggregate.mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 800)); // Simulate complex query
        return {
          _sum: { totalBalance: 2999.50 },
          _count: { id: 100 },
          _avg: { totalBalance: 29.995 },
        };
      });

      const result = await measurePerformance(async () => {
        return await invoiceBalanceRepository.getBalanceSummary(
          'test-shop.myshopify.com',
          new Date('2024-01-01'),
          new Date('2024-01-31')
        );
      });

      expect(result.result).toBeDefined();
      expect(result.duration).toBeLessThan(1500); // Should complete within 1.5 seconds
    });
  });

  describe('Batch Operations Performance', () => {
    it('should process batch inserts efficiently', async () => {
      const batchSize = 100;
      const records = Array.from({ length: batchSize }, (_, i) => ({
        shop: 'test-shop.myshopify.com',
        orderId: `batch-order-${i}`,
        totalBalance: 29.99,
      }));

      mockPrisma.invoiceBalance.createMany.mockImplementation(async (data) => {
        const count = data.data.length;
        await new Promise(resolve => setTimeout(resolve, count * 5)); // 5ms per record
        return { count };
      });

      const result = await measurePerformance(async () => {
        return await mockPrisma.invoiceBalance.createMany({
          data: records,
        });
      });

      expect(result.result.count).toBe(batchSize);
      expect(result.duration).toBeLessThan(2000); // Should complete within 2 seconds
    });

    it('should handle batch updates efficiently', async () => {
      const batchSize = 50;
      const updates = Array.from({ length: batchSize }, (_, i) => ({
        id: i + 1,
        totalBalance: 35.99,
      }));

      mockPrisma.$transaction.mockImplementation(async (operations) => {
        await new Promise(resolve => setTimeout(resolve, operations.length * 10)); // 10ms per operation
        return operations.map(() => ({ id: 1, updated: true }));
      });

      const result = await measurePerformance(async () => {
        const operations = updates.map(update => 
          mockPrisma.invoiceBalance.update({
            where: { id: update.id },
            data: { totalBalance: update.totalBalance },
          })
        );
        return await mockPrisma.$transaction(operations);
      });

      expect(result.result).toHaveLength(batchSize);
      expect(result.duration).toBeLessThan(3000); // Should complete within 3 seconds
    });

    it('should process order batch efficiently', async () => {
      const orders = Array.from({ length: 20 }, (_, i) => ({
        orderId: `perf-order-${i}`,
        shopDomain: 'test-shop.myshopify.com',
      }));

      // Mock order processing to simulate realistic timing
      jest.spyOn(orderProcessingService, 'processOrder').mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 100)); // 100ms per order
        return {
          success: true,
          orderId: 'test',
          shopDomain: 'test-shop.myshopify.com',
          result: { totals: { total: 29.99 } },
        };
      });

      const result = await measurePerformance(async () => {
        return await orderProcessingService.processBatch(orders, {
          batchSize: 5,
          continueOnError: true,
        });
      });

      expect(result.result.totalProcessed).toBe(20);
      expect(result.duration).toBeLessThan(8000); // Should complete within 8 seconds (parallel processing)
    });
  });

  describe('Memory Usage Tests', () => {
    it('should not leak memory during large queries', async () => {
      const initialMemory = memoryTracker.getUsage();

      // Perform multiple large queries
      for (let i = 0; i < 10; i++) {
        await shopRepository.findMany({ limit: 500 });
        
        // Force garbage collection if available
        if (global.gc) {
          global.gc();
        }
      }

      const finalMemory = memoryTracker.getUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;

      // Memory increase should be minimal (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });

    it('should handle memory efficiently during batch processing', async () => {
      const initialMemory = memoryTracker.getUsage();

      // Process multiple batches
      for (let batch = 0; batch < 5; batch++) {
        const records = Array.from({ length: 200 }, (_, i) => ({
          shop: 'test-shop.myshopify.com',
          orderId: `memory-test-${batch}-${i}`,
          totalBalance: 29.99,
        }));

        await mockPrisma.invoiceBalance.createMany({
          data: records,
        });

        // Force garbage collection between batches
        if (global.gc) {
          global.gc();
        }
      }

      const finalMemory = memoryTracker.getUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;

      // Memory increase should be reasonable (less than 75MB)
      expect(memoryIncrease).toBeLessThan(75 * 1024 * 1024);
    });
  });

  describe('Concurrent Operations', () => {
    it('should handle concurrent reads efficiently', async () => {
      const concurrentQueries = 10;
      const queries = Array.from({ length: concurrentQueries }, (_, i) => 
        shopRepository.findMany({
          limit: 50,
          where: { shop: { contains: `shop-${i}` } },
        })
      );

      const result = await measurePerformance(async () => {
        return await Promise.all(queries);
      });

      expect(result.result).toHaveLength(concurrentQueries);
      expect(result.duration).toBeLessThan(3000); // Should complete within 3 seconds
    });

    it('should handle concurrent writes with proper isolation', async () => {
      const concurrentWrites = 5;
      const writes = Array.from({ length: concurrentWrites }, (_, i) => 
        invoiceBalanceRepository.create({
          shop: 'test-shop.myshopify.com',
          orderId: `concurrent-${i}`,
          totalBalance: 29.99,
        })
      );

      const result = await measurePerformance(async () => {
        return await Promise.all(writes);
      });

      expect(result.result).toHaveLength(concurrentWrites);
      expect(result.duration).toBeLessThan(2000); // Should complete within 2 seconds
    });
  });

  describe('Database Connection Performance', () => {
    it('should establish connections quickly', async () => {
      const result = await measurePerformance(async () => {
        // Simulate connection establishment
        await new Promise(resolve => setTimeout(resolve, 50));
        return await mockPrisma.shop.findFirst();
      });

      expect(result.duration).toBeLessThan(200); // Should connect within 200ms
    });

    it('should handle connection pooling efficiently', async () => {
      const connectionTests = Array.from({ length: 20 }, () => 
        measurePerformance(async () => {
          await new Promise(resolve => setTimeout(resolve, 25)); // Simulate query
          return await mockPrisma.shop.findFirst();
        })
      );

      const results = await Promise.all(connectionTests);
      const averageTime = results.reduce((sum, r) => sum + r.duration, 0) / results.length;

      expect(averageTime).toBeLessThan(100); // Average should be under 100ms
    });
  });
});
