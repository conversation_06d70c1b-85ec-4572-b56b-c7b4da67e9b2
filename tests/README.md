# Test Suite for ATP Invoice Preview

This directory contains tests for the ATP Invoice Preview application, focusing on the ShipStation webhook integration.

## Running Tests

You can run the tests using the following npm scripts:

```bash
# Run all tests once
npm test

# Run tests in watch mode (useful during development)
npm run test:watch

# Run tests with coverage report
npm run test:coverage
```

## Test Structure

The test suite is organized as follows:

- `setup.js`: Test setup file that configures the testing environment, mocks, and environment variables
- `shipstation-webhook.test.js`: Tests for the ShipStation webhook handler
- `processed-webhook.test.js`: Tests for the ProcessedWebhook model and deduplication logic
- `shipping-cost.test.js`: Tests for the ShippingCost model and cost tracking
- `shipstation-integration.test.js`: Integration tests that verify the entire webhook flow

## Mocking Strategy

The tests use Vitest's mocking capabilities to:

1. Mock the database interactions
2. Mock external API calls (ShipStation API)
3. Mock environment variables

This allows us to test the application logic without requiring actual database connections or external API access.

## Coverage

Running `npm run test:coverage` will generate a coverage report showing which parts of the code are covered by tests. The coverage report is available in the `coverage` directory after running the command.

## Adding New Tests

When adding new tests:

1. Follow the existing patterns for mocking and test structure
2. Ensure tests are isolated and don't depend on external state
3. Use descriptive test names that explain what is being tested
4. Add appropriate assertions to verify the expected behavior
