# Development Environment Setup Guide

## Overview

This guide walks you through setting up a complete development environment for the Americans United Inc Shopify App. The setup includes all necessary tools, databases, tunneling, and configuration for local development.

## Prerequisites

### System Requirements
- **Operating System**: macOS, Windows 10/11, or Linux
- **Node.js**: Version 18.x or higher
- **npm**: Version 9.x or higher (comes with Node.js)
- **Git**: Latest version
- **Docker Desktop**: For PostgreSQL database

### Hardware Requirements
- **RAM**: Minimum 8GB, recommended 16GB
- **Storage**: At least 5GB free space
- **CPU**: Multi-core processor recommended

## Step 1: Install Core Development Tools

### 1.1 Node.js and npm
**macOS (using Homebrew):**
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Node.js
brew install node@18
```

**Windows:**
1. Download Node.js 18.x from [nodejs.org](https://nodejs.org)
2. Run the installer and follow the setup wizard
3. Verify installation: `node --version` and `npm --version`

**Linux (Ubuntu/Debian):**
```bash
# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 1.2 Git
**macOS:**
```bash
brew install git
```

**Windows:**
Download from [git-scm.com](https://git-scm.com/download/win)

**Linux:**
```bash
sudo apt-get install git
```

### 1.3 Docker Desktop
1. Download Docker Desktop from [docker.com](https://www.docker.com/products/docker-desktop)
2. Install and start Docker Desktop
3. Verify installation: `docker --version`

## Step 2: Clone and Setup Project

### 2.1 Clone Repository
```bash
# Clone the repository
git clone https://github.com/your-username/americans-united-inc.git
cd americans-united-inc

# Install dependencies
npm install
```

### 2.2 Verify Project Structure
Ensure you have these key directories:
```
americans-united-inc/
├── app/                    # Remix application
├── express-server/         # Multistore Express server
├── shared/                 # Shared utilities
├── prisma/                 # Database schema
├── tests/                  # Test suite
├── package.json
├── docker-compose.yml
└── .env.example
```

## Step 3: Database Setup (PostgreSQL with Docker)

### 3.1 Start PostgreSQL Container
```bash
# Start PostgreSQL and pgAdmin containers
docker-compose up -d

# Verify containers are running
docker-compose ps
```

### 3.2 Verify Database Connection
```bash
# Check PostgreSQL logs
docker-compose logs postgres

# Test connection
docker exec -it americans-united-postgres-dev psql -U dev_user -d americans_united_dev -c "SELECT version();"
```

### 3.3 Access pgAdmin (Optional)
1. Open browser to `http://localhost:8080`
2. Login with:
   - **Email**: `<EMAIL>`
   - **Password**: `admin_password`
3. Add server connection:
   - **Host**: `postgres`
   - **Port**: `5432`
   - **Database**: `americans_united_dev`
   - **Username**: `dev_user`
   - **Password**: `dev_password`

## Step 4: Environment Configuration

### 4.1 Create Environment File
```bash
# Copy example environment file
cp .env.example .env.americans-united-inc

# Edit the environment file
nano .env.americans-united-inc
```

### 4.2 Configure Environment Variables
Update `.env.americans-united-inc` with your values:

```bash
# Database Configuration
DATABASE_URL="postgresql://dev_user:dev_password@localhost:5432/americans_united_dev"
TEST_DATABASE_URL="postgresql://dev_user:dev_password@localhost:5432/americans_united_test"

# Shopify App Configuration
SHOPIFY_API_KEY="your_shopify_api_key"
SHOPIFY_API_SECRET="your_shopify_api_secret"
SCOPES="read_orders,read_products,read_inventory,write_orders"
SHOPIFY_APP_URL="https://your-tunnel-url.ngrok.io"

# ShipStation Configuration
SHIPSTATION_V1_KEY="your_shipstation_v1_key"
SHIPSTATION_V1_SECRET="your_shipstation_v1_secret"
SHIPSTATION_API_KEY="your_shipstation_v2_key"
SHIPSTATION_WEBHOOK_KEY="your_webhook_key"

# Security
SCHEDULED_JOB_TOKEN="your_secure_random_token"
WEBHOOK_VERIFICATION_SECRET="your_webhook_secret"

# Application Configuration
ADMIN_SHOP="your-admin-store.myshopify.com"
NODE_ENV="development"
PORT="3000"
EXPRESS_PORT="4000"

# Multistore Configuration (if needed)
MULTISTORE_ENABLED="false"
SUPPORTED_STORES=""
```

### 4.3 Generate Secure Tokens
```bash
# Generate random tokens for security
node -e "console.log('SCHEDULED_JOB_TOKEN=' + require('crypto').randomBytes(32).toString('hex'))"
node -e "console.log('WEBHOOK_VERIFICATION_SECRET=' + require('crypto').randomBytes(32).toString('hex'))"
```

## Step 5: Tunnel Setup (ngrok)

### 5.1 Install ngrok
**macOS:**
```bash
brew install ngrok
```

**Windows:**
1. Download from [ngrok.com](https://ngrok.com/download)
2. Extract to a folder in your PATH

**Linux:**
```bash
# Download and install
wget https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-linux-amd64.tgz
tar xvzf ngrok-v3-stable-linux-amd64.tgz
sudo mv ngrok /usr/local/bin
```

### 5.2 Configure ngrok
```bash
# Sign up at ngrok.com and get your authtoken
ngrok config add-authtoken YOUR_AUTHTOKEN

# Start tunnel (in a separate terminal)
ngrok http 3000
```

### 5.3 Update Environment with Tunnel URL
Update your `.env.americans-united-inc`:
```bash
SHOPIFY_APP_URL="https://your-tunnel-url.ngrok.io"
```

## Step 6: Database Migration

### 6.1 Run Prisma Migrations
```bash
# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate dev --name init

# Verify migration
npx prisma db push
```

### 6.2 Seed Database (Optional)
```bash
# If you have seed data
npx prisma db seed
```

### 6.3 Verify Database Schema
```bash
# Open Prisma Studio to view database
npx prisma studio
```

## Step 7: Shopify App Configuration

### 7.1 Create Development App
1. Go to [partners.shopify.com](https://partners.shopify.com)
2. Create new app: "Americans United Inc - Development"
3. Configure app settings:
   - **App URL**: `https://your-tunnel-url.ngrok.io/app`
   - **Allowed redirection URLs**: `https://your-tunnel-url.ngrok.io/auth/callback`
   - **Webhook URL**: `https://your-tunnel-url.ngrok.io/webhooks/app`

### 7.2 Configure Webhooks
Set up these webhooks in your development app:
- `orders/create` → `https://your-tunnel-url.ngrok.io/webhooks/app`
- `orders/updated` → `https://your-tunnel-url.ngrok.io/webhooks/app`
- `orders/fulfilled` → `https://your-tunnel-url.ngrok.io/webhooks/app`
- `orders/cancelled` → `https://your-tunnel-url.ngrok.io/webhooks/app`
- `app/uninstalled` → `https://your-tunnel-url.ngrok.io/webhooks/app`

### 7.3 Update Environment with App Credentials
Update `.env.americans-united-inc` with your app's:
- **Client ID** → `SHOPIFY_API_KEY`
- **Client Secret** → `SHOPIFY_API_SECRET`

## Step 8: Start Development Servers

### 8.1 Start All Services
```bash
# Terminal 1: Start ngrok tunnel
ngrok http 3000

# Terminal 2: Start Remix development server
npm run dev

# Terminal 3: Start Express multistore server (if needed)
npm run start:express

# Terminal 4: Monitor logs
docker-compose logs -f postgres
```

### 8.2 Verify Services
Check that all services are running:
- **Remix App**: `http://localhost:3000`
- **Express Server**: `http://localhost:4000`
- **Database**: `localhost:5432`
- **pgAdmin**: `http://localhost:8080`
- **Tunnel**: `https://your-tunnel-url.ngrok.io`

## Step 9: Install and Test App

### 9.1 Install App on Development Store
1. Get installation URL from Partners Dashboard
2. Install app on your development store
3. Verify app loads correctly

### 9.2 Test Core Functionality
1. **Authentication**: Verify app loads in Shopify admin
2. **Navigation**: Test menu navigation
3. **Webhooks**: Create a test order and verify webhook processing
4. **Database**: Check that data is being stored correctly

### 9.3 Run Test Suite
```bash
# Run all tests
npm test

# Run specific test types
npm run test:unit
npm run test:integration
```

## Step 10: Development Workflow

### 10.1 Daily Development Routine
```bash
# 1. Start Docker containers
docker-compose up -d

# 2. Start ngrok tunnel (keep URL consistent)
ngrok http 3000

# 3. Start development server
npm run dev

# 4. Start coding!
```

### 10.2 Code Quality Tools
```bash
# Run linting
npm run lint

# Format code
npm run format

# Type checking (if using TypeScript)
npm run type-check
```

### 10.3 Database Management
```bash
# Reset database
npx prisma migrate reset

# View database
npx prisma studio

# Generate new migration
npx prisma migrate dev --name your_migration_name
```

## Step 11: Troubleshooting

### 11.1 Common Issues

#### Issue: "Cannot connect to database"
**Solution**:
```bash
# Check if PostgreSQL container is running
docker-compose ps

# Restart containers
docker-compose down && docker-compose up -d

# Check logs
docker-compose logs postgres
```

#### Issue: "Shopify app not loading"
**Solution**:
1. Verify ngrok tunnel is active
2. Check that SHOPIFY_APP_URL matches ngrok URL
3. Verify app configuration in Partners Dashboard
4. Check browser console for errors

#### Issue: "Webhooks not working"
**Solution**:
1. Verify webhook URLs in Partners Dashboard
2. Check ngrok tunnel is accessible
3. Test webhook endpoint manually
4. Check app logs for webhook processing

### 11.2 Debug Tools
```bash
# View application logs
npm run dev # Shows Remix logs

# View database queries
# Add to .env: DATABASE_URL with ?log=query

# Monitor webhook delivery
# Check Partners Dashboard webhook delivery logs
```

### 11.3 Reset Development Environment
```bash
# Complete reset
docker-compose down -v
npm run clean
rm -rf node_modules
npm install
docker-compose up -d
npx prisma migrate reset
npm run dev
```

## Step 12: IDE Setup (Optional)

### 12.1 VS Code Extensions
Install these helpful extensions:
- **Prisma** - Database schema support
- **ES7+ React/Redux/React-Native snippets** - React snippets
- **Shopify Liquid** - Liquid template support
- **GitLens** - Git integration
- **Prettier** - Code formatting
- **ESLint** - Code linting

### 12.2 VS Code Settings
Create `.vscode/settings.json`:
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "typescript.preferences.importModuleSpecifier": "relative",
  "emmet.includeLanguages": {
    "javascript": "javascriptreact"
  }
}
```

## Step 13: Next Steps

### 13.1 Learn the Codebase
1. Review `codebase-documentation.md`
2. Explore the route handlers in `app/routes/`
3. Understand the service layer in `app/services/`
4. Study the database models in `app/models/`

### 13.2 Development Best Practices
1. **Always test locally** before deploying
2. **Use feature branches** for new development
3. **Write tests** for new functionality
4. **Follow existing code patterns** and conventions
5. **Document new features** and changes

### 13.3 Deployment Preparation
When ready to deploy:
1. Review `deployment-guide.md`
2. Set up production environment variables
3. Test with production-like data
4. Verify all integrations work correctly

## Support and Resources

### Documentation
- **Shopify App Development**: [shopify.dev](https://shopify.dev)
- **Remix Framework**: [remix.run](https://remix.run)
- **Prisma ORM**: [prisma.io](https://prisma.io)
- **Fly.io Deployment**: [fly.io/docs](https://fly.io/docs)

### Getting Help
- **Technical Issues**: <EMAIL>
- **Shopify Development**: [community.shopify.com](https://community.shopify.com)
- **Stack Overflow**: Tag questions with `shopify`, `remix`, `prisma`

This development environment setup provides everything needed for productive local development of the Americans United Inc Shopify App.
