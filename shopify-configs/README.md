# Shopify App Configurations

This directory contains separate Shopify app configurations for each store in the Americans United Inc multistore setup.

## Store Configurations

| Store | Config File | Store Domain | Client ID |
|-------|-------------|--------------|-----------|
| Stylish Stitches | `stylish-stitches.shopify.app.toml` | `stylish-stitches-jordin-kolman.myshopify.com` | `4f62408a57ff8202871bbff18316a1d6` |
| PLD App | `pld-app.shopify.app.toml` | `pld-app-development-store.myshopify.com` | `8c37f2753cfd7a2bafdf5f6bdfd17557` |
| 75th RRA | `75th-rra.shopify.app.toml` | `75th-rra-app-development-store.myshopify.com` | `6a5f4bd11e96c9433fdca65fa1b7a2e2` |

## Deployment Options

### 1. Deploy to All Stores (Recommended)

Deploy to all stores simultaneously:

```bash
# Sequential deployment (safer, default)
npm run deploy:all

# Parallel deployment (faster)
npm run deploy:all:parallel
```

### 2. Deploy to Individual Stores

Deploy to specific stores:

```bash
# Deploy to Stylish Stitches only
npm run deploy:stylish-stitches

# Deploy to PLD App only
npm run deploy:pld-app

# Deploy to 75th RRA only
npm run deploy:75th-rra
```

### 3. Manual Deployment with Shopify CLI

```bash
# Deploy to specific store using config file
shopify app deploy --config=shopify-configs/stylish-stitches.shopify.app.toml

# Deploy with force flag (skip confirmations)
shopify app deploy --config=shopify-configs/pld-app.shopify.app.toml --force
```

## Deployment Scripts

### Node.js Script (Cross-platform)
- **File**: `scripts/deploy-all-apps.js`
- **Usage**: `node scripts/deploy-all-apps.js [--parallel|--sequential]`
- **Features**:
  - Cross-platform compatibility (Windows, macOS, Linux)
  - Colored console output
  - Detailed logging to `logs/deployments/`
  - Error handling and retry logic
  - Deployment summary report

### Bash Script (Unix/Linux/macOS)
- **File**: `scripts/deploy-all-apps.sh`
- **Usage**: `./scripts/deploy-all-apps.sh [--parallel|--sequential]`
- **Features**:
  - Native bash implementation
  - Parallel and sequential deployment modes
  - Comprehensive error handling
  - Deployment logs and summaries

## Configuration Details

Each configuration file contains:

- **Client ID**: Unique identifier for the Shopify app
- **Application URL**: The URL where your app is hosted
- **Webhooks**: Configured for `app/uninstalled`, `orders/create`, and `products/update`
- **Access Scopes**: `read_products`, `read_orders`, `write_orders`
- **Auth Redirects**: Multiple callback URLs for different authentication flows

## Environment Variables

Make sure your `.env` file contains the corresponding API secrets:

```bash
# Stylish Stitches Jordin Kolman
SHOPIFY_API_KEY_STYLISH_STITCHES_JORDIN_KOLMAN=4f62408a57ff8202871bbff18316a1d6
SHOPIFY_API_SECRET_STYLISH_STITCHES_JORDIN_KOLMAN=your-secret-here

# PLD App Development Store
SHOPIFY_API_KEY_PLD_APP_DEVELOPMENT_STORE=8c37f2753cfd7a2bafdf5f6bdfd17557
SHOPIFY_API_SECRET_PLD_APP_DEVELOPMENT_STORE=your-secret-here

# 75th RRA App Development Store
SHOPIFY_API_KEY_75TH_RRA_APP_DEVELOPMENT_STORE=6a5f4bd11e96c9433fdca65fa1b7a2e2
SHOPIFY_API_SECRET_75TH_RRA_APP_DEVELOPMENT_STORE=your-secret-here
```

## Logs

Deployment logs are stored in `logs/deployments/` with the format:
- `{store-name}_{timestamp}.log`

Example: `stylish-stitches_2024-01-15T10-30-45.log`

## Troubleshooting

### Common Issues

1. **Shopify CLI not found**
   ```bash
   npm install -g @shopify/cli @shopify/theme
   ```

2. **Config file not found**
   - Ensure you're running from the project root
   - Check that config files exist in `shopify-configs/`

3. **Authentication errors**
   - Verify client IDs match your Shopify Partner Dashboard
   - Check that API secrets are correctly set in `.env`

4. **Deployment failures**
   - Check deployment logs in `logs/deployments/`
   - Verify your app meets Shopify's requirements
   - Ensure all dependencies are installed

### Getting Help

- Check deployment logs for specific error messages
- Verify Shopify CLI is up to date: `shopify version`
- Ensure you're authenticated: `shopify auth login`

## Security Notes

- Never commit API secrets to version control
- Keep client IDs and secrets secure
- Regularly rotate API credentials
- Use environment variables for sensitive data

## Adding New Stores

To add a new store:

1. Create a new Shopify app in your Partner Dashboard
2. Create a new `.toml` config file in this directory
3. Add the client ID and secret to your `.env` file
4. Update the deployment scripts to include the new store
5. Add npm scripts for individual deployment
