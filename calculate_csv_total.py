import csv

total_carrier_fee = 0
shipment_count = 0

with open('/home/<USER>/workspace/shopify-apps/americans-united-inc/test-data/atp-shipments-5-25.csv', 'r') as file:
    reader = csv.DictReader(file)
    for row in reader:
        carrier_fee = float(row['Carrier - Fee'])
        total_carrier_fee += carrier_fee
        shipment_count += 1

# Add 10% markup
markup = total_carrier_fee * 0.10
total_with_markup = total_carrier_fee + markup

# Add fulfillment costs ($1.50 per shipment)
fulfillment_costs = shipment_count * 1.50
total_expected = total_with_markup + fulfillment_costs

print(f'Total Carrier Fees: ${total_carrier_fee:.2f}')
print(f'10% Markup: ${markup:.2f}')
print(f'Total with Markup: ${total_with_markup:.2f}')
print(f'Shipment Count: {shipment_count}')
print(f'Fulfillment Costs (${1.50} x {shipment_count}): ${fulfillment_costs:.2f}')
print(f'Expected Total: ${total_expected:.2f}')
