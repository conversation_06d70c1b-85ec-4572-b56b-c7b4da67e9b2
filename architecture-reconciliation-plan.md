# Architecture Reconciliation Plan - Americans United Inc Shopify App

## Executive Summary

This plan establishes a single, streamlined architecture by consolidating the mixed service-oriented architecture with legacy utility functions, implementing unused DTOs, and applying all fixes from the codebase analysis. The plan addresses technical debt, architectural inconsistencies, and establishes clear patterns for future development.

## Current State Analysis

### ✅ **Already Implemented (Keep)**
- Service layer foundation (`app/services/`)
- Repository pattern (`app/repositories/`)
- API client abstractions (`app/lib/api/`)
- Configuration management (`app/lib/config/`)
- Dependency injection container (`app/lib/container/`)
- Express multistore server (`express-server/`)
- DTO structure (`app/dto/`)
- Shared utilities (`shared/utils/`)

### ❌ **Problematic Areas (Fix/Remove)**
- Legacy utilities in `app/utils/` (duplicates service functionality)
- Unused DTO implementations
- Mixed architectural patterns in routes
- Circular dependencies between services
- Inconsistent error handling
- Memory leaks in batch processing
- Security vulnerabilities

### 🔄 **Needs Consolidation**
- Duplicate business logic across utils and services
- Inconsistent data validation patterns
- Mixed API client usage (direct vs abstracted)
- Scattered configuration access

## Target Architecture

### **Single Source of Truth Principle**
- **Services**: All business logic
- **Repositories**: All data access
- **DTOs**: All data transformation
- **Utils**: Only pure utility functions (no business logic)
- **Routes**: Only request/response handling

## Phase 1: Critical Fixes & Security (Week 1)

### **1.1 Transaction Safety Implementation**

**Files to Modify:**
- `app/services/order/OrderProcessingService.js` (Lines 78-80, 163-165)
- `app/models/InvoiceBalance.server.js` (Lines 157-178)

**Changes:**
```javascript
// BEFORE: Non-atomic operations
await processOrderLineItems(shop, order, admin);
await markWebhookProcessed(shop, topic, webhookId, orderId);

// AFTER: Atomic transaction
await this.prisma.$transaction(async (tx) => {
  const result = await this.processOrderInternal(orderData, normalizedShopDomain, { tx });
  await this.markWebhookProcessed(shop, topic, webhookId, orderId, tx);
  return result;
});
```

**Timeline:** Day 1-2

### **1.2 Input Validation Framework**

**Files to Create:**
- `app/lib/validation/schemas.js`
- `app/middleware/validation.js`

**Files to Modify:**
- `express-server/routes/webhooks.js` (Lines 50-70)
- `app/routes/api.shipstation-order-webhook.jsx` (Lines 15-30)

**Changes:**
```javascript
// Add comprehensive input validation using Zod schemas
const webhookPayloadSchema = z.object({
  topic: z.string().min(1),
  shop: z.string().min(1),
  payload: z.object({}).passthrough()
});
```

**Timeline:** Day 2-3

### **1.3 Memory Management Fixes**

**Files to Modify:**
- `app/routes/api.shipstation-mapping.jsx` (Lines 100-150)
- `shared/utils/processLargeDataset.js` (Lines 50-100)

**Changes:**
```javascript
// Add garbage collection triggers and memory monitoring
if (isMemoryUsageHigh(0.8)) {
  if (global.gc) global.gc();
  await new Promise(resolve => setTimeout(resolve, 1000));
}
```

**Timeline:** Day 3-4

### **1.4 ID Normalization Standardization**

**Files to Create:**
- `shared/utils/idNormalization.js`

**Files to Modify:**
- `app/models/ProcessedWebhook.server.js` (Use normalized IDs)
- `app/services/order/OrderProcessingService.js` (Use normalized IDs)
- All files handling order IDs

**Changes:**
```javascript
// File: shared/utils/idNormalization.js
export function normalizeOrderId(orderId) {
  if (!orderId) return null;

  // Handle GraphQL ID format: gid://shopify/Order/123456789
  if (typeof orderId === 'string' && orderId.startsWith('gid://shopify/Order/')) {
    return orderId.split('/').pop();
  }

  // Handle numeric ID
  if (typeof orderId === 'number') {
    return orderId.toString();
  }

  // Handle string numeric ID
  if (typeof orderId === 'string' && /^\d+$/.test(orderId)) {
    return orderId;
  }

  throw new Error(`Invalid order ID format: ${orderId}`);
}

export function normalizeShopDomain(shop) {
  if (!shop) return null;

  // Ensure .myshopify.com format
  if (!shop.includes('.myshopify.com')) {
    return `${shop}.myshopify.com`;
  }

  return shop.toLowerCase();
}

export function normalizeVariantId(variantId) {
  if (!variantId) return null;

  // Handle GraphQL ID format: gid://shopify/ProductVariant/123456789
  if (typeof variantId === 'string' && variantId.startsWith('gid://shopify/ProductVariant/')) {
    return variantId.split('/').pop();
  }

  // Handle numeric or string numeric ID
  return variantId.toString();
}

export function normalizeProductId(productId) {
  if (!productId) return null;

  // Handle GraphQL ID format: gid://shopify/Product/123456789
  if (typeof productId === 'string' && productId.startsWith('gid://shopify/Product/')) {
    return productId.split('/').pop();
  }

  // Handle numeric or string numeric ID
  return productId.toString();
}

// Utility to convert numeric ID to GraphQL ID
export function toGraphQLId(type, numericId) {
  return `gid://shopify/${type}/${numericId}`;
}
```

**Update ProcessedWebhook model:**
```javascript
// File: app/models/ProcessedWebhook.server.js
import { normalizeOrderId, normalizeShopDomain } from '../../shared/utils/idNormalization.js';

export async function hasProcessedOrder(shop, orderId) {
  const normalizedShop = normalizeShopDomain(shop);
  const normalizedOrderId = normalizeOrderId(orderId);

  const webhook = await db.processedWebhook.findFirst({
    where: {
      shop: normalizedShop,
      orderId: normalizedOrderId,
    },
  });

  return !!webhook;
}

export async function markOrderProcessed(shop, orderId, topic, webhookId) {
  const normalizedShop = normalizeShopDomain(shop);
  const normalizedOrderId = normalizeOrderId(orderId);

  return await db.processedWebhook.create({
    data: {
      shop: normalizedShop,
      orderId: normalizedOrderId,
      topic,
      webhookId,
      processedAt: new Date(),
    },
  });
}
```

**Impact**: Eliminates duplicate order processing due to ID format mismatches

**Timeline:** Day 4-5

## Phase 2: Architecture Consolidation (Week 2)

### **2.1 Legacy Utility Elimination**

**Files to Remove:**
- `legacy/` (entire directory)
- `app/utils/balance.js` (duplicate of service logic)
- `app/utils/order-processing.js` (moved to services)
- `app/utils/reconciliation.js` (moved to services)
- `app/utils/shipstation-api.js` (replaced by API client)
- `app/utils/shipstation-order-api.js` (replaced by API client)

**Files to Consolidate:**
- `app/utils/memory-cache.js` → `shared/utils/cacheManager.js`
- `app/utils/batch-processing.js` → `shared/utils/processLargeDataset.js`
- `app/utils/json-response.js` → `shared/utils/index.js`

**Timeline:** Day 5-7

### **2.2 Service Layer Standardization**

**Files to Modify:**
- `app/services/order/OrderProcessingService.js` (Break circular dependencies)
- `app/services/reconciliation/ReconciliationOrchestrator.js` (Remove OrderProcessingService import)
- `app/services/shipping/ShippingCostService.js` (Standardize async patterns)

**Changes:**
```javascript
// BEFORE: Circular dependency
import { ReconciliationOrchestrator } from '../reconciliation/ReconciliationOrchestrator.js';

// AFTER: Event-driven communication
this.eventBus.emit('order.processed', { orderId, shopDomain, result });
```

**Timeline:** Day 8-10

### **2.3 DTO Implementation & Usage**

**Files to Complete:**
- `app/dto/order/OrderDTO.js` (Already exists, needs integration)
- `app/dto/order/LineItemDTO.js` (Already exists, needs integration)
- `app/dto/shipping/ShippingCostDTO.js` (Create)
- `app/dto/pricing/PriceDTO.js` (Create)
- `app/dto/common/ApiResponseDTO.js` (Create)
- `app/dto/common/RequestDTO.js` (Create)
- `app/dto/common/ValidationErrorDTO.js` (Create)

**Files to Modify (Use DTOs):**
- `app/services/order/OrderProcessingService.js`
- `app/services/shipping/ShippingCostService.js`
- `app/routes/api.*.jsx` (All API routes)

**Changes:**
```javascript
// BEFORE: Raw data handling
const orderData = await fetchOrder(orderId);
return Response.json(orderData);

// AFTER: DTO standardization
const orderData = await fetchOrder(orderId);
const orderDTO = OrderDTO.fromShopifyOrder(orderData, shop);
return Response.json(ApiResponseDTO.success(orderDTO.toObject()));
```

**Implementation for Missing DTOs:**
```javascript
// File: app/dto/common/ApiResponseDTO.js
export class ApiResponseDTO {
  constructor(success, data = null, error = null, pagination = null) {
    this.success = success;
    this.data = data;
    this.error = error;
    this.pagination = pagination;
    this.timestamp = new Date().toISOString();
  }

  static success(data, pagination = null) {
    return new ApiResponseDTO(true, data, null, pagination);
  }

  static error(message, code = 'UNKNOWN_ERROR') {
    return new ApiResponseDTO(false, null, { message, code }, null);
  }

  static validationError(message, details = null) {
    return new ApiResponseDTO(false, null, {
      message,
      code: 'VALIDATION_ERROR',
      details
    }, null);
  }

  static businessError(message) {
    return new ApiResponseDTO(false, null, {
      message,
      code: 'BUSINESS_ERROR'
    }, null);
  }

  static internalError(message = 'Internal server error') {
    return new ApiResponseDTO(false, null, {
      message,
      code: 'INTERNAL_ERROR'
    }, null);
  }
}

// File: app/dto/common/RequestDTO.js
export class RequestDTO {
  constructor(data = {}) {
    this.requestId = data.requestId || this.generateRequestId();
    this.timestamp = new Date().toISOString();
    this.userAgent = data.userAgent || null;
    this.ip = data.ip || null;
  }

  static async fromRequest(request) {
    const body = await request.json().catch(() => ({}));
    const headers = Object.fromEntries(request.headers.entries());

    return new RequestDTO({
      ...body,
      userAgent: headers['user-agent'],
      ip: headers['x-forwarded-for'] || headers['x-real-ip']
    });
  }

  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// File: app/dto/common/ValidationErrorDTO.js
export class ValidationErrorDTO {
  constructor(field, message, value = null) {
    this.field = field;
    this.message = message;
    this.value = value;
    this.code = 'VALIDATION_ERROR';
  }

  static required(field) {
    return new ValidationErrorDTO(field, `${field} is required`);
  }

  static invalid(field, message, value = null) {
    return new ValidationErrorDTO(field, message, value);
  }

  static format(field, expectedFormat) {
    return new ValidationErrorDTO(
      field,
      `${field} must be in format: ${expectedFormat}`
    );
  }
}
```

**Timeline:** Day 11-14

## Phase 3: Performance & Quality (Week 3)

### **3.1 Database Optimization**

**Files to Modify:**
- `prisma/schema.prisma` (Add composite indexes)
- `app/services/order/processors/ProductProcessor.js` (Fix N+1 queries)
- `app/repositories/PriceRepository.js` (Create for batch operations)

**Changes:**
```prisma
// Add composite indexes for common query patterns
model InvoiceBalance {
  // ... existing fields
  @@index([shop, month, year, category])
  @@index([shop, category])
}

model Price {
  // ... existing fields

  // Optimize price lookups
  @@index([shop, category])
  @@unique([shop, category])
}

// Fix missing foreign key constraints and cascade deletes
model InvoiceTransaction {
  id               Int            @id @default(autoincrement())
  invoiceBalanceId Int
  amount           Decimal
  description      String?
  createdAt        DateTime       @default(now())

  // ADD: Missing foreign key constraint with cascade delete
  invoiceBalance   InvoiceBalance @relation(fields: [invoiceBalanceId], references: [id], onDelete: Cascade)

  @@index([invoiceBalanceId])
}

model ShippingTransaction {
  id             Int          @id @default(autoincrement())
  shippingCostId Int
  amount         Decimal
  description    String?
  createdAt      DateTime     @default(now())

  // ADD: Missing foreign key constraint with cascade delete
  shippingCost   ShippingCost @relation(fields: [shippingCostId], references: [id], onDelete: Cascade)

  @@index([shippingCostId])
}

model InvoiceBalance {
  id           Int                  @id @default(autoincrement())
  shop         String
  month        Int
  year         Int
  category     String
  quantity     Int
  balance      Decimal
  groupId      Int?

  // ADD: Proper cascade delete relationships
  transactions InvoiceTransaction[] @relation(onDelete: Cascade)
  group        BalanceGroup?        @relation(fields: [groupId], references: [id], onDelete: SetNull)

  @@unique([shop, category, month, year])
  @@index([shop, month, year])
  @@index([shop, category])
  @@index([groupId])
}

model ShippingCost {
  id           Int                   @id @default(autoincrement())
  shop         String
  storeId      String
  month        Int
  year         Int
  amount       Decimal
  createdAt    DateTime              @default(now())
  updatedAt    DateTime              @updatedAt

  // ADD: Proper cascade delete relationships
  transactions ShippingTransaction[] @relation(onDelete: Cascade)

  @@index([shop, month, year])
  @@index([storeId, month, year])
  @@index([shop, storeId])
}

// Fix inefficient data types
model ShipStationStoreMapping {
  id       Int     @id @default(autoincrement()) // CHANGED: From String to Int
  storeId  String  @unique // Keep as String for ShipStation compatibility
  shop     String
  storeName String?
  isActive Boolean @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([shop])
  @@index([storeId])
}
```

**N+1 Query Fix Implementation:**
```javascript
// File: app/repositories/PriceRepository.js
export class PriceRepository {
  constructor(prisma) {
    this.prisma = prisma;
  }

  // BEFORE: N+1 queries (called for each line item)
  async getPrice(shop, category) {
    return await this.prisma.price.findFirst({
      where: { shop, category }
    });
  }

  // AFTER: Batch price lookup
  async getPricesBatch(shop, categories) {
    const uniqueCategories = [...new Set(categories)];

    const prices = await this.prisma.price.findMany({
      where: {
        shop,
        category: { in: uniqueCategories }
      }
    });

    // Create lookup map for O(1) access
    const priceMap = new Map();
    prices.forEach(price => {
      priceMap.set(price.category, price);
    });

    return priceMap;
  }

  // Get all prices for a shop (for caching)
  async getAllPricesForShop(shop) {
    const prices = await this.prisma.price.findMany({
      where: { shop }
    });

    const priceMap = new Map();
    prices.forEach(price => {
      priceMap.set(price.category, price);
    });

    return priceMap;
  }
}

// File: app/services/order/processors/ProductProcessor.js
export class ProductProcessor {
  constructor(priceRepository) {
    this.priceRepository = priceRepository;
    this.priceCache = new Map(); // Shop-level price cache
  }

  // BEFORE: N+1 queries
  async processLineItems(order, shop) {
    const results = [];
    for (const lineItem of order.line_items) {
      const category = this.determineCategory(lineItem);
      const price = await this.priceRepository.getPrice(shop, category); // N+1 query
      results.push(this.calculateLineItemTotal(lineItem, price));
    }
    return results;
  }

  // AFTER: Batch processing
  async processLineItems(order, shop) {
    // Extract all categories first
    const categories = order.line_items.map(lineItem =>
      this.determineCategory(lineItem)
    );

    // Single batch query for all prices
    const priceMap = await this.getPricesForShop(shop, categories);

    // Process all line items with cached prices
    const results = order.line_items.map(lineItem => {
      const category = this.determineCategory(lineItem);
      const price = priceMap.get(category);

      if (!price) {
        throw new PriceNotFoundError(`No price found for category: ${category}`);
      }

      return this.calculateLineItemTotal(lineItem, price);
    });

    return results;
  }

  async getPricesForShop(shop, categories) {
    // Check cache first
    const cacheKey = `prices_${shop}`;
    let priceMap = this.priceCache.get(cacheKey);

    if (!priceMap) {
      // Cache miss - load all prices for shop
      priceMap = await this.priceRepository.getAllPricesForShop(shop);
      this.priceCache.set(cacheKey, priceMap);

      // Cache for 5 minutes
      setTimeout(() => {
        this.priceCache.delete(cacheKey);
      }, 5 * 60 * 1000);
    }

    return priceMap;
  }
}
```

**Performance Impact:**
- **Before**: N queries (1 per line item) = 10 line items = 10 database queries
- **After**: 1 query per shop (cached) = 1 database query for entire order
- **Improvement**: ~90% reduction in database queries for order processing

**Timeline:** Day 15-17

### **3.2 API Rate Limiting & Caching**

**Files to Modify:**
- `app/lib/api/shopify/ShopifyClient.js` (Add exponential backoff)
- `app/utils/fulfillment-order-service.js` (Batch API calls)

**Changes:**
```javascript
// Implement exponential backoff for rate limiting
const delay = Math.min(1000 * Math.pow(2, attempt), 30000);
await new Promise(resolve => setTimeout(resolve, delay));
```

**Timeline:** Day 18-19

### **3.3 Code Complexity Reduction**

**Files to Refactor:**
- `app/services/order/processors/ProductProcessor.js` (CC: 28 → <15)
- `app/services/order/OrderProcessingService.js` (CC: 22 → <15)
- `app/services/reconciliation/strategies/ShipStationReconciliation.js` (CC: 19 → <15)

**Strategy:**
- Extract methods for complex conditional logic
- Use strategy pattern for product type processing
- Implement command pattern for order operations

**Timeline:** Day 20-21

## Phase 4: Route Simplification & Critical Issues (Week 4)

### **4.1 Route Handler Standardization**

**Files to Modify:**
- `app/routes/api.shipstation-mapping.jsx` (Extract business logic)
- `app/routes/api.shipstation-order-sync.jsx` (Extract business logic)
- `app/routes/api.shipping-costs.jsx` (Standardize response format)

**Pattern:**
```javascript
// BEFORE: Business logic in route
export async function action({ request }) {
  // 50+ lines of business logic
  const result = await complexBusinessOperation();
  return Response.json(result);
}

// AFTER: Delegated to service
export async function action({ request }) {
  try {
    const requestDTO = await RequestDTO.fromRequest(request);
    const result = await this.businessService.processRequest(requestDTO);
    return Response.json(ApiResponseDTO.success(result));
  } catch (error) {
    return this.errorHandler.handleError(error);
  }
}
```

**Timeline:** Day 22-25

### **4.2 Global Error Handling & Loading States**

**Files to Create:**
- `app/middleware/errorHandler.js`
- `app/lib/errors/ErrorTypes.js`
- `app/components/ErrorBoundary.jsx`
- `app/components/LoadingState.jsx`

**Files to Modify:**
- All route files (Implement consistent error handling and loading states)
- All service files (Use custom error types)
- `app/root.jsx` (Add global error boundary)

**Changes:**
```javascript
// Global Error Boundary
export function ErrorBoundary() {
  return (
    <div className="error-boundary">
      <h2>Something went wrong</h2>
      <p>Please try refreshing the page or contact support.</p>
    </div>
  );
}

// Consistent Loading States
export function LoadingState({ message = "Loading..." }) {
  return (
    <div className="loading-state">
      <Spinner size="large" />
      <Text>{message}</Text>
    </div>
  );
}
```

**Timeline:** Day 26-28

### **4.3 Dropped Orders Investigation & Fix**

**Files to Investigate:**
- `app/routes/webhooks.app.jsx` (Webhook processing)
- `app/services/order/OrderProcessingService.js` (Order processing logic)
- `app/models/ProcessedWebhook.server.js` (Deduplication logic)

**Files to Create:**
- `app/services/monitoring/OrderTrackingService.js`
- `app/utils/order-audit.js`

**Investigation Steps:**
1. Add comprehensive logging to webhook processing
2. Implement order tracking from webhook to completion
3. Add alerts for processing failures
4. Create audit trail for dropped orders

**Changes:**
```javascript
// Enhanced webhook processing with audit trail
export async function action({ request }) {
  const auditId = generateRequestId();

  try {
    console.log(`[AUDIT:${auditId}] Webhook received`, { headers, topic });

    // Process with comprehensive error handling
    const result = await processWebhookWithAudit(payload, auditId);

    console.log(`[AUDIT:${auditId}] Webhook processed successfully`);
    return new Response(null, { status: 200 });
  } catch (error) {
    console.error(`[AUDIT:${auditId}] Webhook processing failed`, error);
    // Alert on critical failures
    await alertOnDroppedOrder(payload, error, auditId);
    throw error;
  }
}
```

**Timeline:** Day 29-30

### **4.4 Price Validation & Error Handling**

**Files to Modify:**
- `app/models/Price.server.js` (Add default price validation)
- `app/services/order/processors/ProductProcessor.js` (Add price error handling)

**Changes:**
```javascript
// Add error checking for missing default prices
export async function getPrice(shop, category) {
  try {
    const price = await db.price.findFirst({
      where: { shop, category }
    });

    if (!price) {
      console.warn(`No price found for shop: ${shop}, category: ${category}`);
      // Return default price or throw specific error
      throw new PriceNotFoundError(`No price configured for category: ${category}`);
    }

    return price;
  } catch (error) {
    console.error(`Error fetching price for ${shop}/${category}:`, error);
    throw error;
  }
}
```

**Timeline:** Day 30-31

## Phase 5: Advanced Features & Infrastructure (Week 5)

### **5.1 iFrame Protection & Security**

**Files to Create:**
- `app/middleware/security.server.js`
- `app/middleware/iframe-protection.js`

**Files to Modify:**
- `app/entry.server.jsx` (Add security headers)
- `app/root.jsx` (Add CSP headers)

**Changes:**
```javascript
// iFrame protection middleware
export function addSecurityHeaders(request, responseHeaders) {
  // Prevent clickjacking
  responseHeaders.set('X-Frame-Options', 'DENY');
  responseHeaders.set('Content-Security-Policy', "frame-ancestors 'none'");

  // Additional security headers
  responseHeaders.set('X-Content-Type-Options', 'nosniff');
  responseHeaders.set('X-XSS-Protection', '1; mode=block');
  responseHeaders.set('Referrer-Policy', 'strict-origin-when-cross-origin');
}
```

**Timeline:** Day 35-36

### **5.2 Pack Handling Improvements**

**Files to Create:**
- `app/services/order/processors/PackProcessor.js`
- `app/utils/pack-detection.js`

**Files to Modify:**
- `app/services/order/processors/ProductProcessor.js` (Enhanced pack logic)

**Changes:**
```javascript
// Enhanced pack detection and handling
export class PackProcessor {
  detectPackType(sku, title, variantTitle) {
    const packIndicators = [
      /pack\s*of\s*(\d+)/i,
      /(\d+)\s*pack/i,
      /(\d+)pk/i,
      /-(\d+)pk-/i
    ];

    for (const indicator of packIndicators) {
      const match = (sku + ' ' + title + ' ' + variantTitle).match(indicator);
      if (match) {
        return {
          isPack: true,
          quantity: parseInt(match[1]),
          packType: this.determinePackType(sku)
        };
      }
    }

    return { isPack: false };
  }

  calculatePackPricing(basePrice, packQuantity, packType) {
    // Implement pack-specific pricing logic
    const discountRates = {
      'sticker': 0.15,  // 15% discount for sticker packs
      'patch': 0.10,    // 10% discount for patch packs
      'default': 0.05   // 5% default pack discount
    };

    const discount = discountRates[packType] || discountRates.default;
    return basePrice * packQuantity * (1 - discount);
  }
}
```

**Timeline:** Day 37-38

### **5.3 Inventory Tracking Implementation Plan**

**Files to Create:**
- `docs/inventory-tracking-plan.md`
- `app/services/inventory/InventoryTrackingService.js`
- `app/models/InventorySnapshot.server.js`

**Database Schema Updates:**
```prisma
// Add to prisma/schema.prisma
model InventorySnapshot {
  id          Int      @id @default(autoincrement())
  shop        String
  productId   String
  variantId   String
  sku         String
  quantity    Int
  reserved    Int      @default(0)
  available   Int
  snapshotAt  DateTime @default(now())

  @@index([shop, variantId])
  @@index([shop, sku])
  @@index([snapshotAt])
}

model InventoryMovement {
  id          Int      @id @default(autoincrement())
  shop        String
  variantId   String
  sku         String
  orderId     String?
  movementType String  // 'sale', 'restock', 'adjustment'
  quantity    Int      // Positive for increase, negative for decrease
  reason      String?
  createdAt   DateTime @default(now())

  @@index([shop, variantId])
  @@index([orderId])
}
```

**Implementation Plan:**
1. **Phase 1**: Basic inventory snapshots
2. **Phase 2**: Real-time inventory tracking
3. **Phase 3**: Low stock alerts
4. **Phase 4**: Automatic reorder points
5. **Phase 5**: Inventory forecasting

**Timeline:** Day 39-42

### **5.4 Structured Logging Implementation**

**Files to Create:**
- `app/lib/logging/Logger.js`
- `app/lib/logging/LogFormatter.js`
- `app/middleware/request-logging.js`

**Files to Modify:**
- All service files (Replace console.log with structured logging)

**Changes:**
```javascript
// Structured logging implementation
export class Logger {
  constructor(context) {
    this.context = context;
  }

  info(message, metadata = {}) {
    this.log('info', message, metadata);
  }

  error(message, error = null, metadata = {}) {
    this.log('error', message, { ...metadata, error: error?.stack });
  }

  warn(message, metadata = {}) {
    this.log('warn', message, metadata);
  }

  log(level, message, metadata) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context: this.context,
      ...metadata,
      requestId: this.getRequestId()
    };

    // Write to file in production, console in development
    if (process.env.NODE_ENV === 'production') {
      this.writeToFile(logEntry);
    } else {
      console.log(JSON.stringify(logEntry, null, 2));
    }
  }
}
```

**Timeline:** Day 43-45

## Phase 6: Infrastructure & Optimization (Week 6)

### **6.1 Monitoring & Health Checks**

**Files to Create:**
- `app/services/monitoring/HealthCheckService.js`
- `app/services/monitoring/MetricsCollector.js`
- `app/routes/api.health.jsx`
- `app/routes/api.metrics.jsx`

**Files to Modify:**
- `app/routes/healthz.jsx` (Enhanced health checks)
- `express-server/server.js` (Add monitoring endpoints)

**Changes:**
```javascript
// Comprehensive health check service
export class HealthCheckService {
  async performHealthCheck() {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkShopifyAPI(),
      this.checkShipStationAPI(),
      this.checkMemoryUsage(),
      this.checkDiskSpace()
    ]);

    const results = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      checks: {},
      uptime: process.uptime()
    };

    checks.forEach((check, index) => {
      const checkName = ['database', 'shopify', 'shipstation', 'memory', 'disk'][index];
      results.checks[checkName] = {
        status: check.status === 'fulfilled' ? 'healthy' : 'unhealthy',
        details: check.value || check.reason?.message
      };

      if (check.status === 'rejected') {
        results.status = 'unhealthy';
      }
    });

    return results;
  }
}
```

**Timeline:** Day 46-48

### **6.2 External API Caching**

**Files to Create:**
- `app/lib/cache/ApiCache.js`
- `app/lib/cache/CacheStrategies.js`
- `app/services/cache/ShipStationCache.js`

**Files to Modify:**
- `app/lib/api/shipstation/ShipStationClient.js` (Add caching)
- `app/lib/api/shopify/ShopifyClient.js` (Add caching)

**Changes:**
```javascript
// API response caching
export class ApiCache {
  constructor(options = {}) {
    this.ttl = options.ttl || 300000; // 5 minutes default
    this.cache = new Map();
  }

  async get(key, fetcher) {
    const cached = this.cache.get(key);

    if (cached && Date.now() - cached.timestamp < this.ttl) {
      return cached.data;
    }

    const data = await fetcher();
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });

    return data;
  }
}
```

**Timeline:** Day 49-51

### **6.3 GraphQL Query Optimization & Database Optimization**

**Files to Create:**
- `app/lib/graphql/QueryBuilder.js`
- `app/lib/graphql/QueryOptimizer.js`
- `app/lib/database/QueryOptimizer.js`

**Files to Modify:**
- `app/lib/api/shopify/GraphQLClient.js` (Add query optimization)
- `prisma/schema.prisma` (Add missing indexes)

**Timeline:** Day 52-54

### **6.4 CI/CD Pipeline & Auto-healing**

**Files to Create:**
- `.github/workflows/ci.yml`
- `.github/workflows/deploy.yml`
- `scripts/auto-healing.js`
- `infrastructure/monitoring/alerts.yml`

**Timeline:** Day 55-57

### **File-by-File Change Matrix**

| File | Action | Priority | Estimated Hours |
|------|--------|----------|----------------|
| `app/services/order/OrderProcessingService.js` | Refactor (transactions, complexity) | High | 4 |
| `app/routes/webhooks.app.jsx` | Fix dropped orders investigation | High | 6 |
| `shared/utils/idNormalization.js` | Create ID normalization utility | High | 2 |
| `app/models/ProcessedWebhook.server.js` | Update to use normalized IDs | High | 1 |
| `app/models/Price.server.js` | Add default price error checking | High | 2 |
| `app/services/reconciliation/ReconciliationOrchestrator.js` | Fix circular deps | High | 2 |
| `app/routes/api.shipstation-mapping.jsx` | Extract business logic | High | 3 |
| `app/utils/fulfillment-order-service.js` | Replace with API client | High | 2 |
| `app/components/ErrorBoundary.jsx` | Create global error boundary | High | 3 |
| `app/services/webhook/WebhookSubscriptionService.js` | Webhook audit & validation | Medium | 4 |
| `app/services/order/processors/PackProcessor.js` | Improve pack handling | Medium | 3 |
| `app/middleware/security.server.js` | iFrame protection | Medium | 2 |
| `app/lib/logging/Logger.js` | Structured logging | Medium | 4 |
| `app/services/monitoring/HealthCheckService.js` | Monitoring & health checks | Medium | 3 |
| `app/lib/cache/ApiCache.js` | External API caching | Medium | 4 |
| `app/lib/graphql/QueryBuilder.js` | GraphQL optimization | Medium | 3 |
| `legacy/` (entire directory) | Remove | Low | 1 |
| `app/utils/balance.js` | Remove (duplicate) | Low | 0.5 |
| `app/dto/shipping/ShippingCostDTO.js` | Create & implement | Low | 2 |
| `app/dto/common/ApiResponseDTO.js` | Create & implement | Medium | 1 |
| `app/dto/common/RequestDTO.js` | Create & implement | Medium | 1 |
| `app/dto/common/ValidationErrorDTO.js` | Create & implement | Medium | 1 |
| `prisma/schema.prisma` | Add indexes | Low | 1 |
| All API routes | Standardize responses | Low | 6 |
| `.github/workflows/ci.yml` | CI/CD pipeline | Low | 4 |
| `scripts/auto-healing.js` | Auto-healing setup | Low | 3 |

### **Testing Strategy**

**Week 1:** Unit tests for critical fixes (transaction safety, input validation)
**Week 2:** Integration tests for consolidated services and DTO implementation
**Week 3:** Performance tests for optimizations and database queries
**Week 4:** End-to-end tests for route changes and error handling
**Week 5:** Unit tests for webhook processing, pack handling, and security features
**Week 6:** Integration tests for monitoring, caching, and infrastructure components

### **Test Coverage Goals**
- **Critical Business Logic**: >95% coverage (order processing, reconciliation)
- **API Endpoints**: >90% coverage (all routes and error cases)
- **Service Layer**: >85% coverage (all services and utilities)
- **Overall Application**: >80% coverage (target from current ~45%)

### **Rollback Plan**

- Git branches for each phase
- Database migration rollback scripts
- Feature flags for new implementations
- Monitoring alerts for performance regressions

## Success Metrics

### **Code Quality**
- Cyclomatic complexity: Average <8 (Current: 12)
- Code duplication: <3% (Current: ~15%)
- Test coverage: >80% (Current: ~45%)

### **Performance**
- API response time: <200ms (Current: 200-500ms)
- Memory usage: Stable under load
- Database query time: <50ms

### **Architecture**
- Zero circular dependencies
- Single responsibility per module
- Consistent error handling patterns
- Complete DTO usage

## Risk Mitigation

### **High Risk Areas**
1. **Order Processing Changes** - Critical business logic
   - Mitigation: Comprehensive testing, gradual rollout
2. **Database Schema Changes** - Data integrity
   - Mitigation: Backup before changes, validation scripts
3. **API Client Replacement** - External integrations
   - Mitigation: Parallel implementation, feature flags

### **Monitoring & Alerts**
- Performance regression detection
- Error rate monitoring
- Memory usage alerts
- Database query performance tracking

## Detailed Implementation Guide

### **Phase 1 Implementation Steps**

#### **Day 1: Transaction Safety**

**Step 1.1: Update OrderProcessingService**
```javascript
// File: app/services/order/OrderProcessingService.js
// Lines to modify: 78-80, 163-165

// Add transaction wrapper method
async processOrderWithTransaction(orderData, shopDomain, options = {}) {
  return await this.prisma.$transaction(async (tx) => {
    const normalizedShopDomain = normalizeShopDomain(shopDomain);

    // Check if already processed (with transaction)
    const existingWebhook = await this.webhookRepository.findProcessed(
      normalizedShopDomain,
      orderData.topic,
      orderData.webhookId,
      { tx }
    );

    if (existingWebhook) {
      throw new BusinessLogicError('Order already processed');
    }

    // Process order
    const result = await this.processOrderInternal(orderData, normalizedShopDomain, { tx });

    // Mark as processed
    await this.webhookRepository.markProcessed(
      normalizedShopDomain,
      orderData.topic,
      orderData.webhookId,
      orderData.orderId,
      { tx }
    );

    return result;
  }, {
    maxWait: 30000, // 30 seconds
    timeout: 60000, // 1 minute
  });
}
```

**Step 1.2: Update WebhookRepository**
```javascript
// File: app/repositories/WebhookRepository.js
// Add transaction support

async markProcessed(shop, topic, webhookId, orderId, options = {}) {
  const { tx = this.prisma } = options;

  return await tx.processedWebhook.create({
    data: {
      shop: normalizeShopDomain(shop),
      topic,
      webhookId,
      orderId: normalizeOrderId(orderId),
      processedAt: new Date(),
    },
  });
}

async findProcessed(shop, topic, webhookId, options = {}) {
  const { tx = this.prisma } = options;

  return await tx.processedWebhook.findFirst({
    where: {
      shop: normalizeShopDomain(shop),
      topic,
      webhookId,
    },
  });
}
```

#### **Day 2: Input Validation Framework**

**Step 2.1: Create Validation Schemas**
```javascript
// File: app/lib/validation/schemas.js
import { z } from 'zod';

export const webhookPayloadSchema = z.object({
  topic: z.string().min(1),
  shop: z.string().min(1),
  webhookId: z.string().min(1),
  payload: z.object({
    id: z.union([z.string(), z.number()]),
    name: z.string().optional(),
    email: z.string().email().optional(),
    line_items: z.array(z.object({
      id: z.union([z.string(), z.number()]),
      variant_id: z.union([z.string(), z.number()]).optional(),
      sku: z.string().optional(),
      title: z.string(),
      quantity: z.number().min(1),
      price: z.string(),
    })).optional(),
  }).passthrough(),
});

export const shipStationWebhookSchema = z.object({
  resource_url: z.string().url(),
  resource_type: z.enum(['SHIP_NOTIFY', 'ITEM_ORDER_NOTIFY']),
});

export const reconciliationRequestSchema = z.object({
  type: z.enum(['scheduled', 'missing-orders', 'shipstation']),
  shopDomain: z.string().min(1),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  options: z.object({
    batchSize: z.number().min(1).max(1000).default(50),
    forceReprocess: z.boolean().default(false),
    dryRun: z.boolean().default(false),
  }).optional(),
});
```

**Step 2.2: Create Validation Middleware**
```javascript
// File: app/middleware/validation.js
import { z } from 'zod';
import { ApiResponseDTO } from '../dto/common/ApiResponseDTO.js';

export function validateRequest(schema) {
  return async (request) => {
    try {
      const body = await request.json();
      const validatedData = schema.parse(body);
      return { success: true, data: validatedData };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          success: false,
          error: ApiResponseDTO.validationError(
            'Invalid request data',
            error.errors
          ),
        };
      }
      throw error;
    }
  };
}

export function validateHeaders(requiredHeaders) {
  return (request) => {
    const headers = Object.fromEntries(request.headers.entries());
    const missing = requiredHeaders.filter(header => !headers[header]);

    if (missing.length > 0) {
      return {
        success: false,
        error: ApiResponseDTO.validationError(
          `Missing required headers: ${missing.join(', ')}`
        ),
      };
    }

    return { success: true, headers };
  };
}
```

#### **Day 3: Memory Management**

**Step 3.1: Enhanced Memory Monitoring**
```javascript
// File: shared/utils/memoryManager.js
export class MemoryManager {
  constructor(options = {}) {
    this.warningThreshold = options.warningThreshold || 0.8;
    this.criticalThreshold = options.criticalThreshold || 0.9;
    this.gcInterval = options.gcInterval || 1000;
    this.lastGC = 0;
  }

  checkMemoryUsage() {
    const usage = process.memoryUsage();
    const heapUsedRatio = usage.heapUsed / usage.heapTotal;

    return {
      heapUsedRatio,
      isWarning: heapUsedRatio > this.warningThreshold,
      isCritical: heapUsedRatio > this.criticalThreshold,
      usage,
    };
  }

  async forceGarbageCollection() {
    const now = Date.now();
    if (now - this.lastGC < this.gcInterval) {
      return false; // Too soon since last GC
    }

    if (global.gc) {
      global.gc();
      this.lastGC = now;
      return true;
    }

    return false;
  }

  async waitForMemoryRelief(maxWaitMs = 5000) {
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitMs) {
      const { isCritical } = this.checkMemoryUsage();
      if (!isCritical) {
        return true;
      }

      await this.forceGarbageCollection();
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    return false; // Timeout reached
  }
}

export const memoryManager = new MemoryManager();
```

### **Phase 2 Implementation Steps**

#### **Day 5: Legacy Code Removal**

**Step 5.1: Identify Dependencies**
```bash
# Script to find all imports of legacy files
grep -r "from.*legacy" app/ express-server/ shared/
grep -r "import.*legacy" app/ express-server/ shared/
grep -r "require.*legacy" app/ express-server/ shared/
```

**Step 5.2: Create Migration Map**
```javascript
// File: scripts/migration/legacy-migration-map.js
export const legacyMigrationMap = {
  'legacy/order-processing.js': {
    replacement: 'app/services/order/OrderProcessingService.js',
    functions: {
      'processOrder': 'OrderProcessingService.processOrder',
      'calculateBalance': 'BalanceCalculator.calculate',
      'validateSKU': 'SKUValidator.validate',
    },
  },
  'legacy/reconciliation.js': {
    replacement: 'app/services/reconciliation/ReconciliationOrchestrator.js',
    functions: {
      'runReconciliation': 'ReconciliationOrchestrator.execute',
      'generateReport': 'ReconciliationReporter.generate',
    },
  },
  'legacy/shipstation-api.js': {
    replacement: 'app/lib/api/shipstation/ShipStationClient.js',
    functions: {
      'getShipments': 'ShipStationClient.getShipments',
      'getLabels': 'ShipStationClient.getLabels',
    },
  },
};
```

#### **Day 8: Service Standardization**

**Step 8.1: Break Circular Dependencies**
```javascript
// File: app/lib/events/EventBus.js
export class EventBus {
  constructor() {
    this.listeners = new Map();
  }

  on(event, listener) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(listener);
  }

  emit(event, data) {
    const listeners = this.listeners.get(event) || [];
    listeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error(`Error in event listener for ${event}:`, error);
      }
    });
  }
}

export const eventBus = new EventBus();
```

**Step 8.2: Update OrderProcessingService**
```javascript
// File: app/services/order/OrderProcessingService.js
// Remove direct import of ReconciliationOrchestrator

import { eventBus } from '../../lib/events/EventBus.js';

export class OrderProcessingService {
  async processOrder(orderData, shopDomain) {
    const result = await this.processOrderWithTransaction(orderData, shopDomain);

    // Emit event instead of direct call
    eventBus.emit('order.processed', {
      orderId: result.orderId,
      shopDomain,
      result,
      timestamp: new Date(),
    });

    return result;
  }
}
```

### **Phase 3 Implementation Steps**

#### **Day 15: Database Optimization**

**Step 15.1: Add Composite Indexes**
```prisma
// File: prisma/schema.prisma
model InvoiceBalance {
  id       Int     @id @default(autoincrement())
  shop     String
  month    Int
  year     Int
  category String
  quantity Int
  balance  Decimal
  groupId  Int?

  transactions InvoiceTransaction[]
  group        BalanceGroup?        @relation(fields: [groupId], references: [id])

  @@unique([shop, category, month, year])
  @@index([shop, month, year]) // Common date range queries
  @@index([shop, category]) // Category-specific queries
  @@index([groupId]) // Group-based queries
  @@index([shop, month, year, category]) // Full composite for complex queries
}

model ShippingCost {
  id          Int      @id @default(autoincrement())
  shop        String
  storeId     String
  month       Int
  year        Int
  amount      Decimal
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  transactions ShippingTransaction[]

  @@index([shop, month, year]) // Date range queries
  @@index([storeId, month, year]) // Store-specific queries
  @@index([shop, storeId]) // Shop-store combination
}
```

#### **Day 18: API Rate Limiting**

**Step 18.1: Enhanced ShopifyClient**
```javascript
// File: app/lib/api/shopify/ShopifyClient.js
export class ShopifyClient extends ApiClient {
  constructor(shop, accessToken) {
    super(`https://${shop}.myshopify.com/admin/api/2025-04`);
    this.accessToken = accessToken;
    this.rateLimiter = new RateLimiter({
      maxRequests: 40, // Shopify REST API limit
      windowMs: 1000,
    });
  }

  async makeRequest(config) {
    await this.rateLimiter.waitForSlot();

    try {
      const response = await this.client.request({
        ...config,
        headers: {
          'X-Shopify-Access-Token': this.accessToken,
          ...config.headers,
        },
      });

      // Check rate limit headers
      const remaining = parseInt(response.headers['x-shopify-shop-api-call-limit']?.split('/')[0] || '40');
      if (remaining < 5) {
        // Slow down if approaching limit
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      return response;
    } catch (error) {
      if (error.response?.status === 429) {
        const retryAfter = parseInt(error.response.headers['retry-after'] || '2');
        await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
        return this.makeRequest(config); // Retry
      }
      throw error;
    }
  }
}
```

### **Quality Gates & Validation**

#### **Automated Checks**
```javascript
// File: scripts/quality-gates.js
export async function runQualityGates() {
  const results = {
    circularDependencies: await checkCircularDependencies(),
    codeComplexity: await checkCodeComplexity(),
    testCoverage: await checkTestCoverage(),
    securityVulnerabilities: await checkSecurity(),
    performanceRegression: await checkPerformance(),
  };

  const failed = Object.entries(results).filter(([_, passed]) => !passed);

  if (failed.length > 0) {
    console.error('Quality gates failed:', failed.map(([gate]) => gate));
    process.exit(1);
  }

  console.log('All quality gates passed ✅');
}
```

---

**Total Estimated Time:** 140-180 hours over 6 weeks
**Team Size:** 1 developer (can be parallelized with 2-3 developers)
**Risk Level:** Medium (well-planned with rollback strategies)

## Summary of Added Tasks

The following critical tasks have been integrated into the Architecture Reconciliation Plan:

### **✅ Newly Added (High Priority)**
- **Find Issue causing dropped orders** → Phase 4.3 (Comprehensive audit and tracking)
- **Add error checking for no default price** → Phase 4.4 (Price validation)
- **Make sure all webhook subscriptions are properly consumed** → Phase 5.1 (Webhook audit)
- **Modify retry unprocessables to run only on product update** → Phase 5.1 (Filtered retry logic)
- **Implement global error boundary** → Phase 4.2 (Error handling)
- **Ensure consistent loading states** → Phase 4.2 (Loading state components)
- **Implement iFrame protection** → Phase 5.2 (Security headers)
- **Improve Pack Handling** → Phase 5.3 (Enhanced pack detection)
- **Structured Logging** → Phase 5.5 (File-based logging)
- **Add monitoring & health checks** → Phase 6.1 (Comprehensive monitoring)
- **Implement Caching for external APIs** → Phase 6.2 (API response caching)
- **Create optimized GraphQL query abstraction** → Phase 6.3 (Query optimization)
- **Improve webhook deduplication** → Phase 5.1 (Enhanced deduplication)
- **Implement database query optimization** → Phase 6.3 (Index optimization)
- **Implement CI/CD pipeline** → Phase 6.4 (Automated deployment)
- **Setup Auto-healing for crashes** → Phase 6.4 (Self-recovery)

### **📋 Tasks for Future Planning**
- **Create Plan for Inventory Tracking** → Phase 5.4 (Implementation plan created)
- **Increase test coverage** → Integrated throughout all phases
- **Request SanMar Access** → External dependency (not in technical plan)
- **Migrate Hosting to AWS** → Separate infrastructure project
- **Map remote database to local** → Operational procedure (not code change)

### **🔄 Already Covered in Original Plan**
- **Abstract Batch Processing** → Phase 2.1 (processLargeDataset.js consolidation)
- **Create Batch Processing for Large Order Volumes** → Phase 3.2 (Already implemented)
- **Abstract API Rate Limiting** → Phase 3.2 (Already in API client abstraction)

## Next Steps

1. **Review and approve this plan**
2. **Set up development environment** (PostgreSQL, ngrok)
3. **Create feature branch** for Phase 1 implementation
4. **Begin with critical fixes** (transaction safety, input validation)
5. **Implement quality gates** and monitoring
6. **Execute phases incrementally** with testing at each step
