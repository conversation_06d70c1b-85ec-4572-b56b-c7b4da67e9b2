# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
build/
dist/
.cache/

# Environment files
.env*
!.env.example

# Database files
prisma/dev.sqlite*
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.local
.env.development.local
.env.test.local
.env.production.local

# Shopify CLI files
.shopify/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
CHANGELOG.md
docs/
planning-files/

# Test files
tests/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Local development scripts
local-testing-scripts/
scripts/

# Temporary files
tmp/
temp/

# Package manager files
yarn.lock
pnpm-lock.yaml

# TypeScript
*.tsbuildinfo

# Prisma
# prisma/migrations/ - REMOVED: migrations are needed for production deployment

# Shopify app files
extensions/
shopify.app.*.toml
shopify.web.toml

# Keep deployment files for multi-process container
!start.sh
!infrastructure/scripts/health-check.sh
!infrastructure/scripts/deploy.sh

# Misc
.turbo/
.next/
.nuxt/
.output/
.vercel/
