import csv

total_carrier_fee = 0
total_shipments = 0
draft_order_carrier_fee = 0
draft_order_shipments = 0
regular_carrier_fee = 0
regular_shipments = 0

with open('/home/<USER>/workspace/shopify-apps/americans-united-inc/test-data/atp-shipments-5-25.csv', 'r') as file:
    reader = csv.DictReader(file)
    for row in reader:
        carrier_fee = float(row['Carrier - Fee'])
        order_source = row['Order - Source']
        
        total_carrier_fee += carrier_fee
        total_shipments += 1
        
        if order_source == 'shopify_draft_order':
            draft_order_carrier_fee += carrier_fee
            draft_order_shipments += 1
        else:
            regular_carrier_fee += carrier_fee
            regular_shipments += 1

print("=== ANALYSIS OF DRAFT ORDERS ===")
print(f"Total shipments in CSV: {total_shipments}")
print(f"Total carrier fees in CSV: ${total_carrier_fee:.2f}")
print()
print(f"Draft order shipments: {draft_order_shipments}")
print(f"Draft order carrier fees: ${draft_order_carrier_fee:.2f}")
print()
print(f"Regular shipments: {regular_shipments}")
print(f"Regular carrier fees: ${regular_carrier_fee:.2f}")
print()

# Calculate expected totals if draft orders are excluded
regular_markup = regular_carrier_fee * 0.10
regular_with_markup = regular_carrier_fee + regular_markup
regular_fulfillment = regular_shipments * 1.50
regular_total = regular_with_markup + regular_fulfillment

print("=== IF DRAFT ORDERS ARE EXCLUDED ===")
print(f"Regular carrier fees: ${regular_carrier_fee:.2f}")
print(f"10% markup: ${regular_markup:.2f}")
print(f"Total with markup: ${regular_with_markup:.2f}")
print(f"Fulfillment costs ({regular_shipments} x $1.50): ${regular_fulfillment:.2f}")
print(f"Expected total: ${regular_total:.2f}")
print()

# Calculate full totals including draft orders
full_markup = total_carrier_fee * 0.10
full_with_markup = total_carrier_fee + full_markup
full_fulfillment = total_shipments * 1.50
full_total = full_with_markup + full_fulfillment

print("=== IF ALL SHIPMENTS ARE INCLUDED ===")
print(f"All carrier fees: ${total_carrier_fee:.2f}")
print(f"10% markup: ${full_markup:.2f}")
print(f"Total with markup: ${full_with_markup:.2f}")
print(f"Fulfillment costs ({total_shipments} x $1.50): ${full_fulfillment:.2f}")
print(f"Expected total: ${full_total:.2f}")
print()

print(f"Difference between including/excluding draft orders: ${full_total - regular_total:.2f}")
print(f"You reported seeing: $11,190.89")
print(f"Difference from regular shipments only: ${11190.89 - regular_total:.2f}")
print(f"Difference from all shipments: ${11190.89 - full_total:.2f}")
